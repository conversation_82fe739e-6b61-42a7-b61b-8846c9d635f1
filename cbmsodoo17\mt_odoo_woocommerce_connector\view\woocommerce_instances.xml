<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="view_wooc_instance_form" model="ir.ui.view">
        <field name="name">wooc.instance.form</field>
        <field name="model">woocommerce.instance</field>
        <field name="arch" type="xml">
            <form string="Configure Instance">
                <header>
                    <button string='Authenticate' type="object" name="connection_authenticate" class="oe_highlight"
                        icon="fa-plug" />
                </header>
                <div class="alert alert-danger" role="alert" style="height: 40px; margin-bottom:0px;" invisible = "is_authenticated == True">
                    <strong>The connection need to Authenticate!!!</strong>
                </div>

                <sheet>
                    <group>
                        <group>
                            <field name="name" />
                            <field name="shop_url" />
                            <field name="wooc_consumer_key" password="True" />
                            <field name="wooc_consumer_secret" password="True" />
                            <field name="wooc_api_version" />
                            <field name="is_authenticated" invisible="1"/>
                            <field name='wooc_company_id' required="1"
                                options="{'no_open':True,'no_create':True,'no_create_edit':True}"
                                string="Company" />
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="view_wooc_instance_tree" model="ir.ui.view">
        <field name="name">wooc.instance.tree.view</field>
        <field name="model">woocommerce.instance</field>
        <field name="arch" type="xml">
            <tree string="Woo Commerce Instance">
                <field name="name" />
                <field name="wooc_company_id" />
            </tree>
        </field>
    </record>

    <record id="view_wooc_instance_kanban" model="ir.ui.view">
        <field name="name">wooc.instance.kanban</field>
        <field name="model">woocommerce.instance</field>
        <field name="arch" type="xml">
            <kanban class="o_kanban_dashboard o_kanban_wooc" create="false"
                sample="1">
                <field name="name" />
                <templates>
                    <t t-name="kanban-box">
                        <div>
                            <div class="o_kanban_card_header">
                                <div class="o_kanban_card_header_title">
                                    <div class="o_primary">
                                        <t t-esc="record.name.value" />
                                    </div>
                                </div>
                            </div>
                            <div>
                                <group class="dashboard_graph_data">
                                    <field name="wooc_dashboard_graph_data" widget="wooc_dashboard_graph"
                                        graph_type="bar" />
                                </group>
                            </div>
                        </div>
                    </t>
                    <br></br>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="view_wooc_instance_search" model="ir.ui.view">
        <field name="name">wooc.instance.search</field>
        <field name="model">woocommerce.instance</field>
        <field name="arch" type="xml">
            <search>
                <field name="name" />
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]" />
            </search>
        </field>
    </record>

    <record id="action_wooc_instance_list" model="ir.actions.act_window">
        <field name="name">Woo Commerce Instances</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">woocommerce.instance</field>
        <field name="view_id" ref="view_wooc_instance_tree" />
        <field name="view_mode">tree,form</field>
        <field name="target">current</field>
    </record>

    <record id="action_wooc_dashboard" model="ir.actions.act_window">
        <field name="name">Woo Commerce Dashboard</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">woocommerce.instance</field>
        <field name="view_id" ref="view_wooc_instance_kanban" />
        <field name="view_mode">kanban</field>
        <field name="target">current</field>
    </record>

</odoo>