<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="common_log_line_view_search" model="ir.ui.view">
        <field name="name">common.log.line.search.view</field>
        <field name="model">common.log.lines.ept</field>
        <field name="arch" type="xml">
            <search>
                <field name="message"/>
                <field name="order_ref"/>
                <field name="default_code"/>
                <field name="model_id"/>
                <filter name="filter_write_date" date="write_date" string="Update Date"/>
                <group expand="0" string="Group By...">
                    <filter name="groupby_model" string="Model" icon="terp-personal"
                            context="{'group_by': 'model_id'}"/>
                    <filter name="groupby_write_date" string="Update Date"
                            context="{'group_by': 'write_date'}"/>

                </group>
            </search>
        </field>
    </record>

    <record id="common_log_line_view_tree" model="ir.ui.view">
        <field name="name">common.log.line.tree.view</field>
        <field name="model">common.log.lines.ept</field>
        <field name="arch" type="xml">
            <tree create="false" default_order="write_date desc" sample="1">
                <field name="order_ref"/>
                <field name="default_code"/>
                <field name="message"/>
                <field name="model_id"/>
                <field name="write_date"/>
            </tree>
        </field>
    </record>

    <record id="common_log_line_view_form" model="ir.ui.view">
        <field name="name">common.log.line.form.view</field>
        <field name="model">common.log.lines.ept</field>
        <field name="arch" type="xml">
            <form create="false" edit="false">
                <sheet>
                    <h3>
                        <field name="message" readonly="1"/>
                    </h3>
                    <group>
                        <group>
                            <field name="model_id" readonly="1"/>
                            <field name="write_date"/>
                        </group>
                        <group>
                            <field name="order_ref"/>
                            <field name="default_code"/>
                        </group>
                    </group>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids" widget="mail_followers"
                           groups="base.group_user"/>
                    <field name="message_ids" widget="mail_thread"/>
                    <field name="activity_ids"/>
                </div>
            </form>
        </field>
    </record>

    <record id="action_common_log_line_ept" model="ir.actions.act_window">
        <field name="name">Log Lines</field>
        <field name="res_model">common.log.lines.ept</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No mismatch log lines are there!
            </p>
            <p>If any import/export/update process has got issue, log lines will be shown here.</p>
        </field>
    </record>
</odoo>
