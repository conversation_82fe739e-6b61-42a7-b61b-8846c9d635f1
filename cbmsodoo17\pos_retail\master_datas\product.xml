<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">

        <record id="product_product_credit" model="product.product">
            <field name="name">Credit</field>
            <field name="available_in_pos">True</field>
            <field name="standard_price">0.00</field>
            <field name="list_price">0.00</field>
            <field name="weight">0.00</field>
            <field name="type">consu</field>
            <field name="is_credit">True</field>
            <field name="categ_id" ref="point_of_sale.product_category_pos"/>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="uom_po_id" ref="uom.product_uom_unit"/>
            <field name="default_code">CREDIT</field>
            <field name="purchase_ok">False</field>
        </record>

    </data>
</odoo>
