# Translation of Odoo Server.
# This file contains the translation of the following modules:
#	* hotel_restaurant
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-05-21 05:17+0000\n"
"PO-Revision-Date: 2020-05-21 05:17+0000\n"
"Last-Translator: <>\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__bom_count
msgid "# Bill of Material"
msgstr "Ведомостей материалов"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__used_in_bom_count
msgid "# BoM Where Used"
msgstr "# Ведомостей использовано"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__product_variant_count
msgid "# Product Variants"
msgstr "Варианты продукции"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__product_count
msgid "# Products"
msgstr "# Продукты"

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.hotel_restaurant_reservation_report123
msgid "<strong>Customer Name</strong>"
msgstr "<strong>Имя покупателя</strong>"

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.hotel_restaurant_reservation_report123
msgid "<strong>End Date</strong>"
msgstr "<strong>Дата окончания</strong>""

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.hotel_restaurant_reservation_report123
msgid "<strong>Reservation No</strong>"
msgstr "<strong>№ Бронирования o</strong>"

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.hotel_restaurant_reservation_report123
msgid "<strong>Start Date</strong>"
msgstr "<strong>Дата начала</strong>"

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__description_sale
msgid "A description of the Product that you want to communicate to your customers. This description will be copied to every Sales Order, Delivery Order and Customer Invoice/Credit Note"
msgstr "Описание продукта, которое Вы хотели бы предоставлять вашим потребителям.Это описание будет отображаться в каждом заказе покупателя, заказе на доставку и счете покупателю/ кредитном обязательстве"

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__type
msgid "A storable product is a product for which you manage stock. The Inventory app has to be installed.\n"
"A consumable product is a product for which stock is not managed.\n"
"A service is a non-material product you provide."
msgstr "Товар сохраняется - это товар, которым вы управляете на складе. Необходимо установить приложение Состав. Затратный товар - это товар, для которого состав не руководствуется. Услуга - это нематериальный товар, который вы предоставляете."

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_menucard_type_form
msgid "Account Properties"
msgstr "Параметры счёта"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__message_needaction
msgid "Action Needed"
msgstr "Требует внимания"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__active
msgid "Active"
msgstr "Активно"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__activity_ids
msgid "Activities"
msgstr "Деятельность"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__activity_state
msgid "Activity State"
msgstr "Этап действия"

#. module: hotel_restaurant
#: model:ir.model,name:hotel_restaurant.model_hotel_restaurant_kitchen_order_tickets
msgid "Add BOM to restaurant module"
msgstr "Добавьте BOM в ресторанный модуль "

#. module: hotel_restaurant
#: model:ir.model,name:hotel_restaurant.model_hotel_restaurant_order_list
msgid "Add restaurant inventory"
msgstr "Добавить инвентарь ресторана"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__message_attachment_count
msgid "Attachment Count"
msgstr "Количество вложений"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__attribute_value_ids
msgid "Attribute Values"
msgstr "Значение атрибута"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__available_in_pos
msgid "Available in POS"
msgstr "Доступен в ТП"

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__produce_delay
msgid "Average lead time in days to manufacture this product. In the case of multi-level BOM, the manufacturing lead times of the components will be added."
msgstr "Среднее время в днях приведения в производство этого товара. В случае многоуровневой спецификации будет добавлено изготовлении компонентов."

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__variant_bom_ids
msgid "BOM Product Variants"
msgstr "Варианты спецификации товара"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__barcode
msgid "Barcode"
msgstr "Штрих-код"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__image
msgid "Big-sized image"
msgstr "Изображение большого размера"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__bom_ids
msgid "Bill of Materials"
msgstr "Ведомость материалов"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__bom_line_ids
msgid "BoM Components"
msgstr "Компоненты ВМ"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__purchase_ok
msgid "Can be Purchased"
msgstr "Можно закупать"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__rental
msgid "Can be Rent"
msgstr "Может быть аренда"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__sale_ok
msgid "Can be Sold"
msgstr "Можно продавать"

#. module: hotel_restaurant
#: selection:hotel.restaurant.tables,state:0
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.hotel_restaurant_reservation_wizard_view
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.hotel_wizard_view
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_restaurant_reservation_form
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_restaurant_tables_form
msgid "Cancel"
msgstr "Отменить"

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_restaurant_order_form
msgid "Cancel Order"
msgstr "Отменить заказ"

#. module: hotel_restaurant
#: selection:hotel.reservation.order,state:0
#: selection:hotel.restaurant.order,state:0
#: selection:hotel.restaurant.reservation,state:0
msgid "Cancelled"
msgstr "Отменено"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_tables__capacity
msgid "Capacity"
msgstr "Вместимость"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__menu_id
msgid "Category"
msgstr "Категория"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__route_from_categ_ids
msgid "Category Routes"
msgstr "Категории технологических карт"

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__pos_categ_id
msgid "Category used in the Point of Sale."
msgstr "Категория используется в точке продажи."

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__to_weight
msgid "Check if the product should be weighted using the hardware scale integration."
msgstr "Проверьте, следует взвешивать продукт с помощью интеграции устройства взвешивания."

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__available_in_pos
msgid "Check if you want this product to appear in the Point of Sale."
msgstr "Проверьте, хотите вы, чтобы этот товар появился в точке продажи."

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__child_id
msgid "Child Categories"
msgstr "Подчиненные категории"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__color
msgid "Color Index"
msgstr "Цветовая палитра"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__company_id
msgid "Company"
msgstr "Компания"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__complete_name
msgid "Complete Name"
msgstr "Полное название"

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_restaurant_reservation_form
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_restaurant_tables_form
msgid "Confirm"
msgstr "Подтвердить"

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_reservation_order_form
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_restaurant_order_form
msgid "Confirm Order"
msgstr "подтвердить заказ"

#. module: hotel_restaurant
#: selection:hotel.reservation.order,state:0
#: selection:hotel.restaurant.order,state:0
#: selection:hotel.restaurant.reservation,state:0
#: selection:hotel.restaurant.tables,state:0
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_restaurant_order_filter
msgid "Confirmed"
msgstr "Подтверждено"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__standard_price
msgid "Cost"
msgstr "Стоимость"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__cost_currency_id
msgid "Cost Currency"
msgstr "Валюта стоимости"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__cost_method
msgid "Cost Method"
msgstr "метод стоимости"

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__standard_price
msgid "Cost used for stock valuation in standard price and as a first price to set in average/fifo. Also used as a base price for pricelists. Expressed in the default unit of measure of the product."
msgstr "Стоимость используется для складской оценки стоимости по стандартной цене и как первая цена устанавливается в среднем / FIFO. Также используется как базовая цена для ПРАЙСЛИСТ. Выражается в единице измерения товара по умолчанию."

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__property_cost_method
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__property_cost_method
msgid "Costing Method"
msgstr "Метод ценообразования"

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_reservation_order_form
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_restaurant_order_form
msgid "Create Invoice"
msgstr "Создание Счета"

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.hotel_wizard_view
msgid "Create Kots"
msgstr ""

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_restaurant_reservation_form
msgid "Create Order"
msgstr "Создать Заказ"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__create_uid
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__create_uid
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_reservation_order__create_uid
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_kitchen_order_tickets__create_uid
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order__create_uid
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order_list__create_uid
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation__create_uid
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation_wizard__create_uid
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_tables__create_uid
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_wizard__create_uid
msgid "Created by"
msgstr "Создано"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__create_date
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__create_date
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_reservation_order__create_date
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_kitchen_order_tickets__create_date
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order__create_date
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order_list__create_date
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation__create_date
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation_wizard__create_date
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_tables__create_date
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_wizard__create_date
msgid "Created on"
msgstr "Создан"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__currency_id
msgid "Currency"
msgstr "Валюта"

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__qty_available
msgid "Current quantity of products.\n"
"In a context with a single Stock Location, this includes goods stored at this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"stored in the Stock Location of the Warehouse of this Shop, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr "Текущее количество продукции.\n"
"В контексте Единого Места хранения, включает в себя товары, хранящиеся в этом месте, или любом из его детей.\n"
"В контексте единого Склада, это включает в себя товары, хранящиеся в Месте хранения этого Склад, или в любом из его детей.\n"
"хранится в Месте Хранения склада этого магазина, или любого из его детей.\n"
"В противном случае, включает в себя товары, хранящиеся в любом Месте Хранения «внутреннего» типа."

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_reservation_order__partner_id
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order__partner_id
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_reservation_order_filter
msgid "Customer"
msgstr "Заказчик"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__sale_delay
msgid "Customer Lead Time"
msgstr "Срок поставки заказчика"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation__cname
msgid "Customer Name"
msgstr "Название заказчика"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__partner_ref
msgid "Customer Ref"
msgstr "Справка клиента"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__taxes_id
msgid "Customer Taxes"
msgstr "Налоги с покупателя"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_reservation_order__date1
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_kitchen_order_tickets__kot_date
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order__o_date
msgid "Date"
msgstr "Дата"

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__supplier_taxes_id
msgid "Default taxes used when buying the product."
msgstr "Типичные налоги применяются при покупке товара."

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__taxes_id
msgid "Default taxes used when selling the product."
msgstr "Типичные налоги применяются при продаже товара."

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__uom_id
msgid "Default unit of measure used for all stock operations."
msgstr "Стандартная единица измерения, используемое для всех складских операций."

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__uom_po_id
msgid "Default unit of measure used for purchase orders. It must be in the same category as the default unit of measure."
msgstr "Единица измерения по умолчанию, используемый для заказов на покупку. Она должна быть в той же категории, что и единица измерения по умолчанию."

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__seller_ids
msgid "Define vendor pricelists."
msgstr "Определите прайс-лист поставщика."

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__sale_delay
msgid "Delivery lead time, in days. It's the number of days, promised to the customer, between the confirmation of the sales order and the delivery."
msgstr "Срок выполнения доставки в днях. Это количество дней, обещанная заказчику, между подтверждением заказа на продажу и доставкой."

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__route_ids
msgid "Depending on the modules installed, this will allow you to define the route of the product: whether it will be bought, manufactured, MTO, etc."
msgstr "В зависимости от установленных модулей, это позволит вам определить маршрут товара: будет ли он покупаться, изготавливаться на заказ и т. Д."

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__description
msgid "Description"
msgstr "Описание"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__description_pickingout
msgid "Description on Delivery Orders"
msgstr "Описание на заказах на доставку"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__description_picking
msgid "Description on Picking"
msgstr "Описание по Комлектации"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__description_pickingin
msgid "Description on Receptions"
msgstr "Описание на приемах"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__display_name
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__display_name
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_reservation_order__display_name
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_kitchen_order_tickets__display_name
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order__display_name
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order_list__display_name
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation__display_name
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation_wizard__display_name
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_tables__display_name
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_wizard__display_name
#: model:ir.model.fields,field_description:hotel_restaurant.field_report_hotel_restaurant_hotel_restaurant_reservation_report123__display_name
msgid "Display Name"
msgstr "Отображаемое Имя"

#. module: hotel_restaurant
#: selection:hotel.reservation.order,state:0
#: selection:hotel.restaurant.order,state:0
#: selection:hotel.restaurant.reservation,state:0
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_restaurant_reservation_form
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_restaurant_order_filter
msgid "Done"
msgstr "Сделано"

#. module: hotel_restaurant
#: selection:hotel.reservation.order,state:0
#: selection:hotel.restaurant.order,state:0
#: selection:hotel.restaurant.reservation,state:0
#: selection:hotel.restaurant.tables,state:0
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_reservation_order_filter
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_restaurant_order_filter
msgid "Draft"
msgstr "Черновик"

#. module: hotel_restaurant
#: selection:hotel.restaurant.tables,state:0
msgid "Edit"
msgstr "Редактировать"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation__end_date
msgid "End Date"
msgstr "Дата окончания"

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr "Обеспечение отслеживания товара, хранящегося на вашем складе."

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__property_account_expense_id
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__property_account_expense_categ_id
msgid "Expense Account"
msgstr "Счет расходов"

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__expense_policy
msgid "Expenses and vendor bills can be re-invoiced to a customer.With this option, a validated expense can be re-invoice to a customer at its cost or sales price."
msgstr "Расходы и счета поставщиков могут быть повторно выставлены в счете клиенту. При таком варианте проверка расходов может быть повторно выставлена в счете клиенту по себестоимости или цене продажи."

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.hotel_restaurant_reservation_wizard_view
msgid "Fill The Dates"
msgstr "Заполните Дату"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__message_follower_ids
msgid "Followers"
msgstr "Подписчики"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__message_channel_ids
msgid "Followers (Channels)"
msgstr "Подписчики (Каналы)"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__message_partner_ids
msgid "Followers (Partners)"
msgstr "Подписчики (Партнеры)"

#. module: hotel_restaurant
#: model:ir.ui.menu,name:hotel_restaurant.menu_open_hotel_menucard_form
msgid "Food Items"
msgstr "Продовольственные товары"

#. module: hotel_restaurant
#: model:ir.ui.menu,name:hotel_restaurant.menu_action_hotel_menucard_type_view_form
msgid "FoodItem Categories"
msgstr "Категории Продуктов Питания"

#. module: hotel_restaurant
#: model:ir.ui.menu,name:hotel_restaurant.menu_action_hotel_menucard_type_view_form_parent
msgid "FoodItem Definations"
msgstr "Определения Продуктов Питания"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__removal_strategy_id
msgid "Force Removal Strategy"
msgstr "Стратегия принудительного удаления"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__virtual_available
msgid "Forecast Quantity"
msgstr "Прогнозируемое Количество"

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__virtual_available
msgid "Forecast quantity (computed as Quantity On Hand - Outgoing + Incoming)\n"
"In a context with a single Stock Location, this includes goods stored in this location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr "Прогноз количества (рассчитывается как Количество в наличии - исходящее + входящее)\n"
"В контексте с одним Складским Местом хранения, сюда входят товары, хранящиеся в этом месте, или какого-либо из его дочерних.\n"
"В контексте с одного Склада, это включает в себя товары, хранящиеся в Месте хранения Склада, или любого из его дочерних.\n"
"В противном случае, это включает в себя товары, хранящиеся на любых Местах хранения с типом \"внутренний\"."

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation_wizard__date_start
msgid "From Date"
msgstr "С даты"

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_reservation_order_form
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_restaurant_order_form
msgid "Generate KOT"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__packaging_ids
msgid "Gives the different ways to package the same product."
msgstr "Предоставляет различные варианты упаковки одного и того же продукта."

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__sequence
msgid "Gives the sequence order when displaying a product list"
msgstr "Определяет порядок следования при отображении списка товаров"

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_reservation_order_filter
msgid "Group By..."
msgstr "Группировать…"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_wizard__grouped
msgid "Group the kots"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_reservation_order__guest_name
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order__guest_name
msgid "Guest Name"
msgstr "Имя гостя"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__hide_expense_policy
msgid "Hide Expense Policy"
msgstr "Скрыть политику расходов"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation__folio_id
msgid "Hotel Folio"
msgstr "Счет отеля"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_reservation_order__folio_id
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order__folio_id
msgid "Hotel Folio Ref"
msgstr ""

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_menucard_type_form
msgid "Hotel Food Items Type"
msgstr ""

#. module: hotel_restaurant
#: model:ir.actions.act_window,name:hotel_restaurant.action_hotel_menucard_type_view_form
msgid "Hotel FoodItem Type"
msgstr "Тип Продукта Питания В Гостинице"

#. module: hotel_restaurant
#: model:ir.actions.act_window,name:hotel_restaurant.action_hotel_menucard_form
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_menucard_tree
msgid "Hotel Menucard"
msgstr "Карта меню отеля"

#. module: hotel_restaurant
#: model:ir.model,name:hotel_restaurant.model_hotel_menucard
msgid "Hotel Menucard Inherit Adding Point_of_sale category"
msgstr ""

#. module: hotel_restaurant
#: model:ir.actions.report,name:hotel_restaurant.hotel_restaurant_reservation_report1
msgid "Hotel Restaurant Reservation Report"
msgstr "Отчет О Бронировании Гостиничного Ресторана"

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.hotel_restaurant_reservation_wizard_view
msgid "Hotel Restaurant Wizard"
msgstr ""

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.hotel_wizard_view
msgid "Hotel Wizard"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__id
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__id
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_reservation_order__id
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_kitchen_order_tickets__id
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order__id
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order_list__id
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation__id
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation_wizard__id
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_tables__id
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_wizard__id
#: model:ir.model.fields,field_description:hotel_restaurant.field_report_hotel_restaurant_hotel_restaurant_reservation_report123__id
msgid "ID"
msgstr "Номер"

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__message_unread
msgid "If checked new messages require your attention."
msgstr "Если отмечено, новые сообщения будут требовать вашего внимания."

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Если отмечено - новые сообщения требуют Вашего внимания."

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Если обозначено, некоторые сообщения имеют ошибку доставки."

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__active
msgid "If unchecked, it will allow you to hide the product without removing it."
msgstr "Если не установлено, это позволит вам скрыть продукт, не удаляя его ."

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__image
msgid "Image of the product variant (Big-sized image of product template if false). It is automatically resized as a 1024x1024px image, with aspect ratio preserved."
msgstr "Изображение варианта товара (Изображение образца товара большого размера, если значение \"ошибочно\"). Его размер изменяется автоматически на изображение 1024x1024 пкс, с сохраненным соотношением сторон."

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__image_medium
msgid "Image of the product variant (Medium-sized image of product template if false)."
msgstr "Изображение варианта товара (Изображение образца товара среднего размера, если значение \"ошибочно\")."

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__image_small
msgid "Image of the product variant (Small-sized image of product template if false)."
msgstr "Изображение варианта товара (Изображение образца товара маленького размера, если значение ошибочно)."

#. module: hotel_restaurant
#: model:ir.model,name:hotel_restaurant.model_hotel_reservation_order
msgid "Includes Hotel Reservation Order"
msgstr "Включает В Себя Заказ Бронирования Отеля"

#. module: hotel_restaurant
#: model:ir.model,name:hotel_restaurant.model_hotel_restaurant_order
msgid "Includes Hotel Restaurant Order"
msgstr "Включает В Себя Заказ Отельного Ресторана"

#. module: hotel_restaurant
#: model:ir.model,name:hotel_restaurant.model_hotel_restaurant_reservation
msgid "Includes Hotel Restaurant Reservation"
msgstr "Включает В Себя Бронирование Гостиничного Ресторана"

#. module: hotel_restaurant
#: model:ir.model,name:hotel_restaurant.model_hotel_restaurant_tables
msgid "Includes Hotel Restaurant Table"
msgstr "Включает В Себя Столик В Ресторане Отеля"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__property_account_income_id
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__property_account_income_categ_id
msgid "Income Account"
msgstr "Cчёт доходов и расходов"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__incoming_qty
msgid "Incoming"
msgstr "Входящие"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__default_code
msgid "Internal Reference"
msgstr "Внутренний артикул"

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__barcode
msgid "International Article Number used for product identification."
msgstr "Международный  номер товара используется для идентификации товара."

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__property_stock_inventory
msgid "Inventory Location"
msgstr "Место инвентаризации"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__property_valuation
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__property_valuation
msgid "Inventory Valuation"
msgstr "Оценка запасов"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__invoice_policy
msgid "Invoicing Policy"
msgstr "Политика выставления счетов"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__isact
msgid "Is Activity"
msgstr "Деятельность"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__isactivitytype
msgid "Is Activity Type"
msgstr "Тип деятельности"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__message_is_follower
msgid "Is Follower"
msgstr "Подписчик"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_housekeeping_activity_type__ismenutype
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__ismenutype
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_room_amenities_type__ismenutype
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_room_type__ismenutype
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_service_type__ismenutype
#: model:ir.model.fields,field_description:hotel_restaurant.field_product_category__ismenutype
msgid "Is Menu Type"
msgstr "Тип меню"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__is_product_variant
msgid "Is Product Variant"
msgstr "Есть вариантом товара"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_h_activity__ismenucard
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__ismenucard
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__isroom
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_room__ismenucard
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_room_amenities__ismenucard
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_services__ismenucard
#: model:ir.model.fields,field_description:hotel_restaurant.field_product_product__ismenucard
msgid "Is Room"
msgstr "Комната"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__isroomtype
msgid "Is Room Type"
msgstr "Тип Комнаты"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__isservicetype
msgid "Is Service Type"
msgstr "Тип службы"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__isservice
msgid "Is Service id"
msgstr "Служебный идентификатор"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__isamenitype
msgid "Is amenities Type"
msgstr "Тип услуг"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__iscategid
msgid "Is categ id"
msgstr ""

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_menucard_tree
msgid "Item Rate"
msgstr ""

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_restaurant_kitchen_order_tickets_form
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_restaurant_kitchen_order_tickets_tree
msgid "KOT List"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_kitchen_order_tickets__orderno
msgid "KOT Number"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__property_account_income_id
msgid "Keep this field empty to use the default value from the product category."
msgstr "Оставьте поле пустым, чтобы использовать значение по умолчанию в категории товара."

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__property_account_expense_id
msgid "Keep this field empty to use the default value from the product category. If anglo-saxon accounting with automated valuation method is configured, the expense account on the product category will be used."
msgstr "Оставьте это поле пустым, чтобы использовать значение по умолчанию из категории товара. Если настроено англосаксонский учет с автоматизированным методом оценки, будут использованы счет расходов на категорию товара."

#. module: hotel_restaurant
#: model:ir.actions.act_window,name:hotel_restaurant.open_view_hotel_restaurant_kitchen_order_tickets_form_tree
msgid "Kitchen Order List"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order_list__kot_order_list
msgid "Kot Order List"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard____last_update
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type____last_update
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_reservation_order____last_update
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_kitchen_order_tickets____last_update
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order____last_update
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order_list____last_update
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation____last_update
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation_wizard____last_update
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_tables____last_update
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_wizard____last_update
#: model:ir.model.fields,field_description:hotel_restaurant.field_report_hotel_restaurant_hotel_restaurant_reservation_report123____last_update
msgid "Last Modified on"
msgstr "Последнее изменение"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__write_uid
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__write_uid
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_reservation_order__write_uid
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_kitchen_order_tickets__write_uid
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order__write_uid
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order_list__write_uid
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation__write_uid
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation_wizard__write_uid
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_tables__write_uid
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_wizard__write_uid
msgid "Last Updated by"
msgstr "Последний раз обновил"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__write_date
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__write_date
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_reservation_order__write_date
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_kitchen_order_tickets__write_date
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order__write_date
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order_list__write_date
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation__write_date
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation_wizard__write_date
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_tables__write_date
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_wizard__write_date
msgid "Last Updated on"
msgstr "Последнее обновление"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__location_id
msgid "Location"
msgstr "Место"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__message_main_attachment_id
msgid "Main Attachment"
msgstr "Основное прикрепления"

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__property_valuation
msgid "Manual: The accounting entries to value the inventory are not posted automatically.\n"
"        Automated: An accounting entry is automatically created to value the inventory when a product enters or leaves the company."
msgstr "Вручную бухгалтерьки записи для оценки состава не публикуются автоматически. Автоматизировано: бухгалтерская запись автоматически создается для оценки состава, когда товар входит или выходит из компании."

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard_type__property_valuation
msgid "Manual: The accounting entries to value the inventory are not posted automatically.\n"
"        Automated: An accounting entry is automatically created to value the inventory when a product enters or leaves the company.\n"
"        "
msgstr "Вручную бухгалтерьки записи для оценки состава не публикуются автоматически. Автоматизировано: бухгалтерская запись автоматически создается для оценки состава, когда товар входит или выходит из компании."

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__service_type
msgid "Manually set quantities on order: Invoice based on the manually entered quantity, without creating an analytic account.\n"
"Timesheets on contract: Invoice based on the tracked hours on the related timesheet.\n"
"Create a task and track hours: Create a task on the sales order validation and track the work hours."
msgstr "Задавайте количество в заказе вручную: счет на основе количества, введенного вручную, без создания аналитического счета.\n"
"Табели учета рабочего времени в контракте: счет на основе записанных часов в соответствующем табеле.\n"
"Создавайте задачи и вносите часы: Создайте задачу при подтверждении заказа и вносите в нее рабочие часы."

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__mrp_product_qty
msgid "Manufactured"
msgstr "произведено"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__produce_delay
msgid "Manufacturing Lead Time"
msgstr "Время производства"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__image_medium
msgid "Medium-sized image"
msgstr "Изображение средних размеров"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__message_has_error
msgid "Message Delivery error"
msgstr "Ошибка доставки сообщения"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__sale_line_warn_msg
msgid "Message for Sales Order Line"
msgstr "Сообщение по позиции заказа продаж"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__message_ids
msgid "Messages"
msgstr "Сообщения"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__orderpoint_ids
msgid "Minimum Stock Rules"
msgstr "Правила минимальных запасов"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__name
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__name
msgid "Name"
msgstr "Название"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Дедлайн следующему шагу"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__activity_summary
msgid "Next Activity Summary"
msgstr "Резюме следующего мероприятия"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__activity_type_id
msgid "Next Activity Type"
msgstr "Тип следующему шагу"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__message_needaction_counter
msgid "Number of Actions"
msgstr "Количество действий"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__message_has_error_counter
msgid "Number of error"
msgstr "количество ошибок"

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Количество сообщений, требующих внимания"

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Количество сообщений с ложной дставкою"

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__message_unread_counter
msgid "Number of unread messages"
msgstr "Количество непрочитанных сообщений"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order_list__o_l
msgid "O L"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order_list__o_list
msgid "O List"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__optional_product_ids
msgid "Optional Products"
msgstr "Дополнительные товары"

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__optional_product_ids
msgid "Optional Products are suggested whenever the customer hits *Add to Cart* (cross-sell strategy, e.g. for computers: warranty, software, etc.)."
msgstr "Дополнительные продукты предлагаются всякий раз, когда клиент попадает *Добавить в корзину * (кросс продажи стратегии, например, для компьютеров: гарантия, программное обеспечение и т.д.)."

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_reservation_order_form
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_reservation_order_tree
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_restaurant_order_form
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_restaurant_order_tree
msgid "Order"
msgstr "Заказ"

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_reservation_order_filter
msgid "Order Date"
msgstr "Дата заказа"

#. module: hotel_restaurant
#: selection:hotel.reservation.order,state:0
#: selection:hotel.restaurant.order,state:0
#: selection:hotel.restaurant.reservation,state:0
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_reservation_order_filter
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_restaurant_order_filter
msgid "Order Done"
msgstr "Заказ Выполнен"

#. module: hotel_restaurant
#: model:ir.actions.act_window,name:hotel_restaurant.open_view_hotel_reservation_order_form_tree
#: model:ir.actions.act_window,name:hotel_restaurant.open_view_hotel_restaurant_order_form_tree1
msgid "Order Generate"
msgstr "Заказ Сформирован"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order_list__order_l
msgid "Order L"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_reservation_order__order_list
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_kitchen_order_tickets__kot_list
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order__order_list
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation__order_list_ids
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_reservation_order_form
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_restaurant_kitchen_order_tickets_form
msgid "Order List"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_reservation_order__order_number
msgid "Order No"
msgstr "№ Заказа"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_kitchen_order_tickets__resno
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order__order_no
msgid "Order Number"
msgstr "№ Заказа"

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_reservation_order_filter
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_restaurant_order_filter
msgid "Order date"
msgstr "Дата Заказа"

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__invoice_policy
msgid "Ordered Quantity: Invoice quantities ordered by the customer.\n"
"Delivered Quantity: Invoice quantities delivered to the customer."
msgstr "Заказанное количество: количество счетов-фактур заказанных клиентом. Доставлена количество: количество счетов, доставленных клиенту."

#. module: hotel_restaurant
#: model:ir.ui.menu,name:hotel_restaurant.menu_open_view_hotel_reservation_order_form_tree
msgid "Orders"
msgstr "Заказы"

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_reservation_order_filter
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_restaurant_order_filter
msgid "Orders that KOT is generated"
msgstr ""

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_reservation_order_filter
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_restaurant_order_filter
msgid "Orders that haven't yet been confirmed"
msgstr "Заказы, которые еще не были подтверждены"

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_reservation_order_filter
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_restaurant_order_filter
msgid "Orders that invoice is generated"
msgstr "Заказы, для которых созданы счета"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__outgoing_qty
msgid "Outgoing"
msgstr "Исходящие"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__parent_id
msgid "Parent Category"
msgstr "Категория предка"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__parent_path
msgid "Parent Path"
msgstr "Родительский путь"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__pos_categ_id
msgid "Point of Sale Category"
msgstr "Категории для точки продаж"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__price
msgid "Price"
msgstr "Цена"

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__list_price
msgid "Price at which the product is sold to customers."
msgstr "Цена, по которой продается товар клиентам."

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__pricelist_id
msgid "Pricelist"
msgstr "Прайс-лист"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__pricelist_item_ids
msgid "Pricelist Item"
msgstr "Пункт прайслиста"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__item_ids
msgid "Pricelist Items"
msgstr "Позиции прайс-листа"

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.hotel_restaurant_reservation_wizard_view
msgid "Print Report"
msgstr "Распечатать Отчет"

#. module: hotel_restaurant
#: model:ir.model,name:hotel_restaurant.model_product_product
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__product_variant_id
msgid "Product"
msgstr "Продукт"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__attribute_line_ids
msgid "Product Attributes"
msgstr "Атрибуты ТМЦ"

#. module: hotel_restaurant
#: model:ir.model,name:hotel_restaurant.model_product_category
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__categ_id
msgid "Product Category"
msgstr "Категория продукта"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__packaging_ids
msgid "Product Packages"
msgstr "Варианты упаковки продукта"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__product_tmpl_id
msgid "Product Template"
msgstr "Шаблон продукта"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__type
msgid "Product Type"
msgstr "Тип продукта"

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_menucard_form
msgid "Product Variant"
msgstr "Вариант продукта"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__product_id
msgid "Product_id"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__property_stock_production
msgid "Production Location"
msgstr "Место производства"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__product_variant_ids
msgid "Products"
msgstr "Продукты"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__description_purchase
msgid "Purchase Description"
msgstr "Описание закупки"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__uom_po_id
msgid "Purchase Unit of Measure"
msgstr "Единицы измерения при закупке"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order_list__item_qty
msgid "Qty"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__qty_at_date
msgid "Quantity"
msgstr "Количество"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__qty_available
msgid "Quantity On Hand"
msgstr "Количество на руках"

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__incoming_qty
msgid "Quantity of planned incoming products.\n"
"In a context with a single Stock Location, this includes goods arriving to this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods arriving to the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods arriving to any Stock Location with 'internal' type."
msgstr "Количество запланированных входящих товаров. В контексте, состоящий из одной позиции на складе, включая товары, поступающие к этому местонахождение, или любых дочерних. В контексте с одним составом это включает товары, поступающие к местонахождению этого состава, или любого дочернего состава. В противном случае это включает товары, прибывающие к любому местонахождение склада с `внутренним` типом."

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__outgoing_qty
msgid "Quantity of planned outgoing products.\n"
"In a context with a single Stock Location, this includes goods leaving this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods leaving the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods leaving any Stock Location with 'internal' type."
msgstr "Количество запланированных выходных товаров. В контексте, состоящий из одного места расположения магазина, это включает товары, которые оставляют это местонахождение, или любой из его дочерних складов. В контексте единого состава это включает товары, которые оставляют место расположения этого склада или любого его дочернего. В противном случае это включает в себя товары, оставляют любое местонахождение во `внутреннем` типу."

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order_list__item_rate
msgid "Rate"
msgstr "Курс"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__expense_policy
msgid "Re-Invoice Policy"
msgstr "Политика повторного выставления счетов"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__code
msgid "Reference"
msgstr "Ссылка"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__reordering_max_qty
msgid "Reordering Max Qty"
msgstr "Максимальное количество пополнений"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__reordering_min_qty
msgid "Reordering Min Qty"
msgstr "Минимальное количество пополнений"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__nbr_reordering_rules
msgid "Reordering Rules"
msgstr "Правила повторных заказов"

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.hotel_restaurant_reservation_report123
msgid "Reservation List"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_reservation_order__reservation_id
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation__name
msgid "Reservation No"
msgstr "№ Бронирования"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__responsible_id
msgid "Responsible"
msgstr "Ответственный"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__activity_user_id
msgid "Responsible User"
msgstr "Ответственный"

#. module: hotel_restaurant
#: model:ir.ui.menu,name:hotel_restaurant.hotel_restaurant_menu
msgid "Restaurant"
msgstr "Ресторан"

#. module: hotel_restaurant
#: model:ir.actions.act_window,name:hotel_restaurant.action_hotel_restaurant_wizard
msgid "Restaurant Reservation List"
msgstr "Список Бронирования Ресторана"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_reservation_order__room_no
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_kitchen_order_tickets__room_no
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order__room_no
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation__room_no
msgid "Room No"
msgstr "№ Комнаты"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__route_ids
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__route_ids
msgid "Routes"
msgstr "Маршруты"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__description_sale
msgid "Sale Description"
msgstr "Описание продажи"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__lst_price
msgid "Sale Price"
msgstr "Цена продажи"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__sale_line_warn
msgid "Sales Order Line"
msgstr "Строка заказа на продажу"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__list_price
msgid "Sales Price"
msgstr "Продажная цена"

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_reservation_order_filter
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_restaurant_order_filter
msgid "Search Table Order"
msgstr "Найти заказ столика"

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__categ_id
msgid "Select category for the current product"
msgstr "Выберите категорию для продукта"

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__sale_line_warn
msgid "Selecting the \"Warning\" option will notify user with the message, Selecting \"Blocking Message\" will throw an exception with the message and block the flow. The Message has to be written in the next field."
msgstr "\"Предупреждение\" - сообщить пользователю. \"Блокирующее сообщение\" - исключительная ситуация, сообщить пользователю и заблокировать рабочий процесс. Текст сообщения должен быть записан в следующее поле."

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__sequence
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order_list__name
msgid "Sequence"
msgstr "Нумерация"

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard_type__removal_strategy_id
msgid "Set a specific removal strategy that will be used regardless of the source location for this product category"
msgstr "Выберите конкретную стратегию удаления, которая будет использоваться независимо от расположения источника этой категории продукции"

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_restaurant_reservation_form
msgid "Set to Draft"
msgstr "Сделать черновиком"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__image_small
msgid "Small-sized image"
msgstr "Маленькое изображение"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__sales_count
msgid "Sold"
msgstr "Продано"

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__property_cost_method
msgid "Standard Price: The products are valued at their standard cost defined on the product.\n"
"        Average Cost (AVCO): The products are valued at weighted average cost.\n"
"        First In First Out (FIFO): The products are valued supposing those that enter the company first will also leave it first."
msgstr "Стандартная цена: товары оцениваются по их стандартной стоимости, определенной на товаре. Средняя цена (AVCO): товары оцениваются по средней стоимости. First In First Out (FIFO): товары оцениваются, считая, что те, кто входит в компанию впервые, также оставят его сначала."

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard_type__property_cost_method
msgid "Standard Price: The products are valued at their standard cost defined on the product.\n"
"        Average Cost (AVCO): The products are valued at weighted average cost.\n"
"        First In First Out (FIFO): The products are valued supposing those that enter the company first will also leave it first.\n"
"        "
msgstr "Стандартная цена: товары оцениваются по их стандартной стоимости, определенной на товаре. Средняя цена (AVCO): товары оцениваются по средней стоимости. First In First Out (FIFO): товары оцениваются, считая, что те, кто входит в компанию впервые, также оставят его сначала."

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation__start_date
msgid "Start Date"
msgstr "Дата начала"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_reservation_order__state
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order__state
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation__state
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_tables__state
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_reservation_order_filter
msgid "State"
msgstr "Регион"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__state
msgid "Status"
msgstr "Статус"

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__activity_state
msgid "Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr "Этап основан на действиях Просроченный: срок исполнения уже прошел Сегодня: дата действия сегодня Запланировано: будущие действия."

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__stock_fifo_manual_move_ids
msgid "Stock Fifo Manual Move"
msgstr "Ручное перемещение по Fifo"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__stock_fifo_real_time_aml_ids
msgid "Stock Fifo Real Time Aml"
msgstr "Перемещение в реальном времени по Fifo"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__property_stock_account_input
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__property_stock_account_input_categ_id
msgid "Stock Input Account"
msgstr "Счет стоимости входящих ТМЦ"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__property_stock_journal
msgid "Stock Journal"
msgstr "Складской журнал"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__stock_move_ids
msgid "Stock Move"
msgstr "Перемещение запасов"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__property_stock_account_output
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__property_stock_account_output_categ_id
msgid "Stock Output Account"
msgstr "Счет стоимости исходящих ТМЦ"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__stock_quant_ids
msgid "Stock Quant"
msgstr "Количество на складе"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__property_stock_valuation_account_id
msgid "Stock Valuation Account"
msgstr "Счёт оценки запасов"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__stock_value_currency_id
msgid "Stock Value Currency"
msgstr "Валюта остатков запаса"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order__amount_subtotal
msgid "SubTotal"
msgstr "Промежуточный итог"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_reservation_order__amount_subtotal
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order_list__price_subtotal
msgid "Subtotal"
msgstr "Подытог"

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_restaurant_tables_form
msgid "Supplier Invoice"
msgstr "Счет Поставщика"

#. module: hotel_restaurant
#: model:ir.ui.menu,name:hotel_restaurant.menu_open_view_hotel_restaurant_reservation_form_tree
msgid "Table Booking"
msgstr "Бронирование Столиков"

#. module: hotel_restaurant
#: model:ir.ui.menu,name:hotel_restaurant.menu_open_view_hotel_restaurant_order_form_tree
msgid "Table Order"
msgstr "Заказ Столика"

#. module: hotel_restaurant
#: model:ir.actions.act_window,name:hotel_restaurant.open_view_hotel_restaurant_reservation_form_tree
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_restaurant_reservation_tree
msgid "Table Reservation"
msgstr "Бронирование Столиков"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_reservation_order__table_no
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_kitchen_order_tickets__tableno
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order__table_no
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation__tableno
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_tables__name
msgid "Table number"
msgstr "№ Столика"

#. module: hotel_restaurant
#: model:ir.ui.menu,name:hotel_restaurant.menu_open_view_hotel_restaurant_tables_form_tree
msgid "Tables"
msgstr "Столики"

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_restaurant_tables_tree
msgid "Tables Detail"
msgstr "Детали Столиков"

#. module: hotel_restaurant
#: model:ir.actions.act_window,name:hotel_restaurant.open_view_hotel_restaurant_tables_form_tree
msgid "Tables Details"
msgstr "Детали Столиков"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_reservation_order__tax
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order__tax
msgid "Tax (%) "
msgstr "Налог"

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__valid_archived_variant_ids
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__valid_existing_variant_ids
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__valid_product_attribute_ids
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__valid_product_attribute_value_ids
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__valid_product_attribute_value_wnva_ids
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__valid_product_attribute_wnva_ids
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__valid_product_template_attribute_line_ids
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__valid_product_template_attribute_line_wnva_ids
msgid "Technical compute"
msgstr "Техническое вычисления"

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__pricelist_id
msgid "Technical field. Used for searching on pricelists, not stored in database."
msgstr "Техническое поле. Используется для поиска по прайс-листам, не хранится в базе данных."

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__stock_move_ids
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__stock_quant_ids
msgid "Technical: used to compute quantities."
msgstr "Технический: используется для вычисления количества."

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__state
msgid "Tells the user if room is available of booked."
msgstr "Сообщает пользователю, свободен ли забронированный номер."

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__product_template_attribute_value_ids
msgid "Template Attribute Values"
msgstr "Значение шаблона атрибута"

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard_type__property_account_expense_categ_id
msgid "The expense is accounted for when a vendor bill is validated, except in anglo-saxon accounting with perpetual inventory valuation in which case the expense (Cost of Goods Sold account) is recognized at the customer invoice validation."
msgstr "Расходы учитываются при проверке счета поставщика, за исключением англосаксонского бухучета с бесконечной оценке запаса, в этом случае расходы (счет стоимости продаваемого товара) признаются подтверждением счета-фактуры клиента."

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard_type__product_count
msgid "The number of products under this category (Does not consider the children categories)"
msgstr "Количество продуктов этой категории (не учитывает подчиненные категории )"

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__lst_price
msgid "The sale price is managed from the product template. Click on the 'Configure Variants' button to set the extra attribute prices."
msgstr "Цена продажи управляется из шаблона товара. Нажмите кнопку `Настроить варианты`, чтобы установить дополнительные цены атрибутов."

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__volume
msgid "The volume in m3."
msgstr "Объём в метрах куб."

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard_type__property_account_income_categ_id
msgid "This account will be used when validating a customer invoice."
msgstr "Этот счет будет использовано при проверке счета-фактуры клиента."

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__image_variant
msgid "This field holds the image used as image for the product variant, limited to 1024x1024px."
msgstr "Это поле содержит изображение варианта продукта, лимит - 1024x1024px."

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__price_extra
msgid "This is the sum of the extra price of all attributes"
msgstr "Это сумма дополнительной цены всех атрибутов"

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__property_stock_production
msgid "This stock location will be used, instead of the default one, as the source location for stock moves generated by manufacturing orders."
msgstr "Это место хранения будет использоваться, вместо места хранения по умолчанию, как исходное место хранения для движений созданных производственными заказами."

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__property_stock_inventory
msgid "This stock location will be used, instead of the default one, as the source location for stock moves generated when you do an inventory."
msgstr "Это место хранения будет использоваться, вместо места хранения по умолчанию, как исходное место хранения для движений созданных при проведении инвентаризации."

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__responsible_id
msgid "This user will be responsible of the next activities related to logistic operations for this product."
msgstr "Этот пользователь будет отвечать за следующие действия, связанные с логистическими операциями для этого товара."

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation_wizard__date_end
msgid "To Date"
msgstr "На дату"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__to_weight
msgid "To Weigh With Scale"
msgstr "Для взвешивания на весах"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_reservation_order__amount_total
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order__amount_total
msgid "Total"
msgstr "Всего"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__total_route_ids
msgid "Total routes"
msgstr "Итого маршрутов"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__service_type
msgid "Track Service"
msgstr "Сервис отслеживания"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__tracking
msgid "Tracking"
msgstr "Отслеживание"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__uom_id
msgid "Unit of Measure"
msgstr "Единица измерения"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__uom_name
msgid "Unit of Measure Name"
msgstr "Название единицы измерения"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__message_unread
msgid "Unread Messages"
msgstr "Непрочитанные Сообщения"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Счетчик непрочитанных сообщений"

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_restaurant_tables_form
msgid "Update"
msgstr "Обновить"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__valid_archived_variant_ids
msgid "Valid Archived Variants"
msgstr "Действительные заархивированные варианты"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__valid_existing_variant_ids
msgid "Valid Existing Variants"
msgstr "Действительные существующие варианты"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__valid_product_template_attribute_line_ids
msgid "Valid Product Attribute Lines"
msgstr "Действительные строки атрибутов товара"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__valid_product_template_attribute_line_wnva_ids
msgid "Valid Product Attribute Lines Without No Variant Attributes"
msgstr "Действительные строки атрибутов товара без варианта атрибутов"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__valid_product_attribute_value_ids
msgid "Valid Product Attribute Values"
msgstr "Действительные значения атрибута товара"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__valid_product_attribute_value_wnva_ids
msgid "Valid Product Attribute Values Without No Variant Attributes"
msgstr "Действительные значения атрибута товара без вариантов атрибутов"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__valid_product_attribute_ids
msgid "Valid Product Attributes"
msgstr "Действительные атрибуты товара"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__valid_product_attribute_wnva_ids
msgid "Valid Product Attributes Without No Variant Attributes"
msgstr "Действительные атрибуты товара без вариантов атрибутов"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__valuation
msgid "Valuation"
msgstr "Оценка"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__stock_value
msgid "Value"
msgstr "Объём"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__image_variant
msgid "Variant Image"
msgstr "Изображение варианта"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__price_extra
msgid "Variant Price Extra"
msgstr "Экстра-цена варианта"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__variant_seller_ids
msgid "Variant Seller"
msgstr "вариант продавца"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__supplier_taxes_id
msgid "Vendor Taxes"
msgstr "Налоги поставщика"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__seller_ids
msgid "Vendors"
msgstr "Производители"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__volume
msgid "Volume"
msgstr "Объём"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_reservation_order__waitername
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_kitchen_order_tickets__w_name
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order__waiter_name
msgid "Waiter Name"
msgstr "Имя официанта"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__warehouse_id
msgid "Warehouse"
msgstr "Склад"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__website_message_ids
msgid "Website Messages"
msgstr "Сообщения с сайта"

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__website_message_ids
msgid "Website communication history"
msgstr "История общения с сайта"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__weight
msgid "Weight"
msgstr "Вес"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__weight_uom_id
msgid "Weight Unit of Measure"
msgstr "Единица измерения веса"

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__weight
msgid "Weight of the product, packaging not included. The unit of measure can be changed in the general settings"
msgstr "Вес изделия, упаковка не входит. Единицу можно изменить в общих настройках"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__weight_uom_name
msgid "Weight unit of measure label"
msgstr "Метка единицы измерения веса"

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard_type__property_stock_account_input_categ_id
msgid "When doing real-time inventory valuation, counterpart journal items for all incoming stock moves will be posted in this account, unless there is a specific valuation account set on the source location. This is the default value for all products in this category. It can also directly be set on each product"
msgstr "При проведении оценки инвентаризации в реальном времени, корреспондирующие элементы журнала для всех входящих перемещений запасов будут отправлены на этот счёт, если нет конкретного счёта заданного по исходному месту хранения. Его также можно установить непосредственно для каждого продукта."

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__property_stock_account_input
msgid "When doing real-time inventory valuation, counterpart journal items for all incoming stock moves will be posted in this account, unless there is a specific valuation account set on the source location. When not set on the product, the one from the product category is used."
msgstr "При проведении оценки инвентаризации в реальном времени, корреспондирующие пункты журнала для всех входящих перемещений запасов будут отправлены на этот счёт, если нет конкретного счёта заданного по исходному месту хранения. Его также можно установить непосредственно для каждого товара."

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard_type__property_stock_account_output_categ_id
msgid "When doing real-time inventory valuation, counterpart journal items for all outgoing stock moves will be posted in this account, unless there is a specific valuation account set on the destination location. This is the default value for all products in this category. It can also directly be set on each product"
msgstr "При проведении оценки инвентаризации в реальном времени, корреспондирующие пункты журнала для всех входящих перемещений запасов будут отправлены на этот счёт, если нет конкретного счёта заданного по месту назначения. Его также можно установить непосредственно для каждого продукта."

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__property_stock_account_output
msgid "When doing real-time inventory valuation, counterpart journal items for all outgoing stock moves will be posted in this account, unless there is a specific valuation account set on the destination location. When not set on the product, the one from the product category is used."
msgstr "При проведении оценки инвентаризации в реальном времени, корреспондирующие элементы журнала для всех исходящих перемещений будут помещены на этот счёт, если нет задан счёт в месте назначения. Его также можно установить непосредственно для каждого продукта."

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard_type__property_stock_journal
msgid "When doing real-time inventory valuation, this is the Accounting Journal in which entries will be automatically posted when stock moves are processed."
msgstr "При оценке стоимости запасов в реальном времени, это бухгалтерский журнал в который будут автоматически добавляться записи при обработке перемещений ТМЦ."

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard_type__property_stock_valuation_account_id
msgid "When real-time inventory valuation is enabled on a product, this account will hold the current value of the products."
msgstr "При оценке запасов в реальном времени, этот счет будет содержать текущую оценку стоимости продуктов."

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_restaurant_order__partner_id
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_restaurant_reservation__cname
msgid "Will show customer name corresponding to selected room no."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_restaurant_order__room_no
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_restaurant_reservation__room_no
msgid "Will show list of currently occupied room no that belongs to selected shop."
msgstr "Отобразится список занятых в данный момент номеров, принадлежащих выбранному магазину."

#. module: hotel_restaurant
#: model:ir.model,name:hotel_restaurant.model_hotel_menucard_type
msgid "amenities Type"
msgstr "Тип услуг"

#. module: hotel_restaurant
#: model:ir.model,name:hotel_restaurant.model_hotel_restaurant_reservation_wizard
msgid "hotel_restaurant_reservation_wizard"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model,name:hotel_restaurant.model_hotel_restaurant_wizard
msgid "hotel_restaurant_wizard"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model,name:hotel_restaurant.model_report_hotel_restaurant_hotel_restaurant_reservation_report123
msgid "report.hotel_restaurant.hotel_restaurant_reservation_report123"
msgstr ""

