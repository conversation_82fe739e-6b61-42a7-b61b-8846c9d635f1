# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * base_location_geonames_import
#
# Translators:
# <AUTHOR> <EMAIL>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-01-03 20:26+0000\n"
"PO-Revision-Date: 2023-10-31 14:38+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Portuguese (Brazil) (https://www.transifex.com/oca/teams/"
"23907/pt_BR/)\n"
"Language: pt_BR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: Weblate 4.17\n"

#. module: base_location_geonames_import
#. odoo-python
#: code:addons/base_location_geonames_import/wizard/geonames_import.py:0
#, python-format
msgid "%d could not be deleted %"
msgstr "%d não pôde ser excluído %"

#. module: base_location_geonames_import
#: model_terms:ir.ui.view,arch_db:base_location_geonames_import.city_zip_geonames_import_form
msgid ""
", create new location entries if not found already in the system, and "
"<b>delete missing entries</b> from new file."
msgstr ""
", crie novas entradas de local, se ainda não foram encontradas no sistema, e "
"<b>exclua as entradas ausentes</b> do novo arquivo."

#. module: base_location_geonames_import
#: model_terms:ir.ui.view,arch_db:base_location_geonames_import.city_zip_geonames_import_form
msgid "Cancel"
msgstr "Cancelar"

#. module: base_location_geonames_import
#: model:ir.model.fields,help:base_location_geonames_import.field_city_zip_geonames_import__letter_case
msgid ""
"Converts retreived city and state names to Title Case (upper case on each "
"first letter of a word) or Upper Case (all letters upper case)."
msgstr ""
"Converte nomes de cidades e estados recuperados em Titulo Maiúsculo ("
"maiúsculas em cada primeira letra de uma palavra) ou Maiúsculas (todas as "
"letras maiúsculas)."

#. module: base_location_geonames_import
#: model:ir.model.fields,field_description:base_location_geonames_import.field_city_zip_geonames_import__country_ids
msgid "Countries"
msgstr "Países"

#. module: base_location_geonames_import
#: model:ir.model,name:base_location_geonames_import.model_res_country
msgid "Country"
msgstr "País"

#. module: base_location_geonames_import
#: model:ir.model.fields,field_description:base_location_geonames_import.field_city_zip_geonames_import__create_uid
msgid "Created by"
msgstr "Criado por"

#. module: base_location_geonames_import
#: model:ir.model.fields,field_description:base_location_geonames_import.field_city_zip_geonames_import__create_date
msgid "Created on"
msgstr "Criado em"

#. module: base_location_geonames_import
#: model:ir.model.fields,field_description:base_location_geonames_import.field_city_zip_geonames_import__display_name
msgid "Display Name"
msgstr "Nome a mostrar"

#. module: base_location_geonames_import
#: model_terms:ir.ui.view,arch_db:base_location_geonames_import.city_zip_geonames_import_form
msgid ""
"For the selected country, this wizard will download the latest version of "
"the list of cities from"
msgstr ""
"Para o país selecionado, este assistente fará o download da versão mais "
"recente da lista de cidades de"

#. module: base_location_geonames_import
#: model:ir.model.fields,field_description:base_location_geonames_import.field_res_country__geonames_state_code_column
msgid "Geonames State Code Column"
msgstr "Coluna Código do estado dos nomes geográficos"

#. module: base_location_geonames_import
#: model:ir.model.fields,field_description:base_location_geonames_import.field_res_country__geonames_state_name_column
msgid "Geonames State Name Column"
msgstr "Coluna Nome do estado dos nomes geográficos"

#. module: base_location_geonames_import
#. odoo-python
#: code:addons/base_location_geonames_import/wizard/geonames_import.py:0
#, python-format
msgid "Got an error %d when trying to download the file %s."
msgstr "Recebido um erro %d ao tentar o download do arquivo %s."

#. module: base_location_geonames_import
#: model:ir.model.fields,field_description:base_location_geonames_import.field_city_zip_geonames_import__id
msgid "ID"
msgstr "ID"

#. module: base_location_geonames_import
#: model_terms:ir.ui.view,arch_db:base_location_geonames_import.city_zip_geonames_import_form
msgid "Import"
msgstr "Importar"

#. module: base_location_geonames_import
#: model:ir.model,name:base_location_geonames_import.model_city_zip_geonames_import
msgid "Import City Zips from Geonames"
msgstr "Importar códigos postais de nomes Geográficos"

#. module: base_location_geonames_import
#: model:ir.actions.act_window,name:base_location_geonames_import.city_zip_geonames_import_action
#: model:ir.ui.menu,name:base_location_geonames_import.city_zip_geonames_import_menu
#: model_terms:ir.ui.view,arch_db:base_location_geonames_import.city_zip_geonames_import_form
msgid "Import from Geonames"
msgstr "Importar de Geonames"

#. module: base_location_geonames_import
#: model:ir.model.fields,field_description:base_location_geonames_import.field_city_zip_geonames_import____last_update
msgid "Last Modified on"
msgstr "Ultima modificação em"

#. module: base_location_geonames_import
#: model:ir.model.fields,field_description:base_location_geonames_import.field_city_zip_geonames_import__write_uid
msgid "Last Updated by"
msgstr "Útima atualização por"

#. module: base_location_geonames_import
#: model:ir.model.fields,field_description:base_location_geonames_import.field_city_zip_geonames_import__write_date
msgid "Last Updated on"
msgstr "Útima atualização em"

#. module: base_location_geonames_import
#: model:ir.model.fields,field_description:base_location_geonames_import.field_city_zip_geonames_import__letter_case
msgid "Letter Case"
msgstr "Capitalização"

#. module: base_location_geonames_import
#: model:ir.model.fields.selection,name:base_location_geonames_import.selection__city_zip_geonames_import__letter_case__title
msgid "Title Case"
msgstr "Nome Próprio"

#. module: base_location_geonames_import
#: model:ir.model.fields.selection,name:base_location_geonames_import.selection__city_zip_geonames_import__letter_case__unchanged
msgid "Unchanged"
msgstr "Sem mudanças"

#. module: base_location_geonames_import
#: model:ir.model.fields.selection,name:base_location_geonames_import.selection__city_zip_geonames_import__letter_case__upper
msgid "Upper Case"
msgstr "Todas maiúsculas"

#. module: base_location_geonames_import
#: model_terms:ir.ui.view,arch_db:base_location_geonames_import.city_zip_geonames_import_form
msgid "geonames.org"
msgstr "geonames.org"

#~ msgid ""
#~ "The country code inside the file (%s) doesn't correspond to the selected "
#~ "country (%s)."
#~ msgstr ""
#~ "O código país dentro do arquivo (%s) não corresponde ao país selecionado "
#~ "(%s)"
