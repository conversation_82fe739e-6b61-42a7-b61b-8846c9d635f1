.on_board_custom_form{
    border-radius: 8px !important;
}
.on_board_custom_form .o_input, .on_board_custom_form .on_board_field {
    /*color: #000;*/
    border: 1px solid #dadada;
    border-radius: 3px;
    padding: 5px 10px;
    vertical-align: middle;
    text-overflow: ellipsis;
    overflow-x: hidden;
}
.on_board_custom_form .o_form_nosheet .o_group .o_group_col_6 {
    /*width: 48%;
    margin: 5px !important;*/
    width: 46%;
    margin: 1% 2% !important;
}
.on_board_custom_form div.o_input_dropdown > a.o_dropdown_button {
    display: inline !important;
    position: absolute;
    border: none;
    top: 5px;
}
.on_board_custom_form > .modal-header > .modal-title {
    font-weight: 500;
}
.o_on_board_heading{
    font-size: 20px !important;
    padding: 10px 20px 0;
}
.o_onboard_header {
    font-size: 17px !important;
    background-color: #e8eeef;
    padding: 6px 25px;
    margin: 0px !important;
    color: #223459;
}
.o_onboard_padding {
    padding: 0 15px 0 15px;
    margin: 0px;
}
.o_onboard_doc_link {
    color: #0068ff !important;
}
a.o_onboard_doc_link a:hover {
    color: #0068ff !important;
}
.o_onboard_doc_link::after {
    content: "";
    background: url('/common_connector_library/static/src/img/asset2.svg');
    color: #0068ff;
    vertical-align: middle;
    height: 17px;
    width: 17px;
    position: absolute;
}
label.o_onboard_label {
    /*color: #383838;*/
    vertical-align: middle;
}
select.o_field_widget.o_onboard_select {
    padding: 5px 12px;
    float: right;
    border: 1px solid #dadada;
    margin-top: -6px;
    border-radius: 3px;
    background-position-x: 95%;
}
.o_onboard_input > .o_input_dropdown > input.o_input {
    border: 1px solid #dadada !important;
    border-radius: 3px;
    padding: 5px 20px 5px 8px;
    vertical-align: text-bottom;
    color: #666666;
    text-overflow: ellipsis;
}
.o_onboard_input > .o_input_dropdown > .o_dropdown_button {
    right: 5%;
    top: 15%;
}
.o_onboard_input {
    width: 50%;
    padding: 0px;
    float: right;
    margin: 0;
    background-position-x: 95%;
}
.o_onboard_input > input[type="text"] {
    border: 1px solid #dadada;
    background-position-x: 95%;
    padding: 5px 12px;
    margin-top: -6px;
    border-radius: 3px;
}
.o_onboard_input > span.o_datepicker_button {
    right: 12px !important;
}
.o_onboard_button, .o_onboard_button_white{
    border-radius: 5px;
    font-size: 13px;
    background-color: #0068ff !important;
    border-color: #bbbbbb;
    margin: 0px 5px !important;
    box-shadow: 1px 1px 1px 1px #e0e0e0;
}
.o_onboard_button_white{
    background-color: #ffffff !important;
    color: #233459;
}
.o_onboard_button:hover {
    background-color: #0250de !important;
}
.o_onboard_button_white:hover {
    background-color: #e8eeef !important;
    color: #233459;
    border-color: #bbbbbb;
}
.o_onboard_button_white:not(:disabled):not(.disabled):active {
    color: #233459;
}
.on_board_custom_form > .o_act_window {
    overflow-x: hidden;
    overflow-y: auto;
}

div.o_onboard_boolean_toggle .fa-check-circle {
    display:none;
}

div.o_onboard_boolean_toggle.custom-control.custom-checkbox > label.custom-control-label::before {
    background-color: #ffffff !important;
    border-radius: 4px;
    width: 22px;
    height: 22px;
    border: 1px solid #c5c5c5;
}
/*Custom Radio button*/
div.o_onboard_radio_toggle .custom-control-input ~ .custom-control-label::before {
    background-color: #ffffff !important;
    border-radius: 4px;
    width: 14px;
    height: 14px;
    border: 1px solid #c5c5c5;
}
div.o_onboard_radio_toggle .custom-control-input:checked ~ .custom-control-label::before {
    background-color: #0068ff !important;
}
.custom-radio .custom-control-input:checked ~ .custom-control-label::after {
    background-image: unset !important;
}

div.o_onboard_boolean_toggle.custom-control.custom-checkbox > input.custom-control-input:checked + label.custom-control-label::before {
    background-color: #0068ff !important;
    background-image: url('/common_connector_library/static/src/img/asset1.svg');
}
div.o_onboard_boolean_toggle.custom-control.custom-checkbox > label.custom-control-label::after {
    transform: translate(0, 0) !important;
    width: 0px;
    height: 0px;
    border-radius: 0px;
    background-color: none;
    cursor: pointer;
}
.o_onboard_setting_box {
    margin-bottom: 5px !important;
    margin-top: 5px !important;
}
div.on_board_custom_form > main > div.o_action > div.o_content > div.o_form_nosheet.o_xxl_form_view {
    padding: 0px !important;
}
div.o_onboard_input > button.btn-secondary, .o_onboard_cron_user > button.btn-secondary{
    height: 30px;
    width: 30px;
    margin: 0px;
    color: #565656 !important;
    position: relative;
}
div.o_onboard_input > button.fa-external-link::before, .o_onboard_cron_user > button.fa-external-link::before {
    background: url('/common_connector_library/static/src/img/asset3.svg');
    content: "";
    height: 18px;
    width: 18px;
    position: absolute;
    top: 6px;
    right: 5px;
}
div.on_board_alert_warning {
    color: #383838;
    font-weight: 300;
    background-color: #fff3f3;
    border-color: #fdc6c6;
    border-radius: 5px;
}
.on_board_alert_warning > h3 {
    font-weight: bold;
    font-size: 14px;
}
div.o_onboard_m_p {
    padding: 10px 5px;
    margin: 5px 0px;
}
.o_onboard_notebook {
    margin: 10px 0 0 0 !important;
    color: #223459;
}
.o_onboard_notebook .o_notebook_headers {
    background-color: #e8eeef;
    margin: 0px !important;
    color: #223459;
    font-size: 16px;
    font-weight: bold;
    text-overflow: ellipsis;
}
.o_onboard_notebook > .o_notebook_headers > .nav.nav-tabs {
    border: none !important;
}
.o_onboard_notebook > .o_notebook_headers > .nav.nav-tabs > .nav-item > a {
    padding-left: 0px !important;
    color: #223459 !important;
}
.o_onboard_notebook > .o_notebook_headers > .nav.nav-tabs > .nav-item > .nav-link.active {
    background-color: #e7eeef;
    border: none;
}
.on_board_list_view .on_board_table tbody:first-of-type > tr:first-child:not(.o_group_header) {
    border: 1px solid #e7eeef;
}
.on_board_list_view .on_board_table thead {
    color: #383838;
}
.on_board_list_view .on_board_table > thead > tr > th {
    padding: 0.5rem;
}
.on_board_list_view .on_board_table > tbody > tr > td {
    padding: 0.5rem;
}
.on_board_list_view .on_board_table > tbody > tr > td.o_field_x2many_list_row_add > a:hover {
    color: #0045a9;
}
.on_board_list_view .on_board_table > tbody > tr > td.o_field_x2many_list_row_add > a {
    color: #0068ff;
}
.o_onboard_extra_margin {
    margin: 0 -40px 0 -35px !important;
    border-left: 1px solid #cecece;
    padding-left: 7%;
}
.o_onboard_cron_box {
    border: 1px solid #dadada !important;
    border-radius: 3px;
    padding: 4px !important;
    width: 45% !important;
    margin: 0px 10px 0px 0px;
    background-position-x: 95%;
    max-width: 55px;
}
.o_onboard_cron_type, .o_onboard_cron_execution, .o_onboard_cron_user {
    margin: 0px 10px 0px 0px;
    width: 45% !important;
    position: relative;
    max-width: 160px;
}
.o_onboard_cron_settings > select, .o_onboard_cron_settings > div.o_datepicker > input, .o_onboard_cron_settings > .o_field_widget > .o_input_dropdown > input{
    border: 1px solid #dadada !important;
    padding: 4px !important;
    border-radius: 3px;
    background-position-x: 95%;
}
.o_onboard_cron_settings > div.o_datepicker > span, .o_onboard_cron_settings > .o_field_widget > .o_input_dropdown > a {
    right: 5%;
    top: 4px;
    color: black;
}
.o_onboard_setting_box > .o_setting_right_pane > span > h6 {
    font-weight: bold;
}
.o_onboard_padding .o_group.o_inner_group > tbody > tr > td.o_td_label {
    min-width: 90px;
}
.o_onboard_padding .o_group.o_inner_group.woo_store_timezone_alignment > tbody > tr > td.o_td_label {
    min-width: 110px;
}
.o_onboard_padding .o_group.o_inner_group.woo_consumer_secret_alignment > tbody > tr > td.o_td_label {
    min-width: 120px;
}
.o_onboard_padding .o_group.o_inner_group.woo_is_export_update_images_alignment > tbody > tr > td.o_td_label {
    min-width: 245px;
}


.o_onboard_padding .o_group.o_inner_group > tbody > tr > td {
    padding: 0px;
    vertical-align: middle;
    color: #202529;
}
.o_onboard_padding .o_group.o_inner_group > tbody > tr > td > a {
    width: 80% !important;
    padding: 4px !important;
    border: 1px solid #dadada;
    border-radius: 3px;
    color: #0250de;
}
.o_onboard_padding .o_group.o_inner_group > tbody > tr > td > a:focus {
    outline: none;
}
.o_onboard_password {
    -webkit-text-security: disc;
    color: black;
    line-height: 24px;
    height: 36px;
    font-size: 20px;
    font-weight: 700;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    transition: all .15s ease-in-out;
    overflow: hidden;
}

/* To Hide Dash board panel in Mobile devices*/
@media only screen and (max-width: 1024px) {
    #shopify_button_toggle, #woo_button_toggle, .hide_in_mobile_device {
        display : none;
    }
}
/*Hide the close button form only magento onboarding, shopify and woo onboarding panel*/
.magento_onboarding_ept .common_close_button_hide_ept,.woo_onboarding_ept .common_close_button_hide_ept,.shopify_onboarding_ept .common_close_button_hide_ept {
    display: none;
}

.o_custom_notebook_headers {
    background-color: #e8eeef !important;
}