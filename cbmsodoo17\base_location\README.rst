====================================
Location management (aka Better ZIP)
====================================

.. 
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! This file is generated by oca-gen-addon-readme !!
   !! changes will be overwritten.                   !!
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! source digest: sha256:d2d2c3fb7340a58539b32f48714ff8c7d557889af4d93a7d90f06c85343a058b
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

.. |badge1| image:: https://img.shields.io/badge/maturity-Mature-brightgreen.png
    :target: https://odoo-community.org/page/development-status
    :alt: Mature
.. |badge2| image:: https://img.shields.io/badge/licence-AGPL--3-blue.png
    :target: http://www.gnu.org/licenses/agpl-3.0-standalone.html
    :alt: License: AGPL-3
.. |badge3| image:: https://img.shields.io/badge/github-OCA%2Fpartner--contact-lightgray.png?logo=github
    :target: https://github.com/OCA/partner-contact/tree/17.0/base_location
    :alt: OCA/partner-contact
.. |badge4| image:: https://img.shields.io/badge/weblate-Translate%20me-F47D42.png
    :target: https://translation.odoo-community.org/projects/partner-contact-17-0/partner-contact-17-0-base_location
    :alt: Translate me on Weblate
.. |badge5| image:: https://img.shields.io/badge/runboat-Try%20me-875A7B.png
    :target: https://runboat.odoo-community.org/builds?repo=OCA/partner-contact&target_branch=17.0
    :alt: Try me on Runboat

|badge1| |badge2| |badge3| |badge4| |badge5|

This module introduces a zip model that allows you to manage locations
in a better way.

The zips will allow the users to complete automatically all
address-related fields by just filling the zip.

Also allows different search filters.

**Table of contents**

.. contents::
   :local:

Configuration
=============

1. Go to *Contacts / Configuration / Localization / Cities*.
2. Create a new City.
3. Go to *Contacts / Configuration / Localization / Zips*.
4. Create a new Zip and relate it to the city (you can also create the
   Zip from the City).

or, with module 'Contacts Directory' installed: #. Go to *Contacts /
Configuration / Localization / Countries*. #. Locate the desired
country. #. Press on the button 'Cities' / 'Zips'.

Usage
=====

1. Access a partner record
2. Fill the field *Location completion*
3. Information about country, state, city and zip will be filled
   automatically

Bug Tracker
===========

Bugs are tracked on `GitHub Issues <https://github.com/OCA/partner-contact/issues>`_.
In case of trouble, please check there if your issue has already been reported.
If you spotted it first, help us to smash it by providing a detailed and welcomed
`feedback <https://github.com/OCA/partner-contact/issues/new?body=module:%20base_location%0Aversion:%2017.0%0A%0A**Steps%20to%20reproduce**%0A-%20...%0A%0A**Current%20behavior**%0A%0A**Expected%20behavior**>`_.

Do not contact contributors directly about support or help with technical issues.

Credits
=======

Authors
-------

* Camptocamp
* ACYSOS S.L.
* Alejandro Santana
* Tecnativa
* AdaptiveCity

Contributors
------------

-  Nicolas Bessi (Camptocamp)
-  Ignacio Ibeas (Acysos S.L.)
-  Pedro M. Baeza <<EMAIL>>
-  Alejandro Santana <<EMAIL>>
-  Sandy Carter <<EMAIL>>
-  Yannick Vaucher <<EMAIL>>
-  Francesco Apruzzese <<EMAIL>>
-  Dave Lasley <<EMAIL>>
-  Aitor Bouzas <<EMAIL>>

Other credits
-------------

-  Icon park: Icon http://icon-park.com/icon/location-map-pin-orange3/

Maintainers
-----------

This module is maintained by the OCA.

.. image:: https://odoo-community.org/logo.png
   :alt: Odoo Community Association
   :target: https://odoo-community.org

OCA, or the Odoo Community Association, is a nonprofit organization whose
mission is to support the collaborative development of Odoo features and
promote its widespread use.

.. |maintainer-pedrobaeza| image:: https://github.com/pedrobaeza.png?size=40px
    :target: https://github.com/pedrobaeza
    :alt: pedrobaeza

Current `maintainer <https://odoo-community.org/page/maintainer-role>`__:

|maintainer-pedrobaeza| 

This module is part of the `OCA/partner-contact <https://github.com/OCA/partner-contact/tree/17.0/base_location>`_ project on GitHub.

You are welcome to contribute. To learn how please visit https://odoo-community.org/page/Contribute.
