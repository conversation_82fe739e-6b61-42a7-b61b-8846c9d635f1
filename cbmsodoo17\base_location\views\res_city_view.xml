<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <record id="view_city_tree_inherit" model="ir.ui.view">
        <field name="model">res.city</field>
        <field name="inherit_id" ref="base_address_extended.view_city_tree" />
        <field name="arch" type="xml">
            <tree position="attributes">
                <attribute name="editable" />
            </tree>
            <field name="zipcode" position="attributes">
                <attribute name="column_invisible">1</attribute>
            </field>
            <field name="zipcode" position="after">
                <field name="zip_ids" widget="many2many_tags" />
            </field>
        </field>
    </record>
    <record id="view_city_form" model="ir.ui.view">
        <field name="model">res.city</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <field name="name" />
                        <field name="country_id" />
                        <field name="state_id" />
                    </group>
                    <notebook>
                        <page name="zips" string="Zips">
                            <field name="zip_ids" />
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>
    <record id="action_res_city_full" model="ir.actions.act_window">
        <field name="name">Cities</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">res.city</field>
        <field name="view_mode">tree,form</field>
        <field
            name="view_ids"
            eval="[(5,0,0),
                      (0, 0, {'view_mode': 'tree', 'view_id': ref('base_address_extended.view_city_tree')}),
                      (0, 0, {'view_mode': 'form', 'view_id': ref('view_city_form')})]"
        />
        <field name="help">
            Display and manage the list of all cities that can be assigned to
            your partner records. Note that an option can be set on each country
            separately
            to enforce any address of it to have a city in this list.
        </field>
    </record>
    <record model="ir.ui.menu" id="base_address_extended.menu_res_city">
        <field name="active" eval="False" />
    </record>
    <menuitem
        name="Cities"
        id="locations_menu_cities"
        parent="contacts.menu_localisation"
        action="action_res_city_full"
        sequence="4"
    />
</odoo>
