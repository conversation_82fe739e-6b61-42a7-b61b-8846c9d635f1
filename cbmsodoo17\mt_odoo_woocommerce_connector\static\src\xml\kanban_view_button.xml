<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="WooCommImportKanban.Buttons" t-inherit="web.KanbanView.Buttons" t-inherit-mode="primary" owl="1">
        <xpath expr="//div[hasclass('o_cp_buttons')]" position="after">
            <button type="button" t-if="props.resModel == 'product.template'" class="btn btn-primary"
                    style="margin-left: 5px;" t-on-click="onClickWooCommProductImport">
                Import from WooCommerce
            </button>

            <button type="button" t-if='props.resModel == "res.partner"' class="btn btn-primary"
                    style="margin-left: 5px;" t-on-click="onClickWooCommCustomerImport">
                Import from WooCommerce
            </button>          
        </xpath>
    </t>
</templates>
