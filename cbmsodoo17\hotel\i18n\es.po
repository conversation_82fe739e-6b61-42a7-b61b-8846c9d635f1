# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hotel
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-08-12 09:23+0000\n"
"PO-Revision-Date: 2020-08-12 09:23+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__product_variant_count
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__product_variant_count
#: model:ir.model.fields,field_description:hotel.field_hotel_services__product_variant_count
msgid "# Product Variants"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__product_count
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__product_count
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__product_count
msgid "# Products"
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__description_sale
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__description_sale
#: model:ir.model.fields,help:hotel.field_hotel_services__description_sale
msgid ""
"A description of the Product that you want to communicate to your customers."
" This description will be copied to every Sales Order, Delivery Order and "
"Customer Invoice/Credit Note"
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio_line__product_type
#: model:ir.model.fields,help:hotel.field_hotel_room__type
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__type
#: model:ir.model.fields,help:hotel.field_hotel_service_line__product_type
#: model:ir.model.fields,help:hotel.field_hotel_services__type
msgid ""
"A storable product is a product for which you manage stock. The Inventory app has to be installed.\n"
"A consumable product is a product for which stock is not managed.\n"
"A service is a non-material product you provide."
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__access_warning
msgid "Access warning"
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio_line__qty_delivered_method
#: model:ir.model.fields,help:hotel.field_hotel_service_line__qty_delivered_method
msgid ""
"According to product configuration, the delivered quantity can be automatically computed by mechanism :\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Analytic From expenses: the quantity is the quantity sum from posted expenses\n"
"  - Timesheet: the quantity is the sum of hours recorded on tasks linked to this sale line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_room_amenities_type_form
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_room_type_form
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_service_type_form
msgid "Account Properties"
msgstr "Propiedades de las Cuentas"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__message_needaction
#: model:ir.model.fields,field_description:hotel.field_hotel_room__message_needaction
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__message_needaction
#: model:ir.model.fields,field_description:hotel.field_hotel_services__message_needaction
msgid "Action Needed"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__active
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__active
#: model:ir.model.fields,field_description:hotel.field_hotel_services__active
msgid "Active"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__activity_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room__activity_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__activity_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__activity_ids
msgid "Activities"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__activity_exception_decoration
#: model:ir.model.fields,field_description:hotel.field_hotel_room__activity_exception_decoration
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__activity_exception_decoration
#: model:ir.model.fields,field_description:hotel.field_hotel_services__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__activity_state
#: model:ir.model.fields,field_description:hotel.field_hotel_room__activity_state
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__activity_state
#: model:ir.model.fields,field_description:hotel.field_hotel_services__activity_state
msgid "Activity State"
msgstr ""

#. module: hotel
#: model:ir.ui.menu,name:hotel.menu_open_hotel_folio1_form_tree_view
msgid "All Folio"
msgstr "Todo Folio"

#. module: hotel
#: model:ir.ui.menu,name:hotel.menu_action_hotel_room_amenities_view_form
msgid "Amenities"
msgstr "Instalaciónes"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__rcateg_id
msgid "Amenity Catagory"
msgstr "Instalaciónes por Categoría"

#. module: hotel
#: model:ir.ui.menu,name:hotel.menu_action_hotel_room_amenities_type_view_form
msgid "Amenity Category"
msgstr "Instalaciónes por Categoría "

#. module: hotel
#: model:ir.ui.menu,name:hotel.menu_parent_amenity
msgid "Amenity Defination"
msgstr "Definición de Instalación"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__amenity_rate
msgid "Amenity Rate"
msgstr "Tarifa de Instalación"

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_room_amenities_list
msgid "Amenity rate"
msgstr "Tarifa de Instalación"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__amount_undiscounted
msgid "Amount Before Discount"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__analytic_account_id
msgid "Analytic Account"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__analytic_tag_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__analytic_tag_ids
msgid "Analytic Tags"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__analytic_line_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__analytic_line_ids
msgid "Analytic lines"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__message_attachment_count
#: model:ir.model.fields,field_description:hotel.field_hotel_room__message_attachment_count
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__message_attachment_count
#: model:ir.model.fields,field_description:hotel.field_hotel_services__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__product_template_attribute_value_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__product_template_attribute_value_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__product_template_attribute_value_ids
msgid "Attribute Values"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__authorized_transaction_ids
msgid "Authorized Transactions"
msgstr ""

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "Automatic Declaration"
msgstr "Declaración Automática"

#. module: hotel
#: model:ir.model.fields.selection,name:hotel.selection__product_product__state__draft
msgid "Available"
msgstr "Disponible"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__barcode
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__barcode
#: model:ir.model.fields,field_description:hotel.field_hotel_services__barcode
msgid "Barcode"
msgstr ""

#. module: hotel
#: model:ir.model.fields.selection,name:hotel.selection__product_product__state__sellable
msgid "Booked"
msgstr "reservada"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__campaign_id
msgid "Campaign"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__product_updatable
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__product_updatable
msgid "Can Edit Product"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__can_image_1024_be_zoomed
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__can_image_1024_be_zoomed
#: model:ir.model.fields,field_description:hotel.field_hotel_services__can_image_1024_be_zoomed
msgid "Can Image 1024 be zoomed"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__can_image_variant_1024_be_zoomed
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__can_image_variant_1024_be_zoomed
#: model:ir.model.fields,field_description:hotel.field_hotel_services__can_image_variant_1024_be_zoomed
msgid "Can Variant Image 1024 be zoomed"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__purchase_ok
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__purchase_ok
#: model:ir.model.fields,field_description:hotel.field_hotel_services__purchase_ok
msgid "Can be Purchased"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__rental
#: model:ir.model.fields,field_description:hotel.field_hotel_services__rental
msgid "Can be Rent"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__sale_ok
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__sale_ok
#: model:ir.model.fields,field_description:hotel.field_hotel_services__sale_ok
msgid "Can be Sold"
msgstr ""

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "Cancel Folio"
msgstr "Cancelar Folio"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__product_uom_category_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__cat_id
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__product_uom_category_id
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__ser_id
msgid "Category"
msgstr "Categoría"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__route_from_categ_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__route_from_categ_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__route_from_categ_ids
msgid "Category Routes"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__checkin_date
msgid "Check In"
msgstr "Check In"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__checkout_date
msgid "Check Out"
msgstr " Check out"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__child_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__child_id
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__child_id
msgid "Child Categories"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__color
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__color
#: model:ir.model.fields,field_description:hotel.field_hotel_services__color
msgid "Color Index"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__combination_indices
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__combination_indices
#: model:ir.model.fields,field_description:hotel.field_hotel_services__combination_indices
msgid "Combination Indices"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__company_id
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__company_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room__company_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__company_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__company_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__company_id
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__company_id
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__company_id
#: model:ir.model.fields,field_description:hotel.field_hotel_services__company_id
#: model:ir.model.fields,field_description:hotel.field_product_category__company_id
msgid "Company"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__complete_name
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__complete_name
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__complete_name
msgid "Complete Name"
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__effective_date
msgid "Completion date of the first delivery order."
msgstr ""

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "Compute"
msgstr ""

#. module: hotel
#: model:ir.ui.menu,name:hotel.hotel_configuration_menu
msgid "Configuration"
msgstr "Configuración"

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "Confirm Folio"
msgstr "Confirmar Folio"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio_line__product_uom_category_id
#: model:ir.model.fields,help:hotel.field_hotel_service_line__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__standard_price
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__standard_price
#: model:ir.model.fields,field_description:hotel.field_hotel_services__standard_price
msgid "Cost"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__cost_currency_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__cost_currency_id
#: model:ir.model.fields,field_description:hotel.field_hotel_services__cost_currency_id
msgid "Cost Currency"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__cost_method
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__cost_method
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__property_cost_method
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__property_cost_method
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__property_cost_method
#: model:ir.model.fields,field_description:hotel.field_hotel_services__cost_method
msgid "Costing Method"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_floor__create_uid
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__create_uid
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__create_uid
#: model:ir.model.fields,field_description:hotel.field_hotel_room__create_uid
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__create_uid
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__create_uid
#: model:ir.model.fields,field_description:hotel.field_hotel_room_images__create_uid
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__create_uid
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__create_uid
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__create_uid
#: model:ir.model.fields,field_description:hotel.field_hotel_services__create_uid
msgid "Created by"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_floor__create_date
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__create_date
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__create_date
#: model:ir.model.fields,field_description:hotel.field_hotel_room__create_date
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__create_date
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__create_date
#: model:ir.model.fields,field_description:hotel.field_hotel_room_images__create_date
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__create_date
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__create_date
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__create_date
#: model:ir.model.fields,field_description:hotel.field_hotel_services__create_date
msgid "Created on"
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__date_order
msgid ""
"Creation date of draft/sent orders,\n"
"Confirmation date of confirmed orders."
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__currency_id
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__currency_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room__currency_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__currency_id
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__currency_id
#: model:ir.model.fields,field_description:hotel.field_hotel_services__currency_id
msgid "Currency"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__currency_rate
msgid "Currency Rate"
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__qty_available
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__qty_available
#: model:ir.model.fields,help:hotel.field_hotel_services__qty_available
msgid ""
"Current quantity of products.\n"
"In a context with a single Stock Location, this includes goods stored at this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"stored in the Stock Location of the Warehouse of this Shop, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__product_custom_attribute_value_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__product_custom_attribute_value_ids
msgid "Custom Values"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__partner_id
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__order_partner_id
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__order_partner_id
msgid "Customer"
msgstr "Cliente"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__sale_delay
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__sale_delay
#: model:ir.model.fields,field_description:hotel.field_hotel_services__sale_delay
msgid "Customer Lead Time"
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__access_url
msgid "Customer Portal URL"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__partner_ref
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__partner_ref
#: model:ir.model.fields,field_description:hotel.field_hotel_services__partner_ref
msgid "Customer Ref"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__client_order_ref
msgid "Customer Reference"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__taxes_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__taxes_id
#: model:ir.model.fields,field_description:hotel.field_hotel_services__taxes_id
msgid "Customer Taxes"
msgstr ""

#. module: hotel
#: model:ir.ui.menu,name:hotel.hotel_dashboard_menu
msgid "Dashboard"
msgstr "Cuadro de mandos"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__signed_on
msgid "Date of the signature."
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__supplier_taxes_id
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__supplier_taxes_id
#: model:ir.model.fields,help:hotel.field_hotel_services__supplier_taxes_id
msgid "Default taxes used when buying the product."
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__taxes_id
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__taxes_id
#: model:ir.model.fields,help:hotel.field_hotel_services__taxes_id
msgid "Default taxes used when selling the product."
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__uom_id
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__uom_id
#: model:ir.model.fields,help:hotel.field_hotel_services__uom_id
msgid "Default unit of measure used for all stock operations."
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__uom_po_id
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__uom_po_id
#: model:ir.model.fields,help:hotel.field_hotel_services__uom_po_id
msgid ""
"Default unit of measure used for purchase orders. It must be in the same "
"category as the default unit of measure."
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__seller_ids
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__seller_ids
#: model:ir.model.fields,help:hotel.field_hotel_services__seller_ids
msgid "Define vendor pricelists."
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__qty_delivered_manual
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__qty_delivered_manual
msgid "Delivered Manually"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__qty_delivered
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__qty_delivered
msgid "Delivered Quantity"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__partner_shipping_id
msgid "Delivery Address"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__commitment_date
msgid "Delivery Date"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__delivery_count
msgid "Delivery Orders"
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__expected_date
msgid ""
"Delivery date you can promise to the customer, computed from the minimum "
"lead time of the order lines in case of Service products. In case of "
"shipping, the shipping policy of the order will be taken into account to "
"either use the minimum or maximum lead time of the order lines."
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__sale_delay
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__sale_delay
#: model:ir.model.fields,help:hotel.field_hotel_services__sale_delay
msgid ""
"Delivery lead time, in days. It's the number of days, promised to the "
"customer, between the confirmation of the sales order and the delivery."
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__route_ids
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__route_ids
#: model:ir.model.fields,help:hotel.field_hotel_services__route_ids
msgid ""
"Depending on the modules installed, this will allow you to define the route "
"of the product: whether it will be bought, manufactured, replenished on "
"order, etc."
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__name
#: model:ir.model.fields,field_description:hotel.field_hotel_room__description
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__description
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__description
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__name
#: model:ir.model.fields,field_description:hotel.field_hotel_services__description
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_room_type_form
msgid "Description"
msgstr "Descripción"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__description_pickingout
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__description_pickingout
#: model:ir.model.fields,field_description:hotel.field_hotel_services__description_pickingout
msgid "Description on Delivery Orders"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__description_picking
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__description_picking
#: model:ir.model.fields,field_description:hotel.field_hotel_services__description_picking
msgid "Description on Picking"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__description_pickingin
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__description_pickingin
#: model:ir.model.fields,field_description:hotel.field_hotel_services__description_pickingin
msgid "Description on Receptions"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__discount
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__discount
msgid "Discount (%)"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_floor__display_name
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__display_name
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__display_name
#: model:ir.model.fields,field_description:hotel.field_hotel_room__display_name
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__display_name
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__display_name
#: model:ir.model.fields,field_description:hotel.field_hotel_room_images__display_name
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__display_name
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__display_name
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__display_name
#: model:ir.model.fields,field_description:hotel.field_hotel_services__display_name
msgid "Display Name"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__display_qty_widget
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__display_qty_widget
msgid "Display Qty Widget"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__display_type
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__display_type
msgid "Display Type"
msgstr ""

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "Do you want to confirm?"
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio_line__is_downpayment
#: model:ir.model.fields,help:hotel.field_hotel_service_line__is_downpayment
msgid ""
"Down payments are made when creating invoices from a sales order. They are "
"not copied when duplicating a sales order."
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__effective_date
msgid "Effective Date"
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__tracking
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__tracking
#: model:ir.model.fields,help:hotel.field_hotel_services__tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__expected_date
msgid "Expected Date"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__property_account_expense_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__property_account_expense_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__property_account_expense_categ_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__property_account_expense_categ_id
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__property_account_expense_categ_id
#: model:ir.model.fields,field_description:hotel.field_hotel_services__property_account_expense_id
msgid "Expense Account"
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__expense_policy
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__expense_policy
#: model:ir.model.fields,help:hotel.field_hotel_services__expense_policy
msgid ""
"Expenses and vendor bills can be re-invoiced to a customer.With this option,"
" a validated expense can be re-invoice to a customer at its cost or sales "
"price."
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__validity_date
msgid "Expiration"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__product_no_variant_attribute_value_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__product_no_variant_attribute_value_ids
msgid "Extra Values"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__fiscal_position_id
msgid "Fiscal Position"
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__fiscal_position_id
msgid ""
"Fiscal positions are used to adapt taxes and accounts for particular "
"customers or sales orders/invoices.The default value comes from the "
"customer."
msgstr ""

#. module: hotel
#: model:ir.model,name:hotel.model_hotel_floor
#: model:ir.ui.menu,name:hotel.menu_open_hotel_floor_form_tree
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_room_form
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_room_tree
msgid "Floor"
msgstr "Piso"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_floor__name
msgid "Floor Name"
msgstr "Nombre de Piso"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__floor_id
msgid "Floor No"
msgstr "Piso No"

#. module: hotel
#: model:ir.actions.act_window,name:hotel.open_hotel_floor_form_tree
msgid "Floor Structure"
msgstr "Estructura del Piso"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__folio_id
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__folio_id
#: model:ir.ui.menu,name:hotel.menu_open_hotel_folio1_form_tree
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "Folio"
msgstr "Folio"

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "Folio Line"
msgstr "Folio Línea"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__message_follower_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room__message_follower_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__message_follower_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__message_follower_ids
msgid "Followers"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__message_channel_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room__message_channel_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__message_channel_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__message_channel_ids
msgid "Followers (Channels)"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__message_partner_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room__message_partner_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__message_partner_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__removal_strategy_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__removal_strategy_id
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__removal_strategy_id
msgid "Force Removal Strategy"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__virtual_available
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__virtual_available
#: model:ir.model.fields,field_description:hotel.field_hotel_services__virtual_available
msgid "Forecast Quantity"
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__virtual_available
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__virtual_available
#: model:ir.model.fields,help:hotel.field_hotel_services__virtual_available
msgid ""
"Forecast quantity (computed as Quantity On Hand - Outgoing + Incoming)\n"
"In a context with a single Stock Location, this includes goods stored in this location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__free_qty
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__free_qty
#: model:ir.model.fields,help:hotel.field_hotel_services__free_qty
msgid ""
"Forecast quantity (computed as Quantity On Hand - reserved quantity)\n"
"In a context with a single Stock Location, this includes goods stored in this location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__free_qty_today
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__free_qty_today
msgid "Free Qty Today"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__free_qty
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__free_qty
#: model:ir.model.fields,field_description:hotel.field_hotel_services__free_qty
msgid "Free To Use Quantity "
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__packaging_ids
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__packaging_ids
#: model:ir.model.fields,help:hotel.field_hotel_services__packaging_ids
msgid "Gives the different ways to package the same product."
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__sequence
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__sequence
#: model:ir.model.fields,help:hotel.field_hotel_services__sequence
msgid "Gives the sequence order when displaying a product list"
msgstr ""

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "Guest Name"
msgstr "Nombre del Huésped"

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "History"
msgstr "Historia"

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_floor_form
msgid "Hotel Floor"
msgstr "Piso de hotel"

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_floor_tree
msgid "Hotel Floors"
msgstr "Pisos de hotel"

#. module: hotel
#: model:ir.actions.act_window,name:hotel.open_hotel_folio1_form_tree
msgid "Hotel Folio"
msgstr "hotel folio"

#. module: hotel
#: model:ir.model,name:hotel.model_hotel_folio
msgid "Hotel Folio Inherit for Electricity Meter Reading"
msgstr ""

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_tree_view
msgid "Hotel Folio1"
msgstr "Hotel Folio1"

#. module: hotel
#: model:ir.ui.menu,name:hotel.hotel_management_menu
msgid "Hotel Management"
msgstr "Gestión hotelera"

#. module: hotel
#: model:res.groups,name:hotel.group_hotel_user
msgid "Hotel Management / User"
msgstr "Gestión Hotelera / Usuario"

#. module: hotel
#: model:res.groups,name:hotel.group_hotel_manager
msgid "Hotel Management/ Manager"
msgstr "Gestión hotelera/ gerente"

#. module: hotel
#: model:ir.actions.act_window,name:hotel.action_hotel_room_form
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_room_tree
msgid "Hotel Room"
msgstr "Habitación de hotel"

#. module: hotel
#: model:ir.actions.act_window,name:hotel.action_hotel_room_amenities_view_form
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_room_amenities_list
msgid "Hotel Room Amenities"
msgstr "Instalaciónes de Habitaciónes del hotel"

#. module: hotel
#: model:ir.actions.act_window,name:hotel.action_hotel_room_amenities_type_view_form
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_room_amenities_type_form
msgid "Hotel Room Amenities Type"
msgstr "Tipo de Instalaciónes de Habitaciónes del hotel "

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_room_images_form
#: model_terms:ir.ui.view,arch_db:hotel.view_room_images_tree
msgid "Hotel Room Image Gallery"
msgstr ""

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_room_type_form
msgid "Hotel Room Type"
msgstr "Tipo de Habitación de hotel"

#. module: hotel
#: model:ir.actions.act_window,name:hotel.action_hotel_services_form
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_services_tree
msgid "Hotel Services"
msgstr "Servicios de hotel"

#. module: hotel
#: model:ir.model,name:hotel.model_hotel_services
msgid "Hotel Services and its charges"
msgstr "Servicios de hotel y sus cargos"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_floor__id
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__id
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__id
#: model:ir.model.fields,field_description:hotel.field_hotel_room__id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_images__id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__id
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__id
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__id
#: model:ir.model.fields,field_description:hotel.field_hotel_services__id
msgid "ID"
msgstr "ID"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__activity_exception_icon
#: model:ir.model.fields,field_description:hotel.field_hotel_room__activity_exception_icon
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__activity_exception_icon
#: model:ir.model.fields,field_description:hotel.field_hotel_services__activity_exception_icon
msgid "Icon"
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__activity_exception_icon
#: model:ir.model.fields,help:hotel.field_hotel_room__activity_exception_icon
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__activity_exception_icon
#: model:ir.model.fields,help:hotel.field_hotel_services__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__message_needaction
#: model:ir.model.fields,help:hotel.field_hotel_folio__message_unread
#: model:ir.model.fields,help:hotel.field_hotel_room__message_needaction
#: model:ir.model.fields,help:hotel.field_hotel_room__message_unread
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__message_needaction
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__message_unread
#: model:ir.model.fields,help:hotel.field_hotel_services__message_needaction
#: model:ir.model.fields,help:hotel.field_hotel_services__message_unread
msgid "If checked, new messages require your attention."
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__message_has_error
#: model:ir.model.fields,help:hotel.field_hotel_folio__message_has_sms_error
#: model:ir.model.fields,help:hotel.field_hotel_room__message_has_error
#: model:ir.model.fields,help:hotel.field_hotel_room__message_has_sms_error
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__message_has_error
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__message_has_sms_error
#: model:ir.model.fields,help:hotel.field_hotel_services__message_has_error
#: model:ir.model.fields,help:hotel.field_hotel_services__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__active
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__active
#: model:ir.model.fields,help:hotel.field_hotel_services__active
msgid ""
"If unchecked, it will allow you to hide the product without removing it."
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__pricelist_id
msgid "If you change the pricelist, only newly added lines will be affected."
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__picking_policy
msgid ""
"If you deliver all products at once, the delivery order will be scheduled "
"based on the greatest product lead time. Otherwise, it will be based on the "
"shortest."
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__image_1920
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__image_1920
#: model:ir.model.fields,field_description:hotel.field_hotel_room_images__img
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__img_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__image_1920
msgid "Image"
msgstr "Image"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__image_1024
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__image_1024
#: model:ir.model.fields,field_description:hotel.field_hotel_services__image_1024
msgid "Image 1024"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__image_128
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__image_128
#: model:ir.model.fields,field_description:hotel.field_hotel_services__image_128
msgid "Image 128"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__image_256
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__image_256
#: model:ir.model.fields,field_description:hotel.field_hotel_services__image_256
msgid "Image 256"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__image_512
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__image_512
#: model:ir.model.fields,field_description:hotel.field_hotel_services__image_512
msgid "Image 512"
msgstr ""

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_room_type_form
msgid "Image Gallery"
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__standard_price
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__standard_price
#: model:ir.model.fields,help:hotel.field_hotel_services__standard_price
msgid ""
"In Standard Price & AVCO: value of the product (automatically computed in AVCO).\n"
"        In FIFO: value of the last unit that left the stock (automatically computed).\n"
"        Used to value the product when the purchase cost is not known (e.g. inventory adjustment).\n"
"        Used to compute margins on sale orders."
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__property_account_income_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__property_account_income_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__property_account_income_categ_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__property_account_income_categ_id
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__property_account_income_categ_id
#: model:ir.model.fields,field_description:hotel.field_hotel_services__property_account_income_id
msgid "Income Account"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__incoming_qty
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__incoming_qty
#: model:ir.model.fields,field_description:hotel.field_hotel_services__incoming_qty
msgid "Incoming"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__incoterm
msgid "Incoterm"
msgstr ""

#. module: hotel
#: model:ir.model,name:hotel.model_sale_order_line
msgid "Inherit Order Line"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__default_code
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__default_code
#: model:ir.model.fields,field_description:hotel.field_hotel_services__default_code
msgid "Internal Reference"
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__barcode
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__barcode
#: model:ir.model.fields,help:hotel.field_hotel_services__barcode
msgid "International Article Number used for product identification."
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__incoterm
msgid ""
"International Commercial Terms are a series of predefined commercial terms "
"used in international transactions."
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__property_stock_inventory
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__property_stock_inventory
#: model:ir.model.fields,field_description:hotel.field_hotel_services__property_stock_inventory
msgid "Inventory Location"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__valuation
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__valuation
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__property_valuation
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__property_valuation
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__property_valuation
#: model:ir.model.fields,field_description:hotel.field_hotel_services__valuation
msgid "Inventory Valuation"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__partner_invoice_id
msgid "Invoice Address"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__invoice_count
msgid "Invoice Count"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__invoice_lines
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__invoice_lines
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "Invoice Lines"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__invoice_status
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__invoice_status
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__invoice_status
msgid "Invoice Status"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__qty_invoiced
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__qty_invoiced
msgid "Invoiced Quantity"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__invoice_ids
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "Invoices"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__invoice_policy
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__invoice_policy
#: model:ir.model.fields,field_description:hotel.field_hotel_services__invoice_policy
msgid "Invoicing Policy"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__message_is_follower
#: model:ir.model.fields,field_description:hotel.field_hotel_room__message_is_follower
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__message_is_follower
#: model:ir.model.fields,field_description:hotel.field_hotel_services__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__is_mto
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__is_mto
msgid "Is Mto"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__is_product_variant
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__is_product_variant
#: model:ir.model.fields,field_description:hotel.field_hotel_services__is_product_variant
msgid "Is Product Variant"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__rental
msgid "Is Rental"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__isroom
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__isroom
#: model:ir.model.fields,field_description:hotel.field_hotel_services__isroom
#: model:ir.model.fields,field_description:hotel.field_product_product__isroom
msgid "Is Room"
msgstr "Is Habitación"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__isroomtype
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__isroomtype
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__isroomtype
#: model:ir.model.fields,field_description:hotel.field_product_category__isroomtype
msgid "Is Room Type"
msgstr "Is Tipo de Habitación"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_services__isservice
msgid "Is Service"
msgstr "Is  servicio"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__isservicetype
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__isservicetype
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__isservicetype
#: model:ir.model.fields,field_description:hotel.field_product_category__isservicetype
msgid "Is Service Type"
msgstr "Is  servicio Tipo"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__isservice
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__isservice
#: model:ir.model.fields,field_description:hotel.field_product_product__isservice
msgid "Is Service id"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__has_configurable_attributes
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__has_configurable_attributes
#: model:ir.model.fields,field_description:hotel.field_hotel_services__has_configurable_attributes
msgid "Is a configurable product"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__is_downpayment
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__is_downpayment
msgid "Is a down payment"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__isamenitype
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__isamenitype
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__isamenitype
#: model:ir.model.fields,field_description:hotel.field_product_category__isamenitype
msgid "Is amenities Type"
msgstr "Is Tipo de Instalación"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__iscategid
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__iscategid
#: model:ir.model.fields,field_description:hotel.field_hotel_services__iscategid
#: model:ir.model.fields,field_description:hotel.field_product_product__iscategid
msgid "Is categ id"
msgstr "Is categ id"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__is_expense
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__is_expense
msgid "Is expense"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__is_expired
msgid "Is expired"
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio_line__is_expense
#: model:ir.model.fields,help:hotel.field_hotel_service_line__is_expense
msgid ""
"Is true if the sales order line comes from an expense or a vendor bills"
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__property_account_income_id
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__property_account_income_id
#: model:ir.model.fields,help:hotel.field_hotel_services__property_account_income_id
msgid ""
"Keep this field empty to use the default value from the product category."
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__property_account_expense_id
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__property_account_expense_id
#: model:ir.model.fields,help:hotel.field_hotel_services__property_account_expense_id
msgid ""
"Keep this field empty to use the default value from the product category. If"
" anglo-saxon accounting with automated valuation method is configured, the "
"expense account on the product category will be used."
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_floor____last_update
#: model:ir.model.fields,field_description:hotel.field_hotel_folio____last_update
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line____last_update
#: model:ir.model.fields,field_description:hotel.field_hotel_room____last_update
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities____last_update
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type____last_update
#: model:ir.model.fields,field_description:hotel.field_hotel_room_images____last_update
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type____last_update
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line____last_update
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type____last_update
#: model:ir.model.fields,field_description:hotel.field_hotel_services____last_update
msgid "Last Modified on"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_floor__write_uid
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__write_uid
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__write_uid
#: model:ir.model.fields,field_description:hotel.field_hotel_room__write_uid
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__write_uid
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__write_uid
#: model:ir.model.fields,field_description:hotel.field_hotel_room_images__write_uid
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__write_uid
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__write_uid
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__write_uid
#: model:ir.model.fields,field_description:hotel.field_hotel_services__write_uid
msgid "Last Updated by"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_floor__write_date
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__write_date
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__write_date
#: model:ir.model.fields,field_description:hotel.field_hotel_room__write_date
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__write_date
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__write_date
#: model:ir.model.fields,field_description:hotel.field_hotel_room_images__write_date
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__write_date
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__write_date
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__write_date
#: model:ir.model.fields,field_description:hotel.field_hotel_services__write_date
msgid "Last Updated on"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__customer_lead
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__customer_lead
msgid "Lead Time"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__location_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__location_id
#: model:ir.model.fields,field_description:hotel.field_hotel_services__location_id
msgid "Location"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__message_main_attachment_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room__message_main_attachment_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__message_main_attachment_id
#: model:ir.model.fields,field_description:hotel.field_hotel_services__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "Manual Description"
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__valuation
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__valuation
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities_type__property_valuation
#: model:ir.model.fields,help:hotel.field_hotel_room_type__property_valuation
#: model:ir.model.fields,help:hotel.field_hotel_service_type__property_valuation
#: model:ir.model.fields,help:hotel.field_hotel_services__valuation
msgid ""
"Manual: The accounting entries to value the inventory are not posted automatically.\n"
"        Automated: An accounting entry is automatically created to value the inventory when a product enters or leaves the company.\n"
"        "
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__service_type
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__service_type
#: model:ir.model.fields,help:hotel.field_hotel_services__service_type
msgid ""
"Manually set quantities on order: Invoice based on the manually entered quantity, without creating an analytic account.\n"
"Timesheets on contract: Invoice based on the tracked hours on the related timesheet.\n"
"Create a task and track hours: Create a task on the sales order validation and track the work hours."
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__max_adult
msgid "Max Adult"
msgstr "Max Adulto"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__max_child
msgid "Max Child"
msgstr "Max Niño"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__medium_id
msgid "Medium"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__message_has_error
#: model:ir.model.fields,field_description:hotel.field_hotel_room__message_has_error
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__message_has_error
#: model:ir.model.fields,field_description:hotel.field_hotel_services__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__sale_line_warn_msg
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__sale_line_warn_msg
#: model:ir.model.fields,field_description:hotel.field_hotel_services__sale_line_warn_msg
msgid "Message for Sales Order Line"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__message_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room__message_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__message_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__message_ids
msgid "Messages"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__qty_delivered_method
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__qty_delivered_method
msgid "Method to update delivered qty"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__orderpoint_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__orderpoint_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__orderpoint_ids
msgid "Minimum Stock Rules"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__name
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__name
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__name
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__name
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__name
#: model:ir.model.fields,field_description:hotel.field_hotel_services__name
msgid "Name"
msgstr "Nombre"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__signed_by
msgid "Name of the person that signed the SO."
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__activity_date_deadline
#: model:ir.model.fields,field_description:hotel.field_hotel_room__activity_date_deadline
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__activity_date_deadline
#: model:ir.model.fields,field_description:hotel.field_hotel_services__activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__activity_summary
#: model:ir.model.fields,field_description:hotel.field_hotel_room__activity_summary
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__activity_summary
#: model:ir.model.fields,field_description:hotel.field_hotel_services__activity_summary
msgid "Next Activity Summary"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__activity_type_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room__activity_type_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__activity_type_id
#: model:ir.model.fields,field_description:hotel.field_hotel_services__activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__note
msgid "Note"
msgstr "Notas"

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "Notes"
msgstr "Notas"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__message_needaction_counter
#: model:ir.model.fields,field_description:hotel.field_hotel_room__message_needaction_counter
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__message_needaction_counter
#: model:ir.model.fields,field_description:hotel.field_hotel_services__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio_line__customer_lead
#: model:ir.model.fields,help:hotel.field_hotel_service_line__customer_lead
msgid ""
"Number of days between the order confirmation and the shipping of the "
"products to the customer"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__message_has_error_counter
#: model:ir.model.fields,field_description:hotel.field_hotel_room__message_has_error_counter
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__message_has_error_counter
#: model:ir.model.fields,field_description:hotel.field_hotel_services__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__message_needaction_counter
#: model:ir.model.fields,help:hotel.field_hotel_room__message_needaction_counter
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__message_needaction_counter
#: model:ir.model.fields,help:hotel.field_hotel_services__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__message_has_error_counter
#: model:ir.model.fields,help:hotel.field_hotel_room__message_has_error_counter
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__message_has_error_counter
#: model:ir.model.fields,help:hotel.field_hotel_services__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__pricelist_item_count
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__pricelist_item_count
#: model:ir.model.fields,field_description:hotel.field_hotel_services__pricelist_item_count
msgid "Number of price rules"
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__message_unread_counter
#: model:ir.model.fields,help:hotel.field_hotel_room__message_unread_counter
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__message_unread_counter
#: model:ir.model.fields,help:hotel.field_hotel_services__message_unread_counter
msgid "Number of unread messages"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__require_payment
msgid "Online Payment"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__require_signature
msgid "Online Signature"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__date_order
msgid "Order Date"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__order_id
msgid "Order Id"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__order_line
msgid "Order Lines"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__name
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__order_id
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__order_id
msgid "Order Reference"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__order_reserve_invoice_ids
msgid "Order Reservation Invoices"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__state
msgid "Order State"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__state
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__state
msgid "Order Status"
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__invoice_policy
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__invoice_policy
#: model:ir.model.fields,help:hotel.field_hotel_services__invoice_policy
msgid ""
"Ordered Quantity: Invoice quantities ordered by the customer.\n"
"Delivered Quantity: Invoice quantities delivered to the customer."
msgstr ""

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "Other Data"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__outgoing_qty
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__outgoing_qty
#: model:ir.model.fields,field_description:hotel.field_hotel_services__outgoing_qty
msgid "Outgoing"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__product_packaging
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__product_packaging
msgid "Package"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__parent_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__parent_id
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__parent_id
msgid "Parent Category"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__parent_path
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__parent_path
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__parent_path
msgid "Parent Path"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__reference
msgid "Payment Ref."
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__payment_term_id
msgid "Payment Terms"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__access_url
msgid "Portal Access URL"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__price
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__price
#: model:ir.model.fields,field_description:hotel.field_hotel_services__price
msgid "Price"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__price_reduce
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__price_reduce
msgid "Price Reduce"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__price_reduce_taxexcl
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__price_reduce_taxexcl
msgid "Price Reduce Tax excl"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__price_reduce_taxinc
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__price_reduce_taxinc
msgid "Price Reduce Tax inc"
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__list_price
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__list_price
#: model:ir.model.fields,help:hotel.field_hotel_services__list_price
msgid "Price at which the product is sold to customers."
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__pricelist_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room__pricelist_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__pricelist_id
#: model:ir.model.fields,field_description:hotel.field_hotel_services__pricelist_id
msgid "Pricelist"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__procurement_group_id
msgid "Procurement Group"
msgstr ""

#. module: hotel
#: model:ir.model,name:hotel.model_product_product
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__product_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room__product_variant_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__product_variant_id
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__product_id
#: model:ir.model.fields,field_description:hotel.field_hotel_services__product_variant_id
msgid "Product"
msgstr "Producto"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__attribute_line_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__attribute_line_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__attribute_line_ids
msgid "Product Attributes"
msgstr ""

#. module: hotel
#: model:ir.model,name:hotel.model_product_category
#: model:ir.model.fields,field_description:hotel.field_hotel_room__categ_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__categ_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__room_categ_id
#: model:ir.model.fields,field_description:hotel.field_hotel_services__categ_id
msgid "Product Category"
msgstr "Categoría de Producto"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__packaging_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__packaging_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__packaging_ids
msgid "Product Packages"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__product_template_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room__product_tmpl_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__product_tmpl_id
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__product_template_id
#: model:ir.model.fields,field_description:hotel.field_hotel_services__product_tmpl_id
msgid "Product Template"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__product_type
#: model:ir.model.fields,field_description:hotel.field_hotel_room__type
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__type
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__product_type
#: model:ir.model.fields,field_description:hotel.field_hotel_services__type
msgid "Product Type"
msgstr ""

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_room_amenities_form
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_room_form
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_services_form
msgid "Product Variant"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__product_id
msgid "Product_id"
msgstr "Producto_id"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__property_stock_production
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__property_stock_production
#: model:ir.model.fields,field_description:hotel.field_hotel_services__property_stock_production
msgid "Production Location"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__product_variant_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__product_variant_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__product_variant_ids
msgid "Products"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__lst_price
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__lst_price
#: model:ir.model.fields,field_description:hotel.field_hotel_services__lst_price
msgid "Public Price"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__description_purchase
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__description_purchase
#: model:ir.model.fields,field_description:hotel.field_hotel_services__description_purchase
msgid "Purchase Description"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__uom_po_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__uom_po_id
#: model:ir.model.fields,field_description:hotel.field_hotel_services__uom_po_id
msgid "Purchase Unit of Measure"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__putaway_rule_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__putaway_rule_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__putaway_rule_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__putaway_rule_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__putaway_rule_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__putaway_rule_ids
msgid "Putaway Rules"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__qty_available_today
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__qty_available_today
msgid "Qty Available Today"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__qty_to_deliver
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__qty_to_deliver
msgid "Qty To Deliver"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__product_uom_qty
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__product_uom_qty
#: model:ir.model.fields,field_description:hotel.field_sale_order_line__product_uom_qty
msgid "Quantity"
msgstr "Cantidad"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__qty_available
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__qty_available
#: model:ir.model.fields,field_description:hotel.field_hotel_services__qty_available
msgid "Quantity On Hand"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__quantity_svl
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__quantity_svl
#: model:ir.model.fields,field_description:hotel.field_hotel_services__quantity_svl
msgid "Quantity Svl"
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__incoming_qty
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__incoming_qty
#: model:ir.model.fields,help:hotel.field_hotel_services__incoming_qty
msgid ""
"Quantity of planned incoming products.\n"
"In a context with a single Stock Location, this includes goods arriving to this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods arriving to the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods arriving to any Stock Location with 'internal' type."
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__outgoing_qty
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__outgoing_qty
#: model:ir.model.fields,help:hotel.field_hotel_services__outgoing_qty
msgid ""
"Quantity of planned outgoing products.\n"
"In a context with a single Stock Location, this includes goods leaving this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods leaving the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods leaving any Stock Location with 'internal' type."
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__expense_policy
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__expense_policy
#: model:ir.model.fields,field_description:hotel.field_hotel_services__expense_policy
msgid "Re-Invoice Expenses"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__visible_expense_policy
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__visible_expense_policy
#: model:ir.model.fields,field_description:hotel.field_hotel_services__visible_expense_policy
msgid "Re-Invoice Policy visible"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__code
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__code
#: model:ir.model.fields,field_description:hotel.field_hotel_services__code
msgid "Reference"
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__origin
msgid "Reference of the document that generated this sales order request."
msgstr ""

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "Related invoices"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__remaining_validity_days
msgid "Remaining Days Before Expiration"
msgstr ""

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "Rent"
msgstr ""

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "Rent(UOM)"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__reordering_max_qty
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__reordering_max_qty
#: model:ir.model.fields,field_description:hotel.field_hotel_services__reordering_max_qty
msgid "Reordering Max Qty"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__reordering_min_qty
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__reordering_min_qty
#: model:ir.model.fields,field_description:hotel.field_hotel_services__reordering_min_qty
msgid "Reordering Min Qty"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__nbr_reordering_rules
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__nbr_reordering_rules
#: model:ir.model.fields,field_description:hotel.field_hotel_services__nbr_reordering_rules
msgid "Reordering Rules"
msgstr ""

#. module: hotel
#: model:ir.ui.menu,name:hotel.hotel_report_menu
msgid "Reports"
msgstr "Informes"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__require_signature
msgid ""
"Request a online signature to the customer in order to confirm orders "
"automatically."
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__require_payment
msgid ""
"Request an online payment to the customer in order to confirm orders "
"automatically."
msgstr ""

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "Reserve Order Invoices"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__responsible_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__responsible_id
#: model:ir.model.fields,field_description:hotel.field_hotel_services__responsible_id
msgid "Responsible"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__activity_user_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room__activity_user_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__activity_user_id
#: model:ir.model.fields,field_description:hotel.field_hotel_services__activity_user_id
msgid "Responsible User"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__room_amenities
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_room_form
msgid "Room Amenities"
msgstr "Habitación Instalaciónes"

#. module: hotel
#: model:ir.ui.menu,name:hotel.menu_open_hotel_room_type_form_tree
msgid "Room Categories"
msgstr "Habitación Categorías"

#. module: hotel
#: model:ir.ui.menu,name:hotel.menu_open_hotel_room_type_parent
msgid "Room Definations"
msgstr "Definición de Habitaciónes"

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "Room Lines"
msgstr ""

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "Room No"
msgstr "No. de Habitación"

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_room_tree
msgid "Room Status"
msgstr "Estado de Habitación"

#. module: hotel
#: model:ir.actions.act_window,name:hotel.open_hotel_room_type_form_tree
#: model:ir.model,name:hotel.model_hotel_room_type
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__categ_id
msgid "Room Type"
msgstr "Tipo de Habitación"

#. module: hotel
#: model:ir.model,name:hotel.model_hotel_room_amenities
msgid "Room amenities"
msgstr "Instalaciones de Habitación"

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_room_tree
msgid "Room rate"
msgstr "Tarifa de Habitación"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__room_lines
#: model:ir.ui.menu,name:hotel.menu_open_hotel_room_form
msgid "Rooms"
msgstr "Habitaciones"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__route_id
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__route_id
msgid "Route"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__route_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__route_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__route_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__route_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__route_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__route_ids
msgid "Routes"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__message_has_sms_error
#: model:ir.model.fields,field_description:hotel.field_hotel_room__message_has_sms_error
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__message_has_sms_error
#: model:ir.model.fields,field_description:hotel.field_hotel_services__message_has_sms_error
msgid "SMS Delivery error"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__description_sale
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__description_sale
#: model:ir.model.fields,field_description:hotel.field_hotel_services__description_sale
msgid "Sales Description"
msgstr ""

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio_filter
msgid "Sales Order"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__sale_line_warn
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__sale_line_warn
#: model:ir.model.fields,field_description:hotel.field_hotel_services__sale_line_warn
msgid "Sales Order Line"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__list_price
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__list_price
#: model:ir.model.fields,field_description:hotel.field_hotel_services__list_price
msgid "Sales Price"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__team_id
msgid "Sales Team"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__user_id
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__salesman_id
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__salesman_id
msgid "Salesperson"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__scheduled_date
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__scheduled_date
msgid "Scheduled Date"
msgstr ""

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio_filter
msgid "Search Hotel Folio"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__access_token
msgid "Security Token"
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__categ_id
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__categ_id
#: model:ir.model.fields,help:hotel.field_hotel_services__categ_id
msgid "Select category for the current product"
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__sale_line_warn
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__sale_line_warn
#: model:ir.model.fields,help:hotel.field_hotel_services__sale_line_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_floor__sequence
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__sequence
#: model:ir.model.fields,field_description:hotel.field_hotel_room__sequence
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__sequence
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__sequence
#: model:ir.model.fields,field_description:hotel.field_hotel_services__sequence
msgid "Sequence"
msgstr ""

#. module: hotel
#: model:ir.ui.menu,name:hotel.menu_open_hotel_service_type_form_tree
msgid "Service Categories"
msgstr "Categorías de servicios"

#. module: hotel
#: model:ir.ui.menu,name:hotel.menu_open_hotel_service_type_form_tree_parent
msgid "Service Definations"
msgstr "Definición de servicios"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_services__service_id
msgid "Service Id"
msgstr ""

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "Service Line"
msgstr ""

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "Service Lines"
msgstr ""

#. module: hotel
#: model:ir.actions.act_window,name:hotel.open_hotel_service_type_form_tree
#: model:ir.model,name:hotel.model_hotel_service_type
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_service_type_form
msgid "Service Type"
msgstr "Tipo de servicio"

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_services_tree
msgid "Service rate"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__service_lines
#: model:ir.ui.menu,name:hotel.menu_open_hotel_services_form
msgid "Services"
msgstr "Servicios"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities_type__removal_strategy_id
#: model:ir.model.fields,help:hotel.field_hotel_room_type__removal_strategy_id
#: model:ir.model.fields,help:hotel.field_hotel_service_type__removal_strategy_id
msgid ""
"Set a specific removal strategy that will be used regardless of the source "
"location for this product category"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__picking_policy
msgid "Shipping Policy"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__shop_id
msgid "Shop"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__signature
msgid "Signature"
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__signature
msgid "Signature received through the portal."
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__signed_by
msgid "Signed By"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__signed_on
msgid "Signed On"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__sales_count
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__sales_count
#: model:ir.model.fields,field_description:hotel.field_hotel_services__sales_count
msgid "Sold"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__source_id
msgid "Source"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__origin
msgid "Source Document"
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__cost_method
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__cost_method
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities_type__property_cost_method
#: model:ir.model.fields,help:hotel.field_hotel_room_type__property_cost_method
#: model:ir.model.fields,help:hotel.field_hotel_service_type__property_cost_method
#: model:ir.model.fields,help:hotel.field_hotel_services__cost_method
msgid ""
"Standard Price: The products are valued at their standard cost defined on the product.\n"
"        Average Cost (AVCO): The products are valued at weighted average cost.\n"
"        First In First Out (FIFO): The products are valued supposing those that enter the company first will also leave it first.\n"
"        "
msgstr ""

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "States"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__state
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__state
#: model:ir.model.fields,field_description:hotel.field_hotel_services__state
#: model:ir.model.fields,field_description:hotel.field_product_product__state
msgid "Status"
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__activity_state
#: model:ir.model.fields,help:hotel.field_hotel_room__activity_state
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__activity_state
#: model:ir.model.fields,help:hotel.field_hotel_services__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__property_stock_account_input_categ_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__property_stock_account_input_categ_id
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__property_stock_account_input_categ_id
msgid "Stock Input Account"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__property_stock_journal
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__property_stock_journal
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__property_stock_journal
msgid "Stock Journal"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__stock_move_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__stock_move_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__stock_move_ids
msgid "Stock Move"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__move_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__move_ids
msgid "Stock Moves"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__property_stock_account_output_categ_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__property_stock_account_output_categ_id
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__property_stock_account_output_categ_id
msgid "Stock Output Account"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__stock_quant_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__stock_quant_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__stock_quant_ids
msgid "Stock Quant"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__property_stock_valuation_account_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__property_stock_valuation_account_id
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__property_stock_valuation_account_id
msgid "Stock Valuation Account"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__stock_valuation_layer_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__stock_valuation_layer_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__stock_valuation_layer_ids
msgid "Stock Valuation Layer"
msgstr ""

#. module: hotel
#: model:ir.model,name:hotel.model_hotel_room_images
msgid "Store multiple images for each room"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__price_subtotal
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__price_subtotal
msgid "Subtotal"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__table_order_invoice_ids
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "Table Order Invoices"
msgstr ""

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "Table Reservations"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__amount_by_group
msgid "Tax amount by group"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__amount_tax
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__tax_id
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__tax_id
msgid "Taxes"
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__valid_product_template_attribute_line_ids
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__valid_product_template_attribute_line_ids
#: model:ir.model.fields,help:hotel.field_hotel_services__valid_product_template_attribute_line_ids
msgid "Technical compute"
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio_line__display_type
#: model:ir.model.fields,help:hotel.field_hotel_service_line__display_type
msgid "Technical field for UX purpose."
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__pricelist_id
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__pricelist_id
#: model:ir.model.fields,help:hotel.field_hotel_services__pricelist_id
msgid ""
"Technical field. Used for searching on pricelists, not stored in database."
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__stock_move_ids
#: model:ir.model.fields,help:hotel.field_hotel_room__stock_quant_ids
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__stock_move_ids
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__stock_quant_ids
#: model:ir.model.fields,help:hotel.field_hotel_services__stock_move_ids
#: model:ir.model.fields,help:hotel.field_hotel_services__stock_quant_ids
msgid "Technical: used to compute quantities."
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__state
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__state
#: model:ir.model.fields,help:hotel.field_hotel_services__state
#: model:ir.model.fields,help:hotel.field_product_product__state
msgid "Tells the user if room is available of booked."
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__amount_untaxed
msgid "The amount without tax."
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__analytic_account_id
msgid "The analytic account related to a sales order."
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities_type__property_account_expense_categ_id
#: model:ir.model.fields,help:hotel.field_hotel_room_type__property_account_expense_categ_id
#: model:ir.model.fields,help:hotel.field_hotel_service_type__property_account_expense_categ_id
msgid ""
"The expense is accounted for when a vendor bill is validated, except in "
"anglo-saxon accounting with perpetual inventory valuation in which case the "
"expense (Cost of Goods Sold account) is recognized at the customer invoice "
"validation."
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities_type__product_count
#: model:ir.model.fields,help:hotel.field_hotel_room_type__product_count
#: model:ir.model.fields,help:hotel.field_hotel_service_type__product_count
msgid ""
"The number of products under this category (Does not consider the children "
"categories)"
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__reference
msgid "The payment communication of this sale order."
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__currency_rate
msgid ""
"The rate of the currency to the currency of rate 1 applicable at the date of"
" the order"
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__lst_price
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__lst_price
#: model:ir.model.fields,help:hotel.field_hotel_services__lst_price
msgid ""
"The sale price is managed from the product template. Click on the 'Configure"
" Variants' button to set the extra attribute prices."
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__amount_tax
msgid "The tax amount."
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities_type__property_account_income_categ_id
#: model:ir.model.fields,help:hotel.field_hotel_room_type__property_account_income_categ_id
#: model:ir.model.fields,help:hotel.field_hotel_service_type__property_account_income_categ_id
msgid "This account will be used when validating a customer invoice."
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room_images__img
msgid "This field holds the image for Room, limited to 1024x1024px"
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__campaign_id
msgid ""
"This is a name that helps you keep track of your different campaign efforts,"
" e.g. Fall_Drive, Christmas_Special"
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__commitment_date
msgid ""
"This is the delivery date promised to the customer. If set, the delivery "
"order will be scheduled based on this date rather than product lead times."
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__medium_id
msgid "This is the method of delivery, e.g. Postcard, Email, or Banner Ad"
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__source_id
msgid ""
"This is the source of the link, e.g. Search Engine, another domain, or name "
"of email list"
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__price_extra
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__price_extra
#: model:ir.model.fields,help:hotel.field_hotel_services__price_extra
msgid "This is the sum of the extra price of all attributes"
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__property_stock_production
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__property_stock_production
#: model:ir.model.fields,help:hotel.field_hotel_services__property_stock_production
msgid ""
"This stock location will be used, instead of the default one, as the source "
"location for stock moves generated by manufacturing orders."
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__property_stock_inventory
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__property_stock_inventory
#: model:ir.model.fields,help:hotel.field_hotel_services__property_stock_inventory
msgid ""
"This stock location will be used, instead of the default one, as the source "
"location for stock moves generated when you do an inventory."
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__responsible_id
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__responsible_id
#: model:ir.model.fields,help:hotel.field_hotel_services__responsible_id
msgid ""
"This user will be responsible of the next activities related to logistic "
"operations for this product."
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room_images__name
msgid "Title"
msgstr "Title"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__qty_to_invoice
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__qty_to_invoice
msgid "To Invoice Quantity"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__amount_total
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__price_total
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__price_total
msgid "Total"
msgstr "total"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__price_tax
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__price_tax
msgid "Total Tax"
msgstr ""

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_tree_view
msgid "Total amount"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__total_route_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__total_route_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__total_route_ids
msgid "Total routes"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__service_type
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__service_type
#: model:ir.model.fields,field_description:hotel.field_hotel_services__service_type
msgid "Track Service"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__tracking
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__tracking
#: model:ir.model.fields,field_description:hotel.field_hotel_services__tracking
msgid "Tracking"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__transaction_ids
msgid "Transactions"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__picking_ids
msgid "Transfers"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__type_name
msgid "Type Name"
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__activity_exception_decoration
#: model:ir.model.fields,help:hotel.field_hotel_room__activity_exception_decoration
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__activity_exception_decoration
#: model:ir.model.fields,help:hotel.field_hotel_services__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__price_unit
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__price_unit
msgid "Unit Price"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__product_uom
#: model:ir.model.fields,field_description:hotel.field_hotel_room__uom_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__uom_id
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__product_uom
#: model:ir.model.fields,field_description:hotel.field_hotel_services__uom_id
msgid "Unit of Measure"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__uom_name
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__uom_name
#: model:ir.model.fields,field_description:hotel.field_hotel_services__uom_name
msgid "Unit of Measure Name"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__message_unread
#: model:ir.model.fields,field_description:hotel.field_hotel_room__message_unread
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__message_unread
#: model:ir.model.fields,field_description:hotel.field_hotel_services__message_unread
msgid "Unread Messages"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__message_unread_counter
#: model:ir.model.fields,field_description:hotel.field_hotel_room__message_unread_counter
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__message_unread_counter
#: model:ir.model.fields,field_description:hotel.field_hotel_services__message_unread_counter
msgid "Unread Messages Counter"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__amount_untaxed
msgid "Untaxed Amount"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__untaxed_amount_to_invoice
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__untaxed_amount_to_invoice
msgid "Untaxed Amount To Invoice"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__untaxed_amount_invoiced
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__untaxed_amount_invoiced
msgid "Untaxed Invoiced Amount"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__valid_product_template_attribute_line_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__valid_product_template_attribute_line_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__valid_product_template_attribute_line_ids
msgid "Valid Product Attribute Lines"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__value_svl
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__value_svl
#: model:ir.model.fields,field_description:hotel.field_hotel_services__value_svl
msgid "Value Svl"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__image_variant_1920
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__image_variant_1920
#: model:ir.model.fields,field_description:hotel.field_hotel_services__image_variant_1920
msgid "Variant Image"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__image_variant_1024
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__image_variant_1024
#: model:ir.model.fields,field_description:hotel.field_hotel_services__image_variant_1024
msgid "Variant Image 1024"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__image_variant_128
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__image_variant_128
#: model:ir.model.fields,field_description:hotel.field_hotel_services__image_variant_128
msgid "Variant Image 128"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__image_variant_256
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__image_variant_256
#: model:ir.model.fields,field_description:hotel.field_hotel_services__image_variant_256
msgid "Variant Image 256"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__image_variant_512
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__image_variant_512
#: model:ir.model.fields,field_description:hotel.field_hotel_services__image_variant_512
msgid "Variant Image 512"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__price_extra
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__price_extra
#: model:ir.model.fields,field_description:hotel.field_hotel_services__price_extra
msgid "Variant Price Extra"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__variant_seller_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__variant_seller_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__variant_seller_ids
msgid "Variant Seller"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__supplier_taxes_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__supplier_taxes_id
#: model:ir.model.fields,field_description:hotel.field_hotel_services__supplier_taxes_id
msgid "Vendor Taxes"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__seller_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__seller_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__seller_ids
msgid "Vendors"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__virtual_available_at_date
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__virtual_available_at_date
msgid "Virtual Available At Date"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__volume
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__volume
#: model:ir.model.fields,field_description:hotel.field_hotel_services__volume
msgid "Volume"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__volume_uom_name
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__volume_uom_name
#: model:ir.model.fields,field_description:hotel.field_hotel_services__volume_uom_name
msgid "Volume unit of measure label"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__warehouse_id
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__warehouse_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room__warehouse_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__warehouse_id
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__warehouse_id
#: model:ir.model.fields,field_description:hotel.field_hotel_services__warehouse_id
msgid "Warehouse"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__website_message_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room__website_message_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__website_message_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__website_message_ids
#: model:ir.model.fields,help:hotel.field_hotel_room__website_message_ids
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__website_message_ids
#: model:ir.model.fields,help:hotel.field_hotel_services__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__weight
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__weight
#: model:ir.model.fields,field_description:hotel.field_hotel_services__weight
msgid "Weight"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__weight_uom_name
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__weight_uom_name
#: model:ir.model.fields,field_description:hotel.field_hotel_services__weight_uom_name
msgid "Weight unit of measure label"
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities_type__property_stock_valuation_account_id
#: model:ir.model.fields,help:hotel.field_hotel_room_type__property_stock_valuation_account_id
#: model:ir.model.fields,help:hotel.field_hotel_service_type__property_stock_valuation_account_id
msgid ""
"When automated inventory valuation is enabled on a product, this account "
"will hold the current value of the products."
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities_type__property_stock_account_input_categ_id
#: model:ir.model.fields,help:hotel.field_hotel_room_type__property_stock_account_input_categ_id
#: model:ir.model.fields,help:hotel.field_hotel_service_type__property_stock_account_input_categ_id
msgid ""
"When doing automated inventory valuation, counterpart journal items for all incoming stock moves will be posted in this account,\n"
"                unless there is a specific valuation account set on the source location. This is the default value for all products in this category.\n"
"                It can also directly be set on each product."
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities_type__property_stock_account_output_categ_id
#: model:ir.model.fields,help:hotel.field_hotel_room_type__property_stock_account_output_categ_id
#: model:ir.model.fields,help:hotel.field_hotel_service_type__property_stock_account_output_categ_id
msgid ""
"When doing automated inventory valuation, counterpart journal items for all outgoing stock moves will be posted in this account,\n"
"                unless there is a specific valuation account set on the destination location. This is the default value for all products in this category.\n"
"                It can also directly be set on each product."
msgstr ""

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities_type__property_stock_journal
#: model:ir.model.fields,help:hotel.field_hotel_room_type__property_stock_journal
#: model:ir.model.fields,help:hotel.field_hotel_service_type__property_stock_journal
msgid ""
"When doing automated inventory valuation, this is the Accounting Journal in "
"which entries will be automatically posted when stock moves are processed."
msgstr ""

#. module: hotel
#: model:ir.model,name:hotel.model_hotel_room_amenities_type
msgid "amenities Type"
msgstr "Tipo de Instalaciones"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__cat_id
msgid "category"
msgstr "Categoría"

#. module: hotel
#: model:ir.model,name:hotel.model_hotel_service_line
msgid "hotel Service line"
msgstr "Línea de servicio de hotel"

#. module: hotel
#: model:ir.model,name:hotel.model_hotel_folio_line
msgid "hotel folio1 room line"
msgstr "hotel folio1 Línea de Habitación"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__hotel_reservation_line_id
msgid "hotel reservation line id="
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room_images__room_images_id
msgid "img_ids"
msgstr ""

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_room_form
msgid "max adult"
msgstr "Max Adulto"

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_room_form
msgid "max child"
msgstr "Max Niño"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__order_line_id
msgid "order_line_id"
msgstr "order_line_id"

#. module: hotel
#: model:ir.model,name:hotel.model_hotel_room
msgid "room Inherit "
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__service_line_id
msgid "service_line_id"
msgstr "servicio_Línea_id"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__amount_by_group
msgid "type: [(name, amount, base, formated amount, formated base)]"
msgstr ""
