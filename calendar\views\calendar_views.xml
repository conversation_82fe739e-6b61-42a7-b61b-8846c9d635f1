<?xml version="1.0"?>
<odoo>

    <!-- Calendar Events Types : Views and Actions -->
    <record id="view_calendar_event_type_tree" model="ir.ui.view">
        <field name="name">calendar.event.type</field>
        <field name="model">calendar.event.type</field>
        <field name="arch" type="xml">
            <tree string="Meeting Types" sample="1" editable="bottom">
                <field name="name"/>
            </tree>
        </field>
    </record>

    <record id="action_calendar_event_type" model="ir.actions.act_window">
        <field name="name">Meeting Types</field>
        <field name="res_model">calendar.event.type</field>
        <field name="view_id" ref="view_calendar_event_type_tree"/>
    </record>

    <!-- Calendar Alarm : -->
    <record id="view_calendar_alarm_tree" model="ir.ui.view">
        <field name="name">calendar.alarm.tree</field>
        <field name="model">calendar.alarm</field>
        <field name="arch" type="xml">
            <tree string="Calendar Alarm" sample="1">
                <field name="name" column_invisible="True"/>
                <field name="alarm_type"/>
                <field name="duration"/>
                <field name="interval"/>
            </tree>
        </field>
    </record>

    <record id="calendar_alarm_view_form" model="ir.ui.view">
        <field name="name">calendar.alarm.form</field>
        <field name="model">calendar.alarm</field>
        <field name="arch" type="xml">
            <form string="Calendar Alarm">
                <sheet>
                    <group>
                        <group name="left_details">
                            <field name="name" invisible="1"/>
                            <field name="alarm_type"/>
                            <field name="mail_template_id" context="{'default_model': 'calendar.event'}"
                                   invisible="alarm_type != 'email'"
                                   required="alarm_type == 'email'"/>
                            <field name="body" invisible="alarm_type != 'notification'"/>
                        </group>
                        <group name="right_details">
                            <label for="duration"/>
                            <div class="o_row">
                                <field name="duration"/>
                                <field name="interval"/>
                            </div>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="action_calendar_alarm" model="ir.actions.act_window">
        <field name="name">Calendar Alarm</field>
        <field name="res_model">calendar.alarm</field>
        <field name="view_mode">tree,form</field>
        <field name="view_id" ref="view_calendar_alarm_tree"/>
    </record>

    <!-- Calendar Events : Views and Actions  -->
    <record id="view_calendar_event_tree" model="ir.ui.view">
        <field name="name">calendar.event.tree</field>
        <field name="model">calendar.event</field>
        <field name="arch" type="xml">
            <tree string="Meetings" sample="1" multi_edit="1">
                <header>
                    <button name="action_open_composer" type="object" context="{'default_composition_mode':'mass_mail'}"
                            string="Send Mail"/>
                </header>
                <field name="name" string="Subject" decoration-bf="1" readonly="recurrency"/>
                <field name="start" string="Start Date" readonly="1"/>
                <field name="stop" string="End Date" readonly="1"/>
                <field name="user_id" widget="many2one_avatar_user" readonly="recurrency" optional="hide"/>
                <field name="partner_ids" widget="many2many_tags" readonly="recurrency" optional="show"/>
                <field name="alarm_ids" widget="many2many_tags" optional="hide" readonly="recurrency"/>
                <field name="categ_ids" widget="many2many_tags" optional="hide" readonly="recurrency" options="{'color_field': 'color'}"/>
                <field name="recurrency" optional="hide" readonly="1"/>
                <field name="privacy" optional="hide" readonly="recurrency"/>
                <field name="show_as" optional="hide" readonly="recurrency"/>
                <field name="location" optional="show" readonly="recurrency"/>
                <field name="duration" widget="float_time" readonly="1"/>
                <field name="description" optional="hide" readonly="recurrency"/>
                <field name="allday" column_invisible="True"/>
                <field name="message_needaction" column_invisible="True"/>
            </tree>
        </field>
    </record>

    <record id="view_calendar_event_form" model="ir.ui.view">
        <field name="name">calendar.event.form</field>
        <field name="model">calendar.event</field>
        <field name="priority" eval="1"/>
        <field name="arch" type="xml">
            <form string="Meetings" js_class="calendar_form">
                <div invisible="not recurrence_id" class="alert alert-info oe_edit_only" role="status">
                    <p>Edit recurring event</p>
                    <field name="recurrence_update" widget="radio"/>
                </div>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button icon="fa-bars" type="object" name="action_open_calendar_event" invisible="not (res_id and res_model_name)">
                            <field name="res_model_name"/>
                        </button>
                    </div>
                    <widget name="web_ribbon" title="Archived" bg_color="text-bg-danger" invisible="active"/>
                    <field name="res_id" invisible="1" />
                    <field name="active" invisible="1"/>
                    <field name="user_can_edit" invisible="1"/>
                    <div class="oe_title mb-3">
                        <div>
                            <label for="name"/>
                        </div>
                        <h1>
                            <field name="name" placeholder="e.g. Business Lunch" readonly="not user_can_edit"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="start_date" widget="daterange" options="{'end_date_field': 'stop_date'}" invisible="not allday" required="allday" readonly="not user_can_edit"/>
                            <field name="start" widget="daterange" options="{'end_date_field': 'stop'}" invisible="allday" required="not allday" readonly="not user_can_edit"/>
                            <field name="stop_date" invisible="1"/>
                            <field name="stop" invisible="1" />
                            <label for="duration" class="fw-bold text-900 opacity-100"/>
                            <div class="d-flex gap-2">
                                <div class="d-flex align-items-start" invisible="allday">
                                    <field name="duration" widget="float_time" string="Duration" class="oe_inline pe-2" readonly="(id and recurrency) or not user_can_edit"/>
                                    <span> hours</span>
                                </div>
                                <span invisible="allday" style="white-space: pre;"> or </span>
                                <div class="d-flex gap-2">
                                    <label for="allday" class=""/>
                                    <field name="allday" class="oe_inline" widget="boolean_toggle" force_save="1" options="{'autosave': False}" readonly="not user_can_edit"/>
                                </div>
                            </div>
                            <field name="event_tz" invisible="not recurrency" readonly="not user_can_edit"/>


                            <field name="recurrence_id" invisible="1" />
                            <field name="rrule_type" invisible="1" />
                            <field name="recurrency" readonly="not user_can_edit"/>
                            <field name="rrule_type_ui" class="w-auto" invisible="not recurrency" required="recurrency" readonly="not user_can_edit"/>
                            <label for="interval" class="fw-bold text-900" invisible="not recurrency or rrule_type_ui != 'custom'"/>
                            <div class="d-flex gap-1" invisible="not recurrency or rrule_type_ui != 'custom'">
                                <field name="interval" string="Repeat every" class="oe_inline w-auto" />
                                <field name="rrule_type" nolabel="1" class="oe_inline" required="rrule_type_ui == 'custom'"/>
                            </div>
                            <span class="fw-bold text-nowrap" invisible="rrule_type_ui not in ['weekly', 'custom'] or (rrule_type_ui == 'custom' and rrule_type != 'weekly')">Repeat on</span>
                            <div invisible="rrule_type_ui not in ['weekly', 'custom'] or (rrule_type_ui == 'custom' and rrule_type != 'weekly')" ><widget name="calendar_week_days" readonly="not user_can_edit"/></div>
                            <field name="recurrence_id" invisible="1" />

                            <label string="Day of Month" for="month_by" class="fw-bold text-900" style="width: fit-content;" invisible="rrule_type_ui not in ['monthly', 'custom'] or (rrule_type_ui == 'custom' and rrule_type != 'monthly')"/>
                            <div class="d-flex gap-2" invisible="rrule_type_ui not in ['monthly', 'custom'] or (rrule_type_ui == 'custom' and rrule_type != 'monthly')">
                                <field name="month_by" nolabel="1" class="oe_inline w-auto" required="rrule_type_ui == 'monthly' or rrule_type == 'monthly'"/>
                                <field name="day" nolabel="1" class="oe_inline w-auto"
                                    required="(rrule_type_ui == 'monthly' or rrule_type == 'monthly') and month_by == 'date'"
                                    invisible="month_by != 'date'"
                                />
                                <field name="byday" string="The" class="oe_inline w-auto" nolabel="1"
                                    required="(rrule_type_ui == 'monthly' or rrule_type == 'monthly') and month_by == 'day'"
                                    invisible="month_by != 'day'"
                                />
                                <field name="weekday" nolabel="1" class="oe_inline w-auto"
                                    required="(rrule_type_ui == 'monthly' or rrule_type == 'monthly') and month_by == 'day'"
                                    invisible="month_by != 'day'"
                                />
                            </div>
                            <label string="Until" for="end_type" class="fw-bold text-900" invisible="not recurrency"/>
                            <div class="d-flex gap-2" invisible="not recurrency">
                                <field name="end_type" class="oe_inline w-auto" nolabel="1" required="recurrency" readonly="not user_can_edit"/>
                                <field name="count" class="oe_inline w-auto" nolabel="1" invisible="end_type != 'count'" required="recurrency" readonly="not user_can_edit"/>
                                <field name="until" class="oe_inline w-auto" nolabel="1" invisible="end_type != 'end_date'" readonly="not user_can_edit"
                                    required="recurrency and end_type == 'end_date'"
                                    placeholder="e.g: 12/31/2023"
                                />
                            </div>

                            <field name="location" placeholder="Online Meeting" readonly="not user_can_edit"/>
                            <label for="videocall_location" class="opacity-100"/>
                            <div col="2">
                                <field name="videocall_location" string="Videocall URL" widget="CopyClipboardChar" force_save="1" readonly="videocall_source == 'discuss' or not user_can_edit"/>
                                <button name="clear_videocall_location" type="object" class="btn btn-link ps-0"
                                    invisible="not videocall_location or not user_can_edit" context="{'recurrence_update': recurrence_update}">
                                    <span class="fa fa-times"></span><span> Clear meeting</span>
                                </button>
                                <button name="set_discuss_videocall_location" type="object" class="btn btn-link ps-0"
                                    invisible="videocall_location or not user_can_edit" context="{'recurrence_update': recurrence_update}">
                                    <span class="fa fa-plus"></span><span> Odoo meeting</span>
                                </button>
                                <button name="action_join_video_call" class="btn btn-link" help="Join Video Call" type="object" invisible="not videocall_location or not user_can_edit">
                                    <span class="oi oi-arrow-right"></span><span> Join video call</span>
                                </button>
                            </div>
                            <field name="videocall_source" invisible="1"/>
                            <field name="access_token" invisible="1" force_save="1"/>
                            <field name="categ_ids" widget="many2many_tags" options="{'color_field': 'color', 'no_create_edit': True}" readonly="not user_can_edit"/>
                            <label for="privacy"/>
                                    <div class="o_row">
                                        <field name="show_as" readonly="not user_can_edit" nolabel="1"/>
                                        <field name="privacy" readonly="not user_can_edit" nolabel="1"/>
                                    </div>
                                    <field name="user_id" widget="many2one_avatar_user" readonly="not user_can_edit"/>
                                    <field name="description" options="{'height': 100}" placeholder="Add description" readonly="not user_can_edit"/>
                        </group>
                        <group>
                            <field name="should_show_status" invisible="1"/>
                            <field name="current_status" class="w-auto" required="should_show_status" readonly="not should_show_status" invisible="not should_show_status"/>
                            <field name="alarm_ids" widget="many2many_tags" options="{'no_quick_create': True}" readonly="not user_can_edit"/>
                            <div class="o_row" colspan="2">
                                <div colspan="1">
                                    <field name="attendees_count" class="w-auto oe_inline" nolabel="1"/><span> Attendees</span>
                                </div>
                                <div name="send_buttons" class="d-flex gap-2 justify-content-end" colspan="1">
                                    <button name="action_open_composer" help="Send Email to attendees" type="object" string=" EMAIL" icon="fa-envelope" invisible="not user_can_edit"/>
                                </div>
                            </div>
                            <div colspan="2">
                                <field name="accepted_count" class="w-auto oe_inline" nolabel="1"/> yes,
                                <field name="tentative_count" class="w-auto oe_inline" nolabel="1"/> maybe,
                                <field name="declined_count" class="w-auto oe_inline" nolabel="1"/> no,
                                <field name="awaiting_count" class="w-auto oe_inline" nolabel="1"/> awaiting
                            </div>
                            <div class="d-flex align-items-baseline" colspan="2">
                                <field name="partner_ids" widget="many2manyattendee"
                                    placeholder="Select attendees..."
                                    options="{'no_quick_create': True}"
                                    context="{'form_view_ref': 'base.view_partner_simple_form'}"
                                    domain="[('type','!=','private')]"
                                    class="oe_inline"
                                    readonly="not user_can_edit"
                                />
                            </div>
                            <div class="alert alert-warning o_form_header mt-2" colspan="2" invisible="not invalid_email_partner_ids" role="status">
                                <p><strong>The following attendees have invalid email addresses and won't receive any email notifications:</strong></p>
                                <field name="invalid_email_partner_ids" widget="many2manyattendee" class="oe_inline"/>
                            </div>
                        </group>
                    </group>
                    <notebook>
                        <page name="page_invitations" string="Invitations" groups="base.group_no_one" invisible="not user_can_edit">
                            <button name="action_sendmail" type="object" string="Send Invitations" icon="fa-envelope" class="oe_link"/>
                            <field name="attendee_ids" widget="one2many" mode="tree,kanban" readonly="1">
                                <tree string="Invitation details" editable="top" create="false" delete="false">
                                    <field name="partner_id" />
                                    <field name="email" widget="email"/>
                                    <field name="phone" widget="phone"/>
                                    <field name="state" />
                                    <button name="do_tentative" invisible="state not in ('needsAction', 'declined', 'accepted')" string="Uncertain" type="object" icon="fa-asterisk"/>
                                    <button name="do_accept" string="Accept" invisible="state not in ('needsAction', 'tentative', 'declined')" type="object" icon="fa-check text-success"/>
                                    <button name="do_decline" string="Decline" invisible="state not in ('needsAction', 'tentative', 'accepted')" type="object" icon="fa-times-circle text-danger"/>
                                </tree>
                                <kanban class="o_kanban_mobile" create="false" delete="false">
                                    <field name="partner_id" />
                                    <field name="state" />
                                    <field name="email" widget="email"/>

                                    <templates>
                                        <t t-name="kanban-box">
                                            <div class="d-flex flex-column justify-content-between">
                                                <field name="partner_id"/>
                                                <field name="email" widget="email"/>
                                                <span>Status: <field name="state" /></span>

                                                <div class="text-end">
                                                    <button name="do_tentative" invisible="state not in ('needsAction', 'declined', 'accepted')" string="Uncertain" type="object" class="btn fa fa-asterisk"/>
                                                    <button name="do_accept" invisible="state not in ('needsAction', 'tentative', 'declined')" string="Accept" type="object" class="btn fa fa-check text-success"/>
                                                    <button name="do_decline" invisible="state not in ('needsAction', 'tentative', 'accepted')" string="Decline" type="object" class="btn fa fa-times-circle text-danger"/>
                                                </div>
                                            </div>
                                        </t>
                                    </templates>
                                </kanban>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids" readonly="not user_can_edit"/>
                    <field name="message_ids" readonly="not user_can_edit"/>
                </div>
            </form>
        </field>
    </record>

    <record id="view_calendar_event_form_quick_create" model="ir.ui.view">
        <field name="name">calendar.event.form.quick_create</field>
        <field name="model">calendar.event</field>
        <field name="priority" eval="2"/>
        <field name="arch" type="xml">
            <form string="Meetings" js_class="calendar_quick_create_form_view">
                <sheet>
                    <field name="recurrence_update" invisible="1"/>
                    <field name="videocall_source" invisible="1"/>
                    <field name="access_token" invisible="1" force_save="1"/>
                    <div class="o_row">
                        <h1 class="w-100"><field name="name" nolabel="1" placeholder="Add title" colspan="2"/></h1>
                    </div>
                    <group>
                        <field name="start_date" widget="daterange" options="{'end_date_field': 'stop_date'}" invisible="not allday" required="allday"/>
                        <field name="start" widget="daterange" options="{'end_date_field': 'stop'}" invisible="allday" required="not allday"/>
                        <field name="stop_date" invisible="1" />
                        <field name="stop" invisible="1" />
                        <field name="allday"/>
                        <field name="partner_ids" widget="many2manyattendee"
                            placeholder="Add attendees..."
                            options="{'no_quick_create': True}"
                            context="{'form_view_ref': 'base.view_partner_simple_form'}"
                            class="oe_inline"
                        />
                        <label for="videocall_location" class="opacity-100"/>
                        <div col="2">
                            <field name="videocall_location" string="Videocall URL" widget="CopyClipboardChar" force_save="1" readonly="videocall_source == 'discuss'"/>
                            <button name="clear_videocall_location" type="object" class="btn btn-link p-0"
                                invisible="not videocall_location" context="{'recurrence_update': recurrence_update}">
                                <span class="fa fa-times"></span><span> Clear meeting</span>
                            </button>
                            <button name="set_discuss_videocall_location" type="object" class="btn btn-link p-0"
                                invisible="videocall_location" context="{'recurrence_update': recurrence_update}">
                                <span class="fa fa-plus"></span> <span>Odoo meeting</span>
                            </button>
                            <button name="action_join_video_call" class="btn btn-link" help="Join Video Call" type="object" invisible="not videocall_location">
                                <span class="oi oi-arrow-right"></span><span> Join video call</span>
                            </button>
                        </div>
                        <field name="description" placeholder="Describe your meeting"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="view_calendar_event_calendar" model="ir.ui.view">
        <field name="name">calendar.event.calendar</field>
        <field name="model">calendar.event</field>
        <field name="priority" eval="2"/>
        <field name="arch" type="xml">
            <calendar js_class="attendee_calendar" string="Meetings" banner_route="/onboarding/calendar" date_start="start" date_stop="stop" date_delay="duration" all_day="allday"
                event_open_popup="true"
                event_limit="5"
                quick_create="true"
                quick_create_view_id="%(calendar.view_calendar_event_form_quick_create)d"
                color="partner_ids">
                <field name="location" invisible="not location" options="{'icon': 'fa fa-map-marker'}"/>
                <field name="attendees_count" invisible="1"/>
                <field name="accepted_count" invisible="1"/>
                <field name="declined_count" invisible="1"/>
                <field name="user_can_edit" invisible="1"/>
                <field name="partner_ids" options="{'block': True, 'icon': 'fa fa-users'}"
                       filters="1" widget="many2manyattendeeexpandable" write_model="calendar.filters"
                       write_field="partner_id" filter_field="partner_checked" avatar_field="avatar_128"
                />
                <field name="videocall_location" widget="url" text="Join Video Call" options="{'icon': 'fa fa-lg fa-video-camera'}" invisible="not videocall_location"/>
                <field name="is_highlighted" invisible="1"/>
                <field name="is_organizer_alone" invisible="1"/>
                <field name="display_description" invisible="1"/>
                <field name="description" invisible="not display_description" options="{'icon': 'fa fa-bars'}"/>
                <field name="privacy" invisible="1"/>
                <field name="res_model_name" invisible="not res_model_name"
                       options="{'icon': 'fa fa-link', 'shouldOpenRecord': true}"/>
                <field name="alarm_ids" invisible="not alarm_ids" options="{'icon': 'fa fa-bell-o'}"/>
                <field name="categ_ids" invisible="not categ_ids" options="{'icon': 'fa fa-tag', 'color_field': 'color'}"/>
                <!-- For recurrence update Dialog -->
                <field name="recurrency" invisible="1"/>
                <field name="recurrence_update" invisible="1"/>
                <field name="partner_id" string="Organizer" options="{'icon': 'fa fa-user-o'}"/>
            </calendar>
        </field>
    </record>

    <record id="view_calendar_event_search" model="ir.ui.view">
        <field name="name">calendar.event.search</field>
        <field name="model">calendar.event</field>
        <field name="arch" type="xml">
            <search string="Search Meetings">
                <field name="name" string="Meeting" filter_domain="[('name', 'ilike', self)]"/>
                <field name="partner_ids"/>
                <field name="user_id"/>
                <field name="location"/>
                <field name="show_as"/>
                <field name="categ_ids"/>
                <field name="description"/>
                <filter string="My Meetings" help="My Meetings" name="mymeetings" domain="[('partner_ids.user_ids', 'in', [uid])]"/>
                <separator/>
                <filter string="Date" name="filter_start_date" date="start"/>
                <separator/>
                <filter string="Busy" name="busy" domain="[('show_as', '=', 'busy')]"/>
                <filter string="Free" name="free" domain="[('show_as', '=', 'free')]"/>
                <separator/>
                <filter string="Public" name="public" domain="[('privacy', '=', 'public')]"/>
                <filter string="Private" name="private" domain="[('privacy', '=', 'private')]"/>
                <filter string="Only Internal Users" name="confidential" domain="[('privacy', '=', 'confidential')]"/>
                <separator/>
                <filter string="Recurrent" name="recurrent" domain="[('recurrency', '=', True)]"/>
                <separator/>
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                <group expand="0" string="Group By">
                    <filter string="Responsible" name="responsible" domain="[]" context="{'group_by': 'user_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="action_calendar_event" model="ir.actions.act_window">
        <field name="name">Meetings</field>
        <field name="res_model">calendar.event</field>
        <field name="view_mode">calendar,tree,form</field>
        <field name="view_id" ref="view_calendar_event_calendar"/>
        <field name="search_view_id" ref="view_calendar_event_search"/>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            No meetings found. Let's schedule one!
          </p><p>
            The calendar is shared between employees and fully integrated with
            other applications such as the employee leaves or the business
            opportunities.
          </p>
        </field>
    </record>

    <record id="res_users_view_form" model="ir.ui.view">
            <field name="name">res.users.view.form.inherit.calendar</field>
            <field name="model">res.users</field>
            <field name="inherit_id" ref="base.view_users_form"/>
            <field name="arch" type="xml">
                <notebook colspan="4" position="inside">
                    <!-- Placeholder container to hold information about external accounts (Google calendar, Microsoft calendar, ...) -->
                    <page string="Calendar" name="calendar" groups="base.group_system"
                         invisible="1">
                        <group name="calendar_accounts"/>
                    </page>
                </notebook>
            </field>
    </record>

    <record id="action_view_calendar_event_calendar" model="ir.actions.act_window.view">
        <field name="act_window_id" ref="action_calendar_event"/>
        <field name="sequence" eval="1"/>
        <field name="view_mode">calendar</field>
        <field name="view_id" ref="view_calendar_event_calendar"/>
    </record>

    <record id="action_view_calendar_event_tree" model="ir.actions.act_window.view">
        <field name="act_window_id" ref="action_calendar_event"/>
        <field name="sequence" eval="2"/>
        <field name="view_mode">tree</field>
        <field name="view_id" ref="view_calendar_event_tree"/>
    </record>

    <record id="action_view_calendar_event_form" model="ir.actions.act_window.view">
        <field name="act_window_id" ref="action_calendar_event"/>
        <field name="sequence" eval="3"/>
        <field name="view_mode">form</field>
        <field name="view_id" ref="view_calendar_event_form"/>
    </record>

    <record id="action_view_start_calendar_sync" model="ir.actions.act_window">
        <field name="name">Connect your Calendar</field>
        <field name="res_model">calendar.provider.config</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="context">{'dialog_size': 'medium'}</field>
    </record>

    <record id="calendar_settings_action" model="ir.actions.act_window">
        <field name="name">Settings</field>
        <field name="res_model">res.config.settings</field>
        <field name="view_mode">form</field>
        <field name="target">inline</field>
        <field name="context">{'module' : 'calendar', 'bin_size': False}</field>
    </record>

    <!-- Menus -->
    <menuitem
        id="mail_menu_calendar"
        name="Calendar"
        sequence="10"
        web_icon="calendar,static/description/icon.png"
        groups="base.group_user"/>

    <menuitem
        id="calendar_event_menu"
        name="Calendar"
        sequence="1"
        action="action_calendar_event"
        parent="mail_menu_calendar"
        groups="base.group_user"/>

    <menuitem
        id="calendar_menu_config"
        parent="calendar.mail_menu_calendar"
        name="Configuration"
        sequence="40"
        action="calendar.action_calendar_event"
        groups="base.group_system,base.group_no_one"/>

    <menuitem
        id="menu_calendar_settings"
        parent="calendar_menu_config"
        name="Settings"
        sequence="45"
        action="calendar_settings_action"
        groups="base.group_system"/>

    <menuitem
        id="calendar_submenu_reminders"
        parent="calendar_menu_config"
        name="Reminders"
        sequence="50"
        action="action_calendar_alarm"
        groups="base.group_no_one"/>

    <menuitem
        id="menu_calendar_configuration"
        name="Calendar"
        parent="base.menu_custom"
        sequence="30"
        groups="base.group_no_one"/>

    <menuitem
        id="menu_calendar_event_type"
        parent="menu_calendar_configuration"
        action="action_calendar_event_type"
        groups="base.group_no_one"/>

    <menuitem
        id="menu_calendar_alarm"
        parent="menu_calendar_configuration"
        action="action_calendar_alarm"
        groups="base.group_no_one"/>

</odoo>
