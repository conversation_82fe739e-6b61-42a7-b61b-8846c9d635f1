<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="ir_cron_update_appointment_status" model="ir.cron">
            <field name="name">Appointment: Enforce Lateness Policy</field>
            <field name="model_id" eval="ref('calendar.model_calendar_event')"/>
            <field name="state">code</field>
            <field name="code">model._cron_update_appointment_status()</field>
            <field name="user_id" ref="base.user_root"/>
            <field name="interval_number">15</field>
            <field name="interval_type">minutes</field>
            <field name="numbercall">-1</field>
            <field name="active" eval="True"/>
        </record>
    </data>
</odoo>