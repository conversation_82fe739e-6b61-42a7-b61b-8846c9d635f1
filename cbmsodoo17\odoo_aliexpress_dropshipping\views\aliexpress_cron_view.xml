<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) 2016-Present Webkul Software Pvt. Ltd. (<https://webkul.com/>) -->
<!-- See LICENSE file for full copyright and licensing details. -->
<!-- License URL : https://store.webkul.com/license.html/ -->
<odoo>
    <data>
        <!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~ SCHEDULAR ACTION ~~~~~~~~~~~~~~~~~~~~~~ -->

        <!-- ~~~~~~~Scheduler for fetching product data from Aliexpress and save it in product feed~~~~~~~~ -->
        <record model="ir.cron" id="aliexpress_scheduler_for_data_fetching">
            <field name="name">Scheduler for fetching product data from Aliexpress and save it in product feed</field>
            <field name="active" eval="False"/>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="numbercall">-1</field>
            <field name="doall" eval="False"/>
            <field name="state">code</field>
            <field name="user_id" ref="base.user_root"/>
            <field name="model_id" ref="model_aliexpress_product"/>
            <field name="code">model.fetch_and_update_product_feed_data_from_aliexpres()</field>
        </record>

        <!-- ~~~~~~~~Scheduler for creating or updating product data from the aliexpress feed~~~~~ -->
        <record model="ir.cron" id="scheduler_for_create_or_update_product_data_from_feed">
            <field name="name">Scheduler for creating or updating product data from the aliexpress feed</field>
            <field name="active" eval="False"/>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="numbercall">-1</field>
            <field name="doall" eval="False"/>
            <field name="state">code</field>
            <field name="user_id" ref="base.user_root"/>
            <field name="model_id" ref="model_aliexpress_product"/>
            <field name="code">model.create_or_update_product_data_from_feed()</field>
        </record>

    </data>
</odoo>
