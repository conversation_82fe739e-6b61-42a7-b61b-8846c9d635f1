# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * base_location
#
# Translators:
# <AUTHOR> <EMAIL>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-11-22 03:38+0000\n"
"PO-Revision-Date: 2017-11-22 03:38+0000\n"
"Last-Translator: OCA Transbot <<EMAIL>>, 2017\n"
"Language-Team: Spanish (Dominican Republic) (https://www.transifex.com/oca/"
"teams/23907/es_DO/)\n"
"Language: es_DO\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: base_location
#: model:res.city,name:base_location.demo_brussels_city
msgid "Brussels"
msgstr ""

#. module: base_location
#: model:ir.model.fields,help:base_location.field_res_company__country_enforce_cities
msgid ""
"Check this box to ensure every address created in that country has a 'City' "
"chosen in the list of the country's cities."
msgstr ""

#. module: base_location
#: model:ir.actions.act_window,name:base_location.action_res_city_full
#: model:ir.ui.menu,name:base_location.locations_menu_cities
msgid "Cities"
msgstr ""

#. module: base_location
#: model:ir.model,name:base_location.model_res_city
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__city_id
#: model:ir.model.fields,field_description:base_location.field_res_partner__city
#: model:ir.model.fields,field_description:base_location.field_res_users__city
msgid "City"
msgstr ""

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_company__city_id
#: model:ir.model.fields,field_description:base_location.field_res_partner__city_id
#: model:ir.model.fields,field_description:base_location.field_res_users__city_id
msgid "City ID"
msgstr ""

#. module: base_location
#: model_terms:ir.ui.view,arch_db:base_location.view_company_form_city
#: model_terms:ir.ui.view,arch_db:base_location.view_partner_form
msgid "City completion"
msgstr ""

#. module: base_location
#: model:ir.model,name:base_location.model_res_city_zip
msgid "City/locations completion object"
msgstr ""

#. module: base_location
#: model:ir.model,name:base_location.model_res_company
msgid "Companies"
msgstr ""

#. module: base_location
#: model:ir.model,name:base_location.model_res_partner
msgid "Contact"
msgstr ""

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__country_id
#: model:ir.model.fields,field_description:base_location.field_res_partner__country_id
#: model:ir.model.fields,field_description:base_location.field_res_users__country_id
#: model_terms:ir.ui.view,arch_db:base_location.view_country_search
msgid "Country"
msgstr ""

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__create_date
msgid "Created on"
msgstr "Creado en"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: base_location
#: model_terms:ir.actions.act_window,help:base_location.action_res_city_full
msgid ""
"Display and manage the list of all cities that can be assigned to\n"
"            your partner records. Note that an option can be set on each "
"country\n"
"            separately\n"
"            to enforce any address of it to have a city in this list."
msgstr ""

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_company__country_enforce_cities
msgid "Enforce Cities"
msgstr ""

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__id
msgid "ID"
msgstr "ID"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip____last_update
msgid "Last Modified on"
msgstr "Última modificación en"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__write_uid
msgid "Last Updated by"
msgstr "Última actualización de"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__write_date
msgid "Last Updated on"
msgstr "Última actualización en"

#. module: base_location
#: model_terms:ir.ui.view,arch_db:base_location.view_partner_form
msgid "Location completion"
msgstr ""

#. module: base_location
#: model:ir.actions.act_window,name:base_location.action_zip_tree
msgid "Locations"
msgstr ""

#. module: base_location
#: model_terms:ir.ui.view,arch_db:base_location.view_city_zip_filter
msgid "Search zip"
msgstr ""

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__state_id
#: model:ir.model.fields,field_description:base_location.field_res_partner__state_id
#: model:ir.model.fields,field_description:base_location.field_res_users__state_id
msgid "State"
msgstr ""

#. module: base_location
#. odoo-python
#: code:addons/base_location/models/res_partner.py:0
#, python-format
msgid ""
"The city of the partner %(partner)s differs from that in location "
"%(location)s"
msgstr ""

#. module: base_location
#. odoo-python
#: code:addons/base_location/models/res_partner.py:0
#, python-format
msgid ""
"The country of the partner %(partner)s differs from that in location "
"%(location)s"
msgstr ""

#. module: base_location
#. odoo-python
#: code:addons/base_location/models/res_partner.py:0
#, python-format
msgid ""
"The state of the partner %(partner)s differs from that in location "
"%(location)s"
msgstr ""

#. module: base_location
#. odoo-python
#: code:addons/base_location/models/res_partner.py:0
#, python-format
msgid ""
"The zip of the partner %(partner)s differs from that in location %(location)s"
msgstr ""

#. module: base_location
#: model:ir.model.fields,help:base_location.field_res_company__zip_id
msgid "Use the city name or the zip code to search the location"
msgstr ""

#. module: base_location
#: model:ir.model.constraint,message:base_location.constraint_res_city_name_state_country_uniq
msgid ""
"You already have a city with that name in the same state.The city must have "
"a unique name within it's state and it's country"
msgstr ""

#. module: base_location
#: model:ir.model.constraint,message:base_location.constraint_res_city_zip_name_city_uniq
msgid ""
"You already have a zip with that code in the same city. The zip code must be "
"unique within it's city"
msgstr ""

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__name
msgid "ZIP"
msgstr ""

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_company__zip_id
#: model:ir.model.fields,field_description:base_location.field_res_partner__zip_id
#: model:ir.model.fields,field_description:base_location.field_res_users__zip_id
msgid "ZIP Location"
msgstr ""

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_partner__zip
#: model:ir.model.fields,field_description:base_location.field_res_users__zip
#: model_terms:ir.ui.view,arch_db:base_location.city_zip_form
msgid "Zip"
msgstr ""

#. module: base_location
#: model:ir.ui.menu,name:base_location.locations_menu_zips
#: model_terms:ir.ui.view,arch_db:base_location.view_city_form
#: model_terms:ir.ui.view,arch_db:base_location.view_res_country_city_better_zip_form
msgid "Zips"
msgstr ""

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city__zip_ids
msgid "Zips in this city"
msgstr ""

#~ msgid "Group By"
#~ msgstr "Agrupar por"
