// Style for the widget "ept_dashboard_graph"
.ept_dashboard_graph {
    position: relative;
    display: block;
    canvas {
        height: 130px !important;
    }
}


/* Kanban View */
.ep-kanban-box{
    border-radius: 5px !important;
    box-shadow: 0px 3px 15px -10px #444;
    padding-top: 15px !important;
}

.ep-kanban-box a{
    color: #0a1735;
}

/* 5 buttons */
.ep_kanban_records a{
    width: 19.2%;
    float: left;
    margin: 1% 1% 1% 0%;
    padding: 8px 0px;
    font-size: 11px;
//     color: #0a1735;
    border: 1px solid;
    border-radius: 5px;
    border-color: #ced4da;
    font-weight: 600;
}

.ep_kanban_records a:last-child{
    margin: 1% 0% 1% 0%;
}

.ep_o_kanban_setting a{
    border: 1px solid #ced4da;
    border-radius: 5px;
    border-color: #ced4da;
    padding: 8px 8px;
    margin: 0px 0px 0px 5px;
    float: right;
    color: #0a1735;
}

.ep-btn-primary{
    background-color: #0068ff;
    border-radius: 5px;
    border: unset;
    padding: 8px 15px;
    box-shadow: 5px 5px 10px -2px rgba(0, 79, 255, 0.2);
    transition: 0.25s ease;
    text-transform: uppercase;
}

.ep-btn-primary:hover, .ep-btn-primary:active, .ep-btn-primary:focus{
    background-color: #0068ff !important;
    border: unset;
    opacity:0.9;
}


/* 7th Background Color code */
// .ep-bg-1{
//     background-color: #fbfaff;
// }
// .ep-bg-2{
//     background-color: #fffdfa;
// }
// .ep-bg-3{
//     background-color: #fffff8;
// }
// .ep-bg-4{
//     background-color: #fafeff;
// }
// .ep-bg-5{
//     background-color: #fbfff5;
// }
// .ep-bg-6{
//     background-color: #fffafa;
// }
// .ep-bg-7{
//     background-color: #fafbff;
// }

// Field with graph_ept widget
.o_field_dashboard_graph_ept {
    width: 100%;
    margin-top:8px;

    .ep_kanban_graph {
        border: 1px solid #ced4da;
        border-radius: 5px;
        padding: 10px;
        color: #E4E4E4;
        width: 100%;

        .chartjs-render-monitor {
            width: 100%;
            padding: 3px 10px;
        }

        .ep_graph_details {
            @media only screen and (max-width: 1440px) {
                a {
                    font-size : 0.875rem;
                }
                h4 {
                    font-size: 0.9rem;
                }
            }
        }
    }

    // Graph height
    .graph_ept{
        max-height:200px !important;
    }

    #sort_order_data {
        max-width: calc(350px - 250px);
        top: 2%;
        right: 7%;
    }

}
.o_dashboard_graph {
    canvas {
        max-height: 200px;
    }
}

.o_kanban_dashboard.o_emipro_kanban{
    .o_content {
        .o_kanban_renderer.o_kanban_ungrouped {
            .o_kanban_record{
                width: 31.33%;
                div::after{
                    width: 4px;
                }
            }
            .o_dashboard_graph{
                margin: 0px -16px;
                padding: 8px 16px;
            }
            .oe_kanban_colorpicker {
                max-width: 100%;
                padding: 10px 15px 10px 20px;
                margin-left: 0px !important;
            }
        }
    }
    .o_kanban_card_manage_title span {
        font-weight: 600;
    }
}