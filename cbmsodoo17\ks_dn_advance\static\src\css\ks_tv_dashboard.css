
.ks-tv-item.ks_chart_container {
    height:100%;
    width:100%;
    margin: auto;
}

.owl-item {
    height: 90vh;

}
.owl-nav {
    text-align: center;
}

.owl-nav .owl-prev,
 .owl-nav .owl-next {
    color: white !important;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    font-size: 60px !important
}
.owl-nav .owl-prev {
    left: -60px;
}
.owl-nav .owl-next {
    right: -60px;
}

.ks-tv-item .ks_dashboarditem_chart_container {
    height: 100%;
}

.ks_float_tv {
    position: fixed !important;
    left: 0;
    bottom: 0;
    right: 0;
    top: 0;
    z-index: 1000 !important;
}

.ks_stop_tv_dashboard {
    position: fixed;
    right: 10px;
    z-index: 10;
    color: #ffffff;
    font-size: 28px !important;
    top: 1vh;
}

button.btn.btn-primary.ks_start_tv_dashboard.mr-2.d-md-block {
    margin-right:5px !important;
}

button.btn.btn-primary.ks_dashboard_print_pdf.mr-2.d-md-block {
    margin-right:5px !important;
}

button.btn.btn-primary.ks_dashboard_send_email.mr-2.d-md-block {
    margin-right:5px !important;
}


.ks_tv_item.d-flex{
    width:100%;
    margin:auto;
}

.ks_tv_item .grid-stack-item {
    min-width: 275px;
    min-height:
}

.ks-tv-item.ks_list_view {
    height:100%;
}

.tv-modal-overlay {
    width: 100%;
    height: 100%;
    position: absolute;
    background: rgba(0, 0, 0, 0.73);
}

.ks_float_tv .owl-carousel {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    height:90vh;
    width:80vw;
}

.ks-tv-item .ks_list_view_container {
    height: 100%;
}

.ks-tv-item .ks_dashboard_kpi {
    height: 132px !important;
}

@media (min-width: 768px) {
    .ks-tv-item .ks_dashboard_item_hover {
        margin: 8px;
        min-width: 250px;
    }
    .ks-tv-kpi .ks_dashboard_item_hover {
        margin: 8px;
        min-width: 250px;
        min-height: 208px;
    }
}

@media (min-width: 1440px) {
    .ks-tv-item .ks_dashboard_item_hover {
        min-width: 350px;
    }
    .ks-tv-kpi .ks_dashboard_item_hover {
        min-width: 350px;
        min-height: 208px;
    }


    .ks-tv-item .ks_dashboard_item_hover {
        margin: 14px;
        min-width: 400px;
    }
     .ks-tv-item .ks_dashboard_kpi_name_preview,
      .ks-tv-item .ks_dashboard_item_name_l5,
      .ks-tv-item .ks_dashboard_item_name_l3,
       .ks-tv-item .ks_dashboard_item_name_l2 {
         font-size: 26px;
     }
     .ks-tv-item .ks_dashboard_icon_l5 span,
     .ks-tv-kpi .ks_dashboard_icon_l5 span {
        font-size: 36px;
     }
     .ks-tv-kpi .ks_dashboard_icon_l5 > img {
        width: 36px !important;
        height: 36px;
     }
     .ks-tv-item .ks_dashboard_item_hover #ksListViewTable td,
     .ks-tv-item .ks_dashboard_item_hover #ksListViewTable th,
     .ks-tv-item .ks_dashboard_item_hover .ks_list_view_heading {
        font-size: 20px !important;
     }
     .ks-tv-kpi .ks_dashboard_item_hover.ks_dashboard_kpi .text-center.mt-1,
     .ks-tv-kpi .ks_dashboard_item_hover.ks_dashboard_kpi .ks_progress .text-center,
      .ks-tv-kpi .ks_dashboard_item_hover .ks_target_previous {
        font-size: 16px;
     }
}

.ks_dn_table .ks_dn_thead .ks_dn_th {
    position: sticky;
    top: -5px;
}

.card-body.table-responsive {
    padding: 0 !important;
}

@media (min-width: 1820px) {
    .ks-tv-item .ks_dashboard_item_hover {
    min-height: 164px;
}
}

#ksListViewTable.ks_list_view_layout_4 thead th,
#ksListViewTable.ks_list_view_layout_3 thead th {
    color: #ffffff;
    background: #324960;
    text-align: center;
}


#ksListViewTable.ks_list_view_layout_4 thead th:nth-child(even) {
   background: #4FC3A1;
}

#ksListViewTable.ks_list_view_layout_2 thead th {
    color: #bfa829;
    background-color: #343a40
}

#ksListViewTable.ks_list_view_layout_1 thead th {
    background-color: #F7F9FA !important
}

.ks_table_layput_3 {
    table-layout: auto !important;
}


/*RTL Crowsel related CSS */
.owl-carousel.owl-rtl {
    direction: ltr !important;
}

/*monkey patch : RTL right click not updating chart*/
.owl-carousel.owl-rtl .fa-angle-left {
    display: none!;
}

.ks_minus {
    display: none! important;
}

.ks_plus {
    display: none! important;
}


.ks_border_aliceblue{
    border: aliceblue !important;
}

.list_header {
    white-space: nowrap !important;
}

.table thead button.ks_list_heder_hover{
    opacity: 0;
    transition: all ease-in-out 0.5s;
}

.table thead th:hover button.ks_list_heder_hover{
    opacity: 1;
}

.ks_hide_display {
    display:none !important;
}



