# -*- coding: utf-8 -*-
from odoo import Command, http
from odoo.http import request
from odoo.addons.website_appointment.controllers.appointment import WebsiteAppointment
import logging

_logger = logging.getLogger(__name__)

class AdvancedAppointmentController(WebsiteAppointment):

    def _handle_appointment_form_submission(
        self, appointment_type,
        date_start, date_end, duration,  # appointment boundaries
        description, answer_input_values, name, customer, appointment_invite, guests=None,  # customer info
        staff_user=None, asked_capacity=1, booking_line_values=None  # appointment staff / resources
    ):
        """ Override the appointment form submission to create appointment with pending status
            and redirect to custom thank you page instead of default calendar view.
        """
        _logger.info("CUSTOM APPOINTMENT CONTROLLER: _handle_appointment_form_submission called")
        _logger.info(f"Appointment Type: {appointment_type.name}, Customer: {customer.name}")

        # Create the event using the parent method logic but with our custom fields
        event = request.env['calendar.event'].with_context(
            mail_notify_author=True,
            mail_create_nolog=True,
            mail_create_nosubscribe=True,
            allowed_company_ids=self._get_allowed_companies(staff_user or appointment_type.create_uid).ids,
        ).sudo().create({
            'appointment_answer_input_ids': [Command.create(vals) for vals in answer_input_values],
            # Set custom approval state to pending
            'approval_state': 'pending',
            **appointment_type._prepare_calendar_event_values(
                asked_capacity, booking_line_values, description, duration,
                appointment_invite, guests, name, customer, staff_user, date_start, date_end
            )
        })

        _logger.info(f"Created appointment {event.id} with approval_state: {event.approval_state}")

        # Instead of redirecting to calendar view, redirect to our custom thank you page
        return request.render('advanced_appointment_manager.appointment_pending_template', {
            'event': event,
            'customer': customer,
            'appointment_type': appointment_type
        })

    @http.route('/appointment/pending', type='http', auth='public', website=True)
    def appointment_pending(self):
        """Standalone route for the pending appointment page"""
        return request.render('advanced_appointment_manager.appointment_pending_template', {
            'event': False,
            'customer': False,
            'appointment_type': False
        })