# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * base_location
#
# Translators:
# <AUTHOR> <EMAIL>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-12-13 03:42+0000\n"
"PO-Revision-Date: 2022-11-04 14:44+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: German (https://www.transifex.com/oca/teams/23907/de/)\n"
"Language: de\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 4.14.1\n"

#. module: base_location
#: model:res.city,name:base_location.demo_brussels_city
msgid "Brussels"
msgstr "Brüssel"

#. module: base_location
#: model:ir.model.fields,help:base_location.field_res_company__country_enforce_cities
msgid ""
"Check this box to ensure every address created in that country has a 'City' "
"chosen in the list of the country's cities."
msgstr ""
"Aktivieren Sie dieses Kontrollkästchen, um sicherzustellen, dass für jede in "
"diesem Land erstellte Adresse eine \"Stadt\" in der Liste der Städte des "
"Landes ausgewählt ist."

#. module: base_location
#: model:ir.actions.act_window,name:base_location.action_res_city_full
#: model:ir.ui.menu,name:base_location.locations_menu_cities
msgid "Cities"
msgstr "Städte"

#. module: base_location
#: model:ir.model,name:base_location.model_res_city
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__city_id
#: model:ir.model.fields,field_description:base_location.field_res_partner__city
#: model:ir.model.fields,field_description:base_location.field_res_users__city
msgid "City"
msgstr "Stadt"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_company__city_id
#: model:ir.model.fields,field_description:base_location.field_res_partner__city_id
#: model:ir.model.fields,field_description:base_location.field_res_users__city_id
msgid "City ID"
msgstr "Stadt-ID"

#. module: base_location
#: model_terms:ir.ui.view,arch_db:base_location.view_company_form_city
#: model_terms:ir.ui.view,arch_db:base_location.view_partner_form
msgid "City completion"
msgstr "Stadt-Vervollständigung"

#. module: base_location
#: model:ir.model,name:base_location.model_res_city_zip
msgid "City/locations completion object"
msgstr "Stadt/Standorte-Vervollständungsobjekt"

#. module: base_location
#: model:ir.model,name:base_location.model_res_company
msgid "Companies"
msgstr "Unternehmen"

#. module: base_location
#: model:ir.model,name:base_location.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__country_id
#: model:ir.model.fields,field_description:base_location.field_res_partner__country_id
#: model:ir.model.fields,field_description:base_location.field_res_users__country_id
#: model_terms:ir.ui.view,arch_db:base_location.view_country_search
msgid "Country"
msgstr "Land"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__create_uid
msgid "Created by"
msgstr "Erstellt von"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__create_date
msgid "Created on"
msgstr "Erstellt am"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__display_name
msgid "Display Name"
msgstr "Anzeigename"

#. module: base_location
#: model_terms:ir.actions.act_window,help:base_location.action_res_city_full
msgid ""
"Display and manage the list of all cities that can be assigned to\n"
"            your partner records. Note that an option can be set on each "
"country\n"
"            separately\n"
"            to enforce any address of it to have a city in this list."
msgstr ""

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_company__country_enforce_cities
msgid "Enforce Cities"
msgstr "Städte erzwingen"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__id
msgid "ID"
msgstr "ID"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip____last_update
msgid "Last Modified on"
msgstr "Zuletzt geändert am"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__write_uid
msgid "Last Updated by"
msgstr "Zuletzt aktualisiert von"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__write_date
msgid "Last Updated on"
msgstr "Zuletzt aktualisiert am"

#. module: base_location
#: model_terms:ir.ui.view,arch_db:base_location.view_partner_form
msgid "Location completion"
msgstr "Standortvervollständigung"

#. module: base_location
#: model:ir.actions.act_window,name:base_location.action_zip_tree
msgid "Locations"
msgstr "Standorte"

#. module: base_location
#: model_terms:ir.ui.view,arch_db:base_location.view_city_zip_filter
msgid "Search zip"
msgstr "PLZ suchen"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__state_id
#: model:ir.model.fields,field_description:base_location.field_res_partner__state_id
#: model:ir.model.fields,field_description:base_location.field_res_users__state_id
msgid "State"
msgstr "Bundesland"

#. module: base_location
#. odoo-python
#: code:addons/base_location/models/res_partner.py:0
#, python-format
msgid ""
"The city of the partner %(partner)s differs from that in location "
"%(location)s"
msgstr ""
"Die Stadt des Partners %(partner)s weicht von der des Standorts %(location)s "
"ab."

#. module: base_location
#. odoo-python
#: code:addons/base_location/models/res_partner.py:0
#, python-format
msgid ""
"The country of the partner %(partner)s differs from that in location "
"%(location)s"
msgstr ""
"Das Land des Partners %(partner)s unterscheidet sich vom dem des Standorts "
"%(location)s"

#. module: base_location
#. odoo-python
#: code:addons/base_location/models/res_partner.py:0
#, python-format
msgid ""
"The state of the partner %(partner)s differs from that in location "
"%(location)s"
msgstr ""
"Das Bundesland des Partners %(partner)s unterscheidet sich von dem am "
"Standort %(location)s"

#. module: base_location
#. odoo-python
#: code:addons/base_location/models/res_partner.py:0
#, python-format
msgid ""
"The zip of the partner %(partner)s differs from that in location %(location)s"
msgstr ""
"Die PLZ des Partners %(partner)s unterscheidet sich von dem des Standorts "
"%(location)s"

#. module: base_location
#: model:ir.model.fields,help:base_location.field_res_company__zip_id
msgid "Use the city name or the zip code to search the location"
msgstr ""
"Verwenden Sie den Stadtnamen oder die PLZ, um nach diesem Standort zu suchen"

#. module: base_location
#: model:ir.model.constraint,message:base_location.constraint_res_city_name_state_country_uniq
msgid ""
"You already have a city with that name in the same state.The city must have "
"a unique name within it's state and it's country"
msgstr ""
"Es gibt bereits eine Stadt mit diesem Namen in diesem Bundesland. Die Stadt "
"muss einen eindeutigen Namen innerhalb des Bundeslands und des Lands haben."

#. module: base_location
#: model:ir.model.constraint,message:base_location.constraint_res_city_zip_name_city_uniq
msgid ""
"You already have a zip with that code in the same city. The zip code must be "
"unique within it's city"
msgstr ""
"Es gibt bereits eine PLZ mit diesem Code in dieser Stadt. Die PLZ muss "
"innerhalb der Stadt eindeutig sein."

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__name
msgid "ZIP"
msgstr "PLZ"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_company__zip_id
#: model:ir.model.fields,field_description:base_location.field_res_partner__zip_id
#: model:ir.model.fields,field_description:base_location.field_res_users__zip_id
msgid "ZIP Location"
msgstr "Standort-PLZ"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_partner__zip
#: model:ir.model.fields,field_description:base_location.field_res_users__zip
#: model_terms:ir.ui.view,arch_db:base_location.city_zip_form
msgid "Zip"
msgstr "PLZ"

#. module: base_location
#: model:ir.ui.menu,name:base_location.locations_menu_zips
#: model_terms:ir.ui.view,arch_db:base_location.view_city_form
#: model_terms:ir.ui.view,arch_db:base_location.view_res_country_city_better_zip_form
msgid "Zips"
msgstr "PLZ"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city__zip_ids
msgid "Zips in this city"
msgstr "PLZ der Stadt"

#~ msgid "City of Address"
#~ msgstr "Stadt"

#~ msgid "Country state"
#~ msgstr "Bundesland"

#~ msgid "Group By"
#~ msgstr "Gruppiere"

#~ msgid "Latitude"
#~ msgstr "Breitengrad"

#~ msgid "Locations Management"
#~ msgstr "Standorteverwaltung"

#~ msgid "Longitude"
#~ msgstr "Längengrad"

#~ msgid "The country of the city differs from the country in location %s"
#~ msgstr "Das Land der Stadt unterscheidet sich vom Land des Standorts %s"

#~ msgid "The country of the state differs from the country in location %s"
#~ msgstr ""
#~ "Das Land des Bundeslands unterscheidet sich vom Land des Standorts %s"

#~ msgid "The official code for the city"
#~ msgstr "Die offizielle Kennung der Stadt"

#~ msgid "The state of the city differs from the state in location %s"
#~ msgstr ""
#~ "Das Bundesland der Stadt unterscheidet sich vom Bundesland am Standort %s"
