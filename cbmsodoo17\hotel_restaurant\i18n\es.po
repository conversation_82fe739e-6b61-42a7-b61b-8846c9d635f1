# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hotel_restaurant
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-08-12 12:53+0000\n"
"PO-Revision-Date: 2020-08-12 12:53+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__bom_count
msgid "# Bill of Material"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__used_in_bom_count
msgid "# BoM Where Used"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__product_variant_count
msgid "# Product Variants"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__product_count
msgid "# Products"
msgstr "Producto"

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.hotel_restaurant_reservation_report123
msgid "<strong>Customer Name</strong>"
msgstr ""

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.hotel_restaurant_reservation_report123
msgid "<strong>End Date</strong>"
msgstr ""

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.hotel_restaurant_reservation_report123
msgid "<strong>Reservation No</strong>"
msgstr ""

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.hotel_restaurant_reservation_report123
msgid "<strong>Start Date</strong>"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__description_sale
msgid ""
"A description of the Product that you want to communicate to your customers."
" This description will be copied to every Sales Order, Delivery Order and "
"Customer Invoice/Credit Note"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__type
msgid ""
"A storable product is a product for which you manage stock. The Inventory app has to be installed.\n"
"A consumable product is a product for which stock is not managed.\n"
"A service is a non-material product you provide."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__accessory_product_ids
msgid ""
"Accessories show up when the customer reviews the cart before payment "
"(cross-sell strategy)."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__accessory_product_ids
msgid "Accessory Products"
msgstr ""

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_menucard_type_form
msgid "Account Properties"
msgstr "propiedades de cuentas"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__message_needaction
msgid "Action Needed"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__active
msgid "Active"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__activity_ids
msgid "Activities"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__activity_state
msgid "Activity State"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model,name:hotel_restaurant.model_hotel_restaurant_kitchen_order_tickets
msgid "Add BOM to restaurant module"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model,name:hotel_restaurant.model_hotel_restaurant_order_list
msgid "Add restaurant inventory"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__inventory_availability
msgid "Adds an inventory availability status on the web product page."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__alternative_product_ids
msgid "Alternative Products"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__product_template_attribute_value_ids
msgid "Attribute Values"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__available_threshold
msgid "Availability Threshold"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields.selection,name:hotel_restaurant.selection__hotel_restaurant_tables__state__available
msgid "Available"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__available_in_pos
msgid "Available in POS"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__produce_delay
msgid ""
"Average lead time in days to manufacture this product. In the case of multi-"
"level BOM, the manufacturing lead times of the components will be added."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__variant_bom_ids
msgid "BOM Product Variants"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__barcode
msgid "Barcode"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__bom_ids
msgid "Bill of Materials"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__bom_line_ids
msgid "BoM Components"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields.selection,name:hotel_restaurant.selection__hotel_restaurant_tables__state__book
msgid "Booked"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__can_image_1024_be_zoomed
msgid "Can Image 1024 be zoomed"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__can_publish
msgid "Can Publish"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__can_image_variant_1024_be_zoomed
msgid "Can Variant Image 1024 be zoomed"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__purchase_ok
msgid "Can be Purchased"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__rental
msgid "Can be Rent"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__sale_ok
msgid "Can be Sold"
msgstr ""

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.hotel_restaurant_reservation_wizard_view
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.hotel_wizard_view
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_restaurant_reservation_form
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_restaurant_tables_form
msgid "Cancel"
msgstr "Cancelar"

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_restaurant_order_form
msgid "Cancel Order"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields.selection,name:hotel_restaurant.selection__hotel_reservation_order__state__cancel
#: model:ir.model.fields.selection,name:hotel_restaurant.selection__hotel_restaurant_order__state__cancel
#: model:ir.model.fields.selection,name:hotel_restaurant.selection__hotel_restaurant_reservation__state__cancel
msgid "Cancelled"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_tables__capacity
msgid "Capacity"
msgstr "Capacidad"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__cart_qty
msgid "Cart Qty"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__menu_id
msgid "Category"
msgstr "categoría"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__route_from_categ_ids
msgid "Category Routes"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__pos_categ_id
msgid "Category used in the Point of Sale."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__to_weight
msgid ""
"Check if the product should be weighted using the hardware scale "
"integration."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__available_in_pos
msgid "Check if you want this product to appear in the Point of Sale."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__child_id
msgid "Child Categories"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__color
msgid "Color Index"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__combination_indices
msgid "Combination Indices"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__company_id
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__company_id
msgid "Company"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__complete_name
msgid "Complete Name"
msgstr ""

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_restaurant_reservation_form
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_restaurant_tables_form
msgid "Confirm"
msgstr ""

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_reservation_order_form
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_restaurant_order_form
msgid "Confirm Order"
msgstr "Confirmar pedido"

#. module: hotel_restaurant
#: model:ir.model.fields.selection,name:hotel_restaurant.selection__hotel_reservation_order__state__confirm
#: model:ir.model.fields.selection,name:hotel_restaurant.selection__hotel_restaurant_order__state__confirm
#: model:ir.model.fields.selection,name:hotel_restaurant.selection__hotel_restaurant_reservation__state__confirm
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_restaurant_order_filter
msgid "Confirmed"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__standard_price
msgid "Cost"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__cost_currency_id
msgid "Cost Currency"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__cost_method
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__property_cost_method
msgid "Costing Method"
msgstr ""

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_reservation_order_form
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_restaurant_order_form
msgid "Create Invoice"
msgstr "Crear factura"

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.hotel_wizard_view
msgid "Create Kots"
msgstr "Crear Kots"

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_restaurant_reservation_form
msgid "Create Order"
msgstr "Crear pedido"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__create_uid
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__create_uid
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_reservation_order__create_uid
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_kitchen_order_tickets__create_uid
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order__create_uid
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order_list__create_uid
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation__create_uid
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation_wizard__create_uid
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_tables__create_uid
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_wizard__create_uid
msgid "Created by"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__create_date
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__create_date
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_reservation_order__create_date
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_kitchen_order_tickets__create_date
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order__create_date
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order_list__create_date
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation__create_date
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation_wizard__create_date
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_tables__create_date
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_wizard__create_date
msgid "Created on"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__currency_id
msgid "Currency"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__qty_available
msgid ""
"Current quantity of products.\n"
"In a context with a single Stock Location, this includes goods stored at this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"stored in the Stock Location of the Warehouse of this Shop, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__custom_message
msgid "Custom Message"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_reservation_order__partner_id
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order__partner_id
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_reservation_order_filter
msgid "Customer"
msgstr "Cliente"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__sale_delay
msgid "Customer Lead Time"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation__cname
msgid "Customer Name"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__partner_ref
msgid "Customer Ref"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__taxes_id
msgid "Customer Taxes"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_reservation_order__date1
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_kitchen_order_tickets__kot_date
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order__o_date
msgid "Date"
msgstr "Fecha"

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__supplier_taxes_id
msgid "Default taxes used when buying the product."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__taxes_id
msgid "Default taxes used when selling the product."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__uom_id
msgid "Default unit of measure used for all stock operations."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__uom_po_id
msgid ""
"Default unit of measure used for purchase orders. It must be in the same "
"category as the default unit of measure."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__seller_ids
msgid "Define vendor pricelists."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__sale_delay
msgid ""
"Delivery lead time, in days. It's the number of days, promised to the "
"customer, between the confirmation of the sales order and the delivery."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__route_ids
msgid ""
"Depending on the modules installed, this will allow you to define the route "
"of the product: whether it will be bought, manufactured, replenished on "
"order, etc."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__description
msgid "Description"
msgstr "Descripción"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__website_description
msgid "Description for the website"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__description_pickingout
msgid "Description on Delivery Orders"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__description_picking
msgid "Description on Picking"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__description_pickingin
msgid "Description on Receptions"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__website_sequence
msgid "Determine the display order in the Website E-commerce"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__display_name
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__display_name
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_reservation_order__display_name
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_kitchen_order_tickets__display_name
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order__display_name
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order_list__display_name
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation__display_name
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation_wizard__display_name
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_tables__display_name
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_wizard__display_name
#: model:ir.model.fields,field_description:hotel_restaurant.field_report_hotel_restaurant_hotel_restaurant_reservation_report123__display_name
msgid "Display Name"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields.selection,name:hotel_restaurant.selection__hotel_reservation_order__state__done
#: model:ir.model.fields.selection,name:hotel_restaurant.selection__hotel_restaurant_order__state__done
#: model:ir.model.fields.selection,name:hotel_restaurant.selection__hotel_restaurant_reservation__state__done
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_restaurant_reservation_form
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_restaurant_order_filter
msgid "Done"
msgstr "Hecho"

#. module: hotel_restaurant
#: model:ir.model.fields.selection,name:hotel_restaurant.selection__hotel_reservation_order__state__draft
#: model:ir.model.fields.selection,name:hotel_restaurant.selection__hotel_restaurant_order__state__draft
#: model:ir.model.fields.selection,name:hotel_restaurant.selection__hotel_restaurant_reservation__state__draft
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_reservation_order_filter
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_restaurant_order_filter
msgid "Draft"
msgstr "borrador"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation__end_date
msgid "End Date"
msgstr "Fecha fin"

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__property_account_expense_id
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__property_account_expense_categ_id
msgid "Expense Account"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__expense_policy
msgid ""
"Expenses and vendor bills can be re-invoiced to a customer.With this option,"
" a validated expense can be re-invoice to a customer at its cost or sales "
"price."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__product_template_image_ids
msgid "Extra Product Media"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__product_variant_image_ids
msgid "Extra Variant Images"
msgstr ""

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.hotel_restaurant_reservation_wizard_view
msgid "Fill The Dates"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__message_follower_ids
msgid "Followers"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__message_channel_ids
msgid "Followers (Channels)"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: hotel_restaurant
#: model:ir.ui.menu,name:hotel_restaurant.menu_open_hotel_menucard_form
msgid "Food Items"
msgstr "Alimentos"

#. module: hotel_restaurant
#: model:ir.ui.menu,name:hotel_restaurant.menu_action_hotel_menucard_type_view_form
msgid "FoodItem Categories"
msgstr "Categoría s de Artículos de alimentos"

#. module: hotel_restaurant
#: model:ir.ui.menu,name:hotel_restaurant.menu_action_hotel_menucard_type_view_form_parent
msgid "FoodItem Definations"
msgstr "Definición de Artículos de alimento"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__removal_strategy_id
msgid "Force Removal Strategy"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__virtual_available
msgid "Forecast Quantity"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__virtual_available
msgid ""
"Forecast quantity (computed as Quantity On Hand - Outgoing + Incoming)\n"
"In a context with a single Stock Location, this includes goods stored in this location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__free_qty
msgid ""
"Forecast quantity (computed as Quantity On Hand - reserved quantity)\n"
"In a context with a single Stock Location, this includes goods stored in this location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__free_qty
msgid "Free To Use Quantity "
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation_wizard__date_start
msgid "From Date"
msgstr "Desde Fecha"

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_reservation_order_form
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_restaurant_order_form
msgid "Generate KOT"
msgstr "Generate KOT"

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__packaging_ids
msgid "Gives the different ways to package the same product."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__sequence
msgid "Gives the sequence order when displaying a product list"
msgstr ""

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_reservation_order_filter
msgid "Group By..."
msgstr "Agrupar por…"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_wizard__grouped
msgid "Group the kots"
msgstr "Grupo de Kots"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_reservation_order__guest_name
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order__guest_name
msgid "Guest Name"
msgstr "Huésped nombre"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation__folio_id
msgid "Hotel Folio"
msgstr "Hotel Folio"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_reservation_order__folio_id
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order__folio_id
msgid "Hotel Folio Ref"
msgstr ""

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_menucard_type_form
msgid "Hotel Food Items Type"
msgstr "Tipo de alimentos del hotel"

#. module: hotel_restaurant
#: model:ir.actions.act_window,name:hotel_restaurant.action_hotel_menucard_type_view_form
msgid "Hotel FoodItem Type"
msgstr "Tipo de alimento del hotel"

#. module: hotel_restaurant
#: model:ir.actions.act_window,name:hotel_restaurant.action_hotel_menucard_form
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_menucard_tree
msgid "Hotel Menucard"
msgstr "Carta de menú del hotel"

#. module: hotel_restaurant
#: model:ir.actions.report,name:hotel_restaurant.hotel_restaurant_reservation_report1
msgid "Hotel Restaurant Reservation Report"
msgstr ""

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.hotel_restaurant_reservation_wizard_view
msgid "Hotel Restaurant Wizard"
msgstr ""

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.hotel_wizard_view
msgid "Hotel Wizard"
msgstr "Hotel Wizard"

#. module: hotel_restaurant
#: model:ir.model,name:hotel_restaurant.model_hotel_menucard
msgid "Hotel menucard Inherit "
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__id
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__id
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_reservation_order__id
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_kitchen_order_tickets__id
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order__id
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order_list__id
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation__id
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation_wizard__id
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_tables__id
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_wizard__id
#: model:ir.model.fields,field_description:hotel_restaurant.field_report_hotel_restaurant_hotel_restaurant_reservation_report123__id
msgid "ID"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__activity_exception_icon
msgid "Icon"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__message_needaction
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__message_unread
msgid "If checked, new messages require your attention."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__message_has_error
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__active
msgid ""
"If unchecked, it will allow you to hide the product without removing it."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__image_1920
msgid "Image"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__image_1024
msgid "Image 1024"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__image_128
msgid "Image 128"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__image_256
msgid "Image 256"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__image_512
msgid "Image 512"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__standard_price
msgid ""
"In Standard Price & AVCO: value of the product (automatically computed in AVCO).\n"
"        In FIFO: value of the last unit that left the stock (automatically computed).\n"
"        Used to value the product when the purchase cost is not known (e.g. inventory adjustment).\n"
"        Used to compute margins on sale orders."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model,name:hotel_restaurant.model_hotel_reservation_order
msgid "Includes Hotel Reservation Order"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model,name:hotel_restaurant.model_hotel_restaurant_order
msgid "Includes Hotel Restaurant Order"
msgstr "Incluye pedido del restaurante del hotel"

#. module: hotel_restaurant
#: model:ir.model,name:hotel_restaurant.model_hotel_restaurant_reservation
msgid "Includes Hotel Restaurant Reservation"
msgstr "Incluye reserva en el restaurante del hotel"

#. module: hotel_restaurant
#: model:ir.model,name:hotel_restaurant.model_hotel_restaurant_tables
msgid "Includes Hotel Restaurant Table"
msgstr "Incluye mesa en el restaurante del hotel"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__property_account_income_id
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__property_account_income_categ_id
msgid "Income Account"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__incoming_qty
msgid "Incoming"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__default_code
msgid "Internal Reference"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__barcode
msgid "International Article Number used for product identification."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__inventory_availability
msgid "Inventory Availability"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__property_stock_inventory
msgid "Inventory Location"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__valuation
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__property_valuation
msgid "Inventory Valuation"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__invoice_policy
msgid "Invoicing Policy"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__isact
msgid "Is Activity"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__isactivitytype
msgid "Is Activity Type"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_housekeeping_activity_type__ismenutype
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__ismenutype
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_room_amenities_type__ismenutype
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_room_type__ismenutype
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_service_type__ismenutype
#: model:ir.model.fields,field_description:hotel_restaurant.field_product_category__ismenutype
msgid "Is Menu Type"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__is_product_variant
msgid "Is Product Variant"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__is_published
msgid "Is Published"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_h_activity__ismenucard
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__ismenucard
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__isroom
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_room__ismenucard
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_room_amenities__ismenucard
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_services__ismenucard
#: model:ir.model.fields,field_description:hotel_restaurant.field_product_product__ismenucard
msgid "Is Room"
msgstr "Es habitación"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__isroomtype
msgid "Is Room Type"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__isservicetype
msgid "Is Service Type"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__isservice
msgid "Is Service id"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__has_configurable_attributes
msgid "Is a configurable product"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__isamenitype
msgid "Is amenities Type"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__iscategid
msgid "Is categ id"
msgstr ""

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_menucard_tree
msgid "Item Rate"
msgstr "Item Rate"

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_restaurant_kitchen_order_tickets_form
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_restaurant_kitchen_order_tickets_tree
msgid "KOT List"
msgstr "KOT List"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_kitchen_order_tickets__orderno
msgid "KOT Number"
msgstr "KOT numero"

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__property_account_income_id
msgid ""
"Keep this field empty to use the default value from the product category."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__property_account_expense_id
msgid ""
"Keep this field empty to use the default value from the product category. If"
" anglo-saxon accounting with automated valuation method is configured, the "
"expense account on the product category will be used."
msgstr ""

#. module: hotel_restaurant
#: model:ir.actions.act_window,name:hotel_restaurant.open_view_hotel_restaurant_kitchen_order_tickets_form_tree
msgid "Kitchen Order List"
msgstr "Lista de pedidos de cocina"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order_list__kot_order_list
msgid "Kot Order List"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard____last_update
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type____last_update
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_reservation_order____last_update
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_kitchen_order_tickets____last_update
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order____last_update
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order_list____last_update
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation____last_update
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation_wizard____last_update
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_tables____last_update
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_wizard____last_update
#: model:ir.model.fields,field_description:hotel_restaurant.field_report_hotel_restaurant_hotel_restaurant_reservation_report123____last_update
msgid "Last Modified on"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__write_uid
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__write_uid
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_reservation_order__write_uid
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_kitchen_order_tickets__write_uid
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order__write_uid
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order_list__write_uid
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation__write_uid
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation_wizard__write_uid
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_tables__write_uid
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_wizard__write_uid
msgid "Last Updated by"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__write_date
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__write_date
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_reservation_order__write_date
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_kitchen_order_tickets__write_date
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order__write_date
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order_list__write_date
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation__write_date
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation_wizard__write_date
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_tables__write_date
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_wizard__write_date
msgid "Last Updated on"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__location_id
msgid "Location"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__valuation
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard_type__property_valuation
msgid ""
"Manual: The accounting entries to value the inventory are not posted automatically.\n"
"        Automated: An accounting entry is automatically created to value the inventory when a product enters or leaves the company.\n"
"        "
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__service_type
msgid ""
"Manually set quantities on order: Invoice based on the manually entered quantity, without creating an analytic account.\n"
"Timesheets on contract: Invoice based on the tracked hours on the related timesheet.\n"
"Create a task and track hours: Create a task on the sales order validation and track the work hours."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__mrp_product_qty
msgid "Manufactured"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__produce_delay
msgid "Manufacturing Lead Time"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__sale_line_warn_msg
msgid "Message for Sales Order Line"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__message_ids
msgid "Messages"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__orderpoint_ids
msgid "Minimum Stock Rules"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__name
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__name
msgid "Name"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__activity_summary
msgid "Next Activity Summary"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__pricelist_item_count
msgid "Number of price rules"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__message_unread_counter
msgid "Number of unread messages"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order_list__o_l
msgid "O L"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order_list__o_list
msgid "O List"
msgstr ""

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_reservation_order_form
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_reservation_order_tree
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_restaurant_order_form
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_restaurant_order_tree
msgid "Order"
msgstr "Pedido"

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_reservation_order_filter
msgid "Order Date"
msgstr "Fecha pedido"

#. module: hotel_restaurant
#: model:ir.model.fields.selection,name:hotel_restaurant.selection__hotel_reservation_order__state__order
#: model:ir.model.fields.selection,name:hotel_restaurant.selection__hotel_restaurant_order__state__order
#: model:ir.model.fields.selection,name:hotel_restaurant.selection__hotel_restaurant_reservation__state__order
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_reservation_order_filter
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_restaurant_order_filter
msgid "Order Done"
msgstr "Pedido Hecho"

#. module: hotel_restaurant
#: model:ir.actions.act_window,name:hotel_restaurant.open_view_hotel_reservation_order_form_tree
#: model:ir.actions.act_window,name:hotel_restaurant.open_view_hotel_restaurant_order_form_tree1
msgid "Order Generate"
msgstr "Generar pedido"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order_list__order_l
msgid "Order L"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_reservation_order__order_list
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_kitchen_order_tickets__kot_list
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order__order_list
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation__order_list_ids
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_reservation_order_form
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_restaurant_kitchen_order_tickets_form
msgid "Order List"
msgstr "Lista de pedido"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_reservation_order__order_number
msgid "Order No"
msgstr "Pedido No"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_kitchen_order_tickets__resno
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order__order_no
msgid "Order Number"
msgstr "Numero de Pedido"

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_reservation_order_filter
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_restaurant_order_filter
msgid "Order date"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__invoice_policy
msgid ""
"Ordered Quantity: Invoice quantities ordered by the customer.\n"
"Delivered Quantity: Invoice quantities delivered to the customer."
msgstr ""

#. module: hotel_restaurant
#: model:ir.ui.menu,name:hotel_restaurant.menu_open_view_hotel_reservation_order_form_tree
msgid "Orders"
msgstr "Pedidos"

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_reservation_order_filter
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_restaurant_order_filter
msgid "Orders that KOT is generated"
msgstr ""

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_reservation_order_filter
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_restaurant_order_filter
msgid "Orders that haven't yet been confirmed"
msgstr "Pedidos que Todavía no has sido confirmados"

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_reservation_order_filter
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_restaurant_order_filter
msgid "Orders that invoice is generated"
msgstr "Pedidos que han generado factura"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__outgoing_qty
msgid "Outgoing"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__parent_id
msgid "Parent Category"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__parent_path
msgid "Parent Path"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__pos_categ_id
msgid "Point of Sale Category"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__price
msgid "Price"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__list_price
msgid "Price at which the product is sold to customers."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__pricelist_id
msgid "Pricelist"
msgstr ""

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.hotel_restaurant_reservation_wizard_view
msgid "Print Report"
msgstr "Imprimir Informe"

#. module: hotel_restaurant
#: model:ir.model,name:hotel_restaurant.model_product_product
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__product_variant_id
msgid "Product"
msgstr "Producto"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__attribute_line_ids
msgid "Product Attributes"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model,name:hotel_restaurant.model_product_category
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__categ_id
msgid "Product Category"
msgstr "Categoría de Producto"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__packaging_ids
msgid "Product Packages"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__product_tmpl_id
msgid "Product Template"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__type
msgid "Product Type"
msgstr ""

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_menucard_form
msgid "Product Variant"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__product_id
msgid "Product_id"
msgstr "Product_id"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__property_stock_production
msgid "Production Location"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__product_variant_ids
msgid "Products"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__lst_price
msgid "Public Price"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__description_purchase
msgid "Purchase Description"
msgstr "Descripción de la compra"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__uom_po_id
msgid "Purchase Unit of Measure"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__putaway_rule_ids
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__putaway_rule_ids
msgid "Putaway Rules"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order_list__item_qty
msgid "Qty"
msgstr "Ctdad"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__qty_available
msgid "Quantity On Hand"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__quantity_svl
msgid "Quantity Svl"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__incoming_qty
msgid ""
"Quantity of planned incoming products.\n"
"In a context with a single Stock Location, this includes goods arriving to this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods arriving to the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods arriving to any Stock Location with 'internal' type."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__outgoing_qty
msgid ""
"Quantity of planned outgoing products.\n"
"In a context with a single Stock Location, this includes goods leaving this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods leaving the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods leaving any Stock Location with 'internal' type."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order_list__item_rate
msgid "Rate"
msgstr "Tarifa"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__rating_ids
msgid "Rating"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__rating_avg
msgid "Rating Average"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__rating_last_feedback
msgid "Rating Last Feedback"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__rating_last_image
msgid "Rating Last Image"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__rating_last_value
msgid "Rating Last Value"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__rating_count
msgid "Rating count"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__expense_policy
msgid "Re-Invoice Expenses"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__visible_expense_policy
msgid "Re-Invoice Policy visible"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__rating_last_feedback
msgid "Reason of the rating"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__code
msgid "Reference"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__reordering_max_qty
msgid "Reordering Max Qty"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__reordering_min_qty
msgid "Reordering Min Qty"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__nbr_reordering_rules
msgid "Reordering Rules"
msgstr ""

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.hotel_restaurant_reservation_report123
msgid "Reservation List"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_reservation_order__reservation_id
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation__name
msgid "Reservation No"
msgstr "Reservación No"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__responsible_id
msgid "Responsible"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__activity_user_id
msgid "Responsible User"
msgstr ""

#. module: hotel_restaurant
#: model:ir.ui.menu,name:hotel_restaurant.hotel_restaurant_menu
msgid "Restaurant"
msgstr "Restaurante"

#. module: hotel_restaurant
#: model:ir.actions.act_window,name:hotel_restaurant.action_hotel_restaurant_wizard
msgid "Restaurant Reservation List"
msgstr "Lista de Reservación restaurante"

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__website_id
msgid "Restrict publishing to this website."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_reservation_order__room_no
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_kitchen_order_tickets__room_no
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order__room_no
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation__room_no
msgid "Room No"
msgstr "Habitación No"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__route_ids
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__route_ids
msgid "Routes"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__is_seo_optimized
msgid "SEO optimized"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__message_has_sms_error
msgid "SMS Delivery error"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__description_sale
msgid "Sales Description"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__sale_line_warn
msgid "Sales Order Line"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__list_price
msgid "Sales Price"
msgstr ""

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_reservation_order_filter
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_restaurant_order_filter
msgid "Search Table Order"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__categ_id
msgid "Select category for the current product"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__sale_line_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__sequence
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order_list__name
msgid "Sequence"
msgstr "secuencia"

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard_type__removal_strategy_id
msgid ""
"Set a specific removal strategy that will be used regardless of the source "
"location for this product category"
msgstr ""

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_restaurant_reservation_form
msgid "Set to Draft"
msgstr "Ajustar a borrador reserva"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__website_size_x
msgid "Size X"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__website_size_y
msgid "Size Y"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__sales_count
msgid "Sold"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__cost_method
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard_type__property_cost_method
msgid ""
"Standard Price: The products are valued at their standard cost defined on the product.\n"
"        Average Cost (AVCO): The products are valued at weighted average cost.\n"
"        First In First Out (FIFO): The products are valued supposing those that enter the company first will also leave it first.\n"
"        "
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation__start_date
msgid "Start Date"
msgstr "Fecha de inicio"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_reservation_order__state
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order__state
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation__state
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_tables__state
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_reservation_order_filter
msgid "State"
msgstr "Estado"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__state
msgid "Status"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__property_stock_account_input_categ_id
msgid "Stock Input Account"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__property_stock_journal
msgid "Stock Journal"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__stock_move_ids
msgid "Stock Move"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__property_stock_account_output_categ_id
msgid "Stock Output Account"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__stock_quant_ids
msgid "Stock Quant"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__property_stock_valuation_account_id
msgid "Stock Valuation Account"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__stock_valuation_layer_ids
msgid "Stock Valuation Layer"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__website_style_ids
msgid "Styles"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order__amount_subtotal
msgid "SubTotal"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_reservation_order__amount_subtotal
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order_list__price_subtotal
msgid "Subtotal"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__alternative_product_ids
msgid ""
"Suggest alternatives to your customer (upsell strategy). Those products show"
" up on the product page."
msgstr ""

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_restaurant_tables_form
msgid "Supplier Invoice"
msgstr ""

#. module: hotel_restaurant
#: model:ir.ui.menu,name:hotel_restaurant.menu_open_view_hotel_restaurant_reservation_form_tree
msgid "Table Booking"
msgstr "Reserva de mesa"

#. module: hotel_restaurant
#: model:ir.ui.menu,name:hotel_restaurant.menu_open_view_hotel_restaurant_order_form_tree
msgid "Table Order"
msgstr "Pedido mesa"

#. module: hotel_restaurant
#: model:ir.actions.act_window,name:hotel_restaurant.open_view_hotel_restaurant_reservation_form_tree
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_restaurant_reservation_tree
msgid "Table Reservation"
msgstr "Reserva de Mesas"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_reservation_order__table_no
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_kitchen_order_tickets__tableno
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order__table_no
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation__tableno
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_tables__name
msgid "Table number"
msgstr "Mesa numero"

#. module: hotel_restaurant
#: model:ir.ui.menu,name:hotel_restaurant.menu_open_view_hotel_restaurant_tables_form_tree
msgid "Tables"
msgstr "Mesas"

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_restaurant_tables_tree
msgid "Tables Detail"
msgstr "Detalle de las mesas"

#. module: hotel_restaurant
#: model:ir.actions.act_window,name:hotel_restaurant.open_view_hotel_restaurant_tables_form_tree
msgid "Tables Details"
msgstr "Detalle de las mesas"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_reservation_order__tax
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order__tax
msgid "Tax (%) "
msgstr "Impuesto (%)"

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__valid_product_template_attribute_line_ids
msgid "Technical compute"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__pricelist_id
msgid ""
"Technical field. Used for searching on pricelists, not stored in database."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__stock_move_ids
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__stock_quant_ids
msgid "Technical: used to compute quantities."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__state
msgid "Tells the user if room is available of booked."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard_type__property_account_expense_categ_id
msgid ""
"The expense is accounted for when a vendor bill is validated, except in "
"anglo-saxon accounting with perpetual inventory valuation in which case the "
"expense (Cost of Goods Sold account) is recognized at the customer invoice "
"validation."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__website_url
msgid "The full URL to access the document through the website."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard_type__product_count
msgid ""
"The number of products under this category (Does not consider the children "
"categories)"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__public_categ_ids
msgid ""
"The product will be available in each mentioned eCommerce category. Go to "
"Shop > Customize and enable 'eCommerce categories' to view all eCommerce "
"categories."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__lst_price
msgid ""
"The sale price is managed from the product template. Click on the 'Configure"
" Variants' button to set the extra attribute prices."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard_type__property_account_income_categ_id
msgid "This account will be used when validating a customer invoice."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__price_extra
msgid "This is the sum of the extra price of all attributes"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__property_stock_production
msgid ""
"This stock location will be used, instead of the default one, as the source "
"location for stock moves generated by manufacturing orders."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__property_stock_inventory
msgid ""
"This stock location will be used, instead of the default one, as the source "
"location for stock moves generated when you do an inventory."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__responsible_id
msgid ""
"This user will be responsible of the next activities related to logistic "
"operations for this product."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_reservation_wizard__date_end
msgid "To Date"
msgstr "Hasta fecha"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__to_weight
msgid "To Weigh With Scale"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_reservation_order__amount_total
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order__amount_total
msgid "Total"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard_type__total_route_ids
msgid "Total routes"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__service_type
msgid "Track Service"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__tracking
msgid "Tracking"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__uom_id
msgid "Unit of Measure"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__uom_name
msgid "Unit of Measure Name"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__message_unread
msgid "Unread Messages"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__message_unread_counter
msgid "Unread Messages Counter"
msgstr ""

#. module: hotel_restaurant
#: model_terms:ir.ui.view,arch_db:hotel_restaurant.view_hotel_restaurant_tables_form
msgid "Update"
msgstr "Actualizar"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__valid_product_template_attribute_line_ids
msgid "Valid Product Attribute Lines"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__value_svl
msgid "Value Svl"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__image_variant_1920
msgid "Variant Image"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__image_variant_1024
msgid "Variant Image 1024"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__image_variant_128
msgid "Variant Image 128"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__image_variant_256
msgid "Variant Image 256"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__image_variant_512
msgid "Variant Image 512"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__price_extra
msgid "Variant Price Extra"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__variant_seller_ids
msgid "Variant Seller"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__supplier_taxes_id
msgid "Vendor Taxes"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__seller_ids
msgid "Vendors"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__website_published
msgid "Visible on current website"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__volume
msgid "Volume"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__volume_uom_name
msgid "Volume unit of measure label"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_reservation_order__waitername
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_kitchen_order_tickets__w_name
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_restaurant_order__waiter_name
msgid "Waiter Name"
msgstr "Nombre camarero"

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__warehouse_id
msgid "Warehouse"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__website_id
msgid "Website"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__public_categ_ids
msgid "Website Product Category"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__website_sequence
msgid "Website Sequence"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__website_url
msgid "Website URL"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__website_meta_description
msgid "Website meta description"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__website_meta_keywords
msgid "Website meta keywords"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__website_meta_title
msgid "Website meta title"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__website_meta_og_img
msgid "Website opengraph image"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__weight
msgid "Weight"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,field_description:hotel_restaurant.field_hotel_menucard__weight_uom_name
msgid "Weight unit of measure label"
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard_type__property_stock_valuation_account_id
msgid ""
"When automated inventory valuation is enabled on a product, this account "
"will hold the current value of the products."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard_type__property_stock_account_input_categ_id
msgid ""
"When doing automated inventory valuation, counterpart journal items for all incoming stock moves will be posted in this account,\n"
"                unless there is a specific valuation account set on the source location. This is the default value for all products in this category.\n"
"                It can also directly be set on each product."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard_type__property_stock_account_output_categ_id
msgid ""
"When doing automated inventory valuation, counterpart journal items for all outgoing stock moves will be posted in this account,\n"
"                unless there is a specific valuation account set on the destination location. This is the default value for all products in this category.\n"
"                It can also directly be set on each product."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_menucard_type__property_stock_journal
msgid ""
"When doing automated inventory valuation, this is the Accounting Journal in "
"which entries will be automatically posted when stock moves are processed."
msgstr ""

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_restaurant_order__partner_id
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_restaurant_reservation__cname
msgid "Will show customer name corresponding to selected room no."
msgstr "Mostrara nombre de cliente correspondiendo al numero de Habitación Seleccionado"

#. module: hotel_restaurant
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_restaurant_order__room_no
#: model:ir.model.fields,help:hotel_restaurant.field_hotel_restaurant_reservation__room_no
msgid ""
"Will show list of currently occupied room no that belongs to selected shop."
msgstr "Mostrara lista de numero de Habitación actualmente ocupada que pertenezcan al comercio selecionado."

#. module: hotel_restaurant
#: model:ir.model,name:hotel_restaurant.model_hotel_menucard_type
msgid "amenities Type"
msgstr "Tipo de Instalaciones"

#. module: hotel_restaurant
#: model:ir.model,name:hotel_restaurant.model_hotel_restaurant_reservation_wizard
msgid "hotel_restaurant_reservation_wizard"
msgstr "hotel_restaurante_Reservación_wizard"

#. module: hotel_restaurant
#: model:ir.model,name:hotel_restaurant.model_hotel_restaurant_wizard
msgid "hotel_restaurant_wizard"
msgstr "hotel_restaurante_wizard"

#. module: hotel_restaurant
#: model:ir.model,name:hotel_restaurant.model_report_hotel_restaurant_hotel_restaurant_reservation_report123
msgid "report hotel restaurant hotel restaurant reservation report"
msgstr ""
