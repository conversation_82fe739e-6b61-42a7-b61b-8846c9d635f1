<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="appointment_see_all" model="ir.rule">
            <field name="name">Appointment: See All</field>
            <field name="model_id" ref="calendar.model_calendar_event"/>
            <field name="groups" eval="[(4, ref('base.group_system'))]"/>
            <field name="domain_force">[(1, '=', 1)]</field>
        </record>
        <record id="appointment_see_own_and_assigned" model="ir.rule">
            <field name="name">Appointment: See Own and Assigned</field>
            <field name="model_id" ref="calendar.model_calendar_event"/>
            <field name="groups" eval="[(4, ref('base.group_user'))]"/>
            <field name="domain_force">
                ['|', ('user_id', '=', user.id), ('partner_ids', 'in', [user.partner_id.id])]
            </field>
        </record>
    </data>
</odoo>