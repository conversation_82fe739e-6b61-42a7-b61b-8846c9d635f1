<?xml version='1.0' encoding='UTF-8' ?>
<odoo>
    <template id="show_sign_in11" inherit_id="website.navbar_nav">
        <xpath expr="//ul[@id='top_menu']" position="inside">
            <li class="booking-menu">
                <a t-attf-href="/product_screen/">
                    <b style="font-size: 20px;">Booking</b>
                </a>
            </li>
        </xpath>
    </template>
    <template id="product_show" name="Product show">
        <t t-call="website.layout">
            <div id="wrap" class="pattern_bg">
                <div class="oe_structure"/>
                <div class="container">
                <section id="reservation_hero_section" class="Chambres fix-margin">
                    <div class="container zindex">
                        <div class="col-lg-12 banner_cnt">
                            <h2>Reservation</h2>
                            <p>Welcome to our hotel, where luxury meets comfort in the heart. Our boutique hotel is a haven of sophistication, blending modern elegance with timeless charm. Whether you're here for business or leisure, our attentive staff and exquisite amenities ensure a memorable stay.</p>
                            
                        </div>

	                </div>
	   
	            </section>
<!--                    <form action="/product_search/" method="post" class="search-form" style="background-color: beige;">-->
<!--                        <div class="search-input">-->
<!--                            <p>Check-in Date</p>-->
<!--                            <input type="date" name="from_date" id="from_date" class="input-field"/>-->
<!--                        </div>-->
<!--                        <div class="search-input">-->
<!--                            <p>Check-out Date</p>-->
<!--                            <input type="date" name="to_date" id="to_date" class="input-field"/>-->
<!--                        </div>-->
<!--                        <div class="check-button-container">-->
<!--                            <input type="submit" value="Check" class="check-button"/>-->
<!--                        </div>-->
<!--                    </form>-->
                    <form class="hotel_form" action="/product_search/" type="post">
                        <div style="position: initial; margin: 10px 0 0 0; z-index: 20; display: grid;
                                    grid-template-columns: repeat(3,1fr); background: #fff; border-radius: 4px;
                                    width:100%;border: 1px solid black">
                            <div class="search-input">
                            <p>Check-in Date</p>
                            <input type="date" name="from_date" id="from_date" class="input-field"/>
                        </div>
                        <div class="search-input">
                            <p>Check-out Date</p>
                            <input type="date" name="to_date" id="to_date" class="input-field"/>
                        </div>
                        <div class="check-button-container">
                            <input type="submit" value="Check" class="check-button"/>
                        </div>
<!--                            <div style="cursor: pointer; padding: 10px 15px; height: 100%; position: relative; display: grid;">-->
<!--                                <p class="lead" style="font-family: 'Arial', sans-serif; margin-left: 3px;">Check-in Date</p>-->
<!--                                <input type="date" style="appearance: none; background-color: #f5f5f5; border: none; padding: 10px;-->
<!--                                            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; font-size: 14px;-->
<!--                                            color: #333; border-radius: 6px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);-->
<!--                                            outline: none; transition: background-color 0.3s, box-shadow 0.3s; width: 100%; margin-bottom: 0px;"-->
<!--                                       name="from_date" id="from_date"/>-->
<!--                            </div>-->
<!--                            <div style="cursor: pointer; padding: 10px 15px; height: 100%; position: relative; display: grid;">-->
<!--                                <p class="lead" style="font-family: 'Arial', sans-serif; margin-left: 3px;">Check-out Date</p>-->
<!--                                <input type="date" style="appearance: none; background-color: #f5f5f5; border: none; padding: 10px;-->
<!--                                            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; font-size: 14px;-->
<!--                                            color: #333; border-radius: 6px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);-->
<!--                                            outline: none; transition: background-color 0.3s, box-shadow 0.3s; width: 100%; margin-bottom: 0px;"-->
<!--                                       name="to_date" id="to_date"/>-->
<!--                            </div>-->
<!--                            <div style="cursor: pointer; padding: 10px 15px; height: 100%; position: relative; display: grid;-->
<!--                                        align-items: center; justify-content: center; align-content: center; justify-items: center;">-->
<!--                                <input type="submit" value="Check" class="check-button"/>-->
<!--                            </div>-->
                        </div>
                    </form>
                    <div class='row booking_info row' style="position: inherit;
                                                            margin-top: 30px;
                                                            justify-content: space-around;
                                                            border-radius: 4px;
                                                            width: 100%;
                                                            font-family: Comic Sans MS, monospace;
                                                            font-size: initial;">
                        <t t-if="room_res">
                            <t>
                                <div class="col-md-3"
                                     style="background:#114052; width:25%; padding:10px; font-weight:bold; color:#fff;">

                                </div>
                                <div class="col-md-5"
                                     style="background:#114052;width:55%; padding:10px; font-weight:bold; color:#fff; text-align:left">
                                    <span style="font-family: 'Arial', sans-serif; font-weight: bold; color: white; margin-left: -34px">Room type</span>
                                </div>
                                <div class="col-md-4"
                                     style="background:#114052; width:20%; padding:10px; font-weight:bold; color:#fff">
                                    <span style="font-family: 'Arial', sans-serif; font-weight: bold; color: white; margin-left: -128px">Room Booking</span>
                                </div>
                            </t>
                            <form action="/product/reserv/" id="room_form" style="width: 100%;">
                                <t t-foreach="room_res" t-as="td_product1">
                                    <t t-if="website.get_type(td_product1)">
                                        <div class="roomspercoloumn row" style="border: 2px solid #114052; background-color:#fff;">
                                            <div class="col-md-3" style="margin-top:50px;">
                                                <div class="imagescoloumn">
                                                    <div class="image-row">
                                                        <div class="demo" style="margin-top:-27px;">
                                                            <div class="item">
                                                                    <ul class="gallery list-unstyled cS-hidden image-gallery-cls">
                                                                        <t t-if="website.check_next_image(td_product1,0)">
                                                                            <li t-att-data-thumb="website.image_url_new(td_product1['image'], 'img', None if product_image_big else '512x512')[0]"
                                                                                style="width:100px !important;">
                                                                                <a class="example-image-link"
                                                                                   t-att-href="website.image_url_new(td_product1['image'], 'img', None if product_image_big else '512x512')[0]"
                                                                                   data-lightbox="example-1">
                                                                                    <img class="example-image"
                                                                                         t-att-src="website.image_url_new(td_product1['image'], 'img', None if product_image_big else '512x512')[0]"
                                                                                         style="width: 450px; height: 250px;"/>
                                                                                </a>
                                                                            </li>
                                                                        </t>
                                                                        <t t-if="website.check_next_image(td_product1,1)">
                                                                            <li t-att-data-thumb="website.image_url_new(td_product1['image'], 'img', None if product_image_big else '512x512')[1]"
                                                                                style="width:100px !important;">
                                                                                <a class="example-image-link"
                                                                                   t-att-href="website.image_url_new(td_product1['image'], 'img', None if product_image_big else '512x512')[1]"
                                                                                   data-lightbox="example-1">
                                                                                    <img class="example-image"
                                                                                         t-att-src="website.image_url_new(td_product1['image'], 'img', None if product_image_big else '512x512')[1]"
                                                                                         style="width: 450px; height: 250px;"/>
                                                                                </a>
                                                                            </li>
                                                                        </t>
                                                                        <t t-if="website.check_next_image(td_product1,2)">
                                                                            <li t-att-data-thumb="website.image_url_new(td_product1['image'], 'img', None if product_image_big else '512x512')[2]"
                                                                                style="width:100px !important;">
                                                                                <a class="example-image-link"
                                                                                   t-att-href="website.image_url_new(td_product1['image'], 'img', None if product_image_big else '512x512')[2]"
                                                                                   data-lightbox="example-1">
                                                                                    <img class="example-image"
                                                                                         t-att-src="website.image_url_new(td_product1['image'], 'img', None if product_image_big else '512x512')[2]"
                                                                                         style="width: 450px; height: 250px;"/>
                                                                                </a>
                                                                            </li>
                                                                        </t>
                                                                        <t t-if="website.check_next_image(td_product1,0)">
                                                                            <li t-att-data-thumb="website.image_url_new(td_product1['image'], 'img', None if product_image_big else '512x512')[0]"
                                                                                style="width:100px !important;">
                                                                                <a class="example-image-link"
                                                                                   t-att-href="website.image_url_new(td_product1['image'], 'img', None if product_image_big else '512x512')[0]"
                                                                                   data-lightbox="example-1">
                                                                                    <img class="example-image"
                                                                                         t-att-src="website.image_url_new(td_product1['image'], 'img', None if product_image_big else '512x512')[0]"
                                                                                         style="width: 450px; height: 250px;"/>
                                                                                </a>
                                                                            </li>
                                                                        </t>
                                                                        <t t-if="website.check_next_image(td_product1,1)">
                                                                            <li t-att-data-thumb="website.image_url_new(td_product1['image'], 'img', None if product_image_big else '512x512')[1]"
                                                                                style="width:100px !important;">
                                                                                <a class="example-image-link"
                                                                                   t-att-href="website.image_url_new(td_product1['image'], 'img', None if product_image_big else '512x512')[1]"
                                                                                   data-lightbox="example-1">
                                                                                    <img class="example-image"
                                                                                         t-att-src="website.image_url_new(td_product1['image'], 'img', None if product_image_big else '512x512')[1]"
                                                                                         style="width: 450px; height: 250px;"/>
                                                                                </a>
                                                                            </li>
                                                                        </t>
                                                                    </ul>
                                                            </div>
                                                            <div>
                                                                <strong style="color: black; font-size: 24px; display: block; margin-bottom: 15px;">
                                                                    <span style="font-family: 'Arial', sans-serif; font-weight: bold;" t-esc="td_product1['type'][0]"/>
                                                                </strong>
                                                                <div class="col-md-5 orders">
                                                                    <div class="tab-container" style="margin-top: 15px;">
                                                                        <a href="#" class="tab-link room-tab" style="color: white;">Room Information</a>
                                                                        <a href="#" class="tab-link services-tab" style="color: white;">Facilities and Services</a>
                                                                        <a href="#" class="tab-link amenities-tab" style="color: white;">Amenities</a>
                                                                    </div>
                                                                    <div id="room-div">
                                                                        <p t-raw="td_product1['description']"></p>
                                                                    </div>
                                                                    <div id="services-div"
                                                                         style="overflow-y: auto; padding: 10px 10px 0 0; text-align: justify; display: none;">
                                                                        <ul class="services-list">
                                                                            <t t-foreach="td_product1['services']" t-as="service">
                                                                                <li>
                                                                                    <t t-if="service.image_1920">
                                                                                        <img class="tiny-square-image" t-att-src="'data:image/png;base64,' + service.image_1920.decode('utf-8')"/>
                                                                                    </t>
                                                                                    <span class="service-name" t-esc="service.name"/>
                                                                                </li>
                                                                                <br></br>
                                                                            </t>
                                                                        </ul>
                                                                    </div>
                                                                    <div id="amenities-div"
                                                                         style="overflow-y: auto; padding: 10px 10px 0 0; text-align: justify; display: none;">
                                                                        <ul class="amenities-list">
                                                                            <t t-foreach="td_product1['amenities']" t-as="amenity">
                                                                                <li>
                                                                                    <i class="fa fa-circle-o"></i>
                                                                                    <span t-esc="amenity"/>
                                                                                </li>
                                                                            </t>
                                                                        </ul>
                                                                    </div>
                                                                    </div>
                                                                </div>
                                                            <br></br>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
<!--                                            <div class="col-md-5" style="width:45%; padding:10px; margin-top:5px;">-->
<!--                                                <strong>-->
<!--                                                    <span t-esc="td_product1['type'][0]"/>-->
<!--                                                </strong>-->
<!--                                                <br/>-->
<!--                                                <br/>-->
<!--                                                <div-->
<!--                                                        style="height:190px; overflow-y:auto; padding:10px 10px 0 0; text-align:justify; ">-->
<!--                                                    <span t-raw="td_product1['description']"/>-->
<!--                                                </div>-->
<!--                                                <div class="col-md-5 orders">-->
<!--                                                    <div class="tab-container">-->
<!--                                                        <a href="#" class="facilities-tab">Facilities</a>-->
<!--                                                        <a href="#" class="tab-link services-tab">Facilities and Services</a>-->
<!--                                                        <a href="#" class="tab-link amenities-tab">Amenities</a>-->
<!--                                                    </div>-->
<!--                                                    <br/>-->
<!--                                                    <br/>-->
<!--&lt;!&ndash;                                                    <div id="facilities-div"&ndash;&gt;-->
<!--&lt;!&ndash;                                                         style="overflow-y: auto; padding: 10px 10px 0 0; text-align: justify; width: 200%;">&ndash;&gt;-->
<!--&lt;!&ndash;                                                        <span t-raw="td_product1['facilities']"/>&ndash;&gt;-->
<!--&lt;!&ndash;                                                    </div>&ndash;&gt;-->

<!--                                                    <div id="services-div"-->
<!--                                                         style="overflow-y: auto; padding: 10px 10px 0 0; text-align: justify; display: none;">-->
<!--                                                        <ul class="services-list">-->
<!--                                                            <t t-foreach="td_product1['services']" t-as="service">-->
<!--                                                                <li>-->
<!--                                                                    <img class="tiny-square-image" t-att-src="'data:image/png;base64,' + service.image_1920.decode('utf-8')"/>-->
<!--                                                                    <span class="service-name" t-esc="service.name"/>-->
<!--                                                                </li>-->
<!--                                                                <br></br>-->
<!--                                                            </t>-->
<!--                                                        </ul>-->
<!--                                                    </div>-->
<!--                                                    <div id="amenities-div"-->
<!--                                                         style="overflow-y: auto; padding: 10px 10px 0 0; text-align: justify; display: none;">-->
<!--                                                        <ul class="amenities-list">-->
<!--                                                            <t t-foreach="td_product1['amenities']" t-as="amenity">-->
<!--                                                                <li>-->
<!--                                                                    <i class="fa fa-circle-o"></i>-->
<!--                                                                    <span t-esc="amenity"/>-->
<!--                                                                </li>-->
<!--                                                            </t>-->
<!--                                                        </ul>-->
<!--                                                    </div>-->
<!--                                                </div>-->
<!--                                                <br></br>-->
<!--                                            </div>-->
                                            <div class="col-md-4"
                                                 style="width:40%; padding:10px;  margin-top:13px; float:right; margin-left: 30%;">
                                                <div style="background:#114052; text-align:center;">
                                                    <span style="font-weight:bold; font-size:20px; color:#fff">
                                                        <span t-esc="td_product1['currency']"/>
                                                    </span>
                                                    <span style="font-weight:bold; color:#fff; font-size:20px;">
                                                        <span t-esc="td_product1.get('price', 0.0)"/>
                                                    </span>
                                                </div>
                                                <br/>
                                                <span style="line-height:40px;">Name:</span>
                                                <input t-att-name="'name_%s' % str(td_product1['room_type_id'])"
                                                       type="text" class="form-control"
                                                       style="float:right; clear:right; width:55%;"/>

                                                <br/>
                                                <span style="line-height:40px;">No Of Rooms</span>
                                                <select t-att-name="'no_room_%s' % str(td_product1['room_type_id'])"
                                                        class="form-control"
                                                        style="float:right; clear:right; width:55%; ">
                                                    <t t-if="td_product1['count']">
                                                        <option>0</option>
                                                        <t t-foreach="td_product1['count']" t-as="room">
                                                            <t t-foreach="room" t-as="room1">
                                                                <option t-att-value="room1">
                                                                    <t t-esc="room1"/>
                                                                </option>
                                                            </t>
                                                        </t>
                                                    </t>
                                                </select>
                                                <br/>
                                                <span style="line-height:40px;">Adult</span>
                                                <select t-att-name="'adult_%s' % str(td_product1['room_type_id'])"
                                                        class="form-control adult"
                                                        style="float:right; clear:right; width:55%; "
                                                        required="required">
                                                    <t t-if="td_product1['adult']">
                                                        <option>0</option>
                                                        <t t-foreach="td_product1['adult']" t-as="room1111">
                                                            <t t-foreach="room1111" t-as="roommmm11">
                                                                <option t-att-value="roommmm11">
                                                                    <t t-esc="roommmm11"/>
                                                                </option>
                                                            </t>
                                                        </t>
                                                    </t>
                                                </select>
                                                <br/>
                                                <span style="line-height:40px;">Children</span>
                                                <select t-att-name="'child_%s' % str(td_product1['room_type_id'])"
                                                        class="form-control"
                                                        style="float:right; clear:right; width:55%; ">
                                                    <t t-if="td_product1['child']">
                                                        <option>0</option>
                                                        <t t-foreach="td_product1['child']" t-as="rchild">
                                                            <t t-foreach="rchild" t-as="rc">
                                                                <option t-att-value="rc">
                                                                    <t t-esc="rc"/>
                                                                </option>
                                                            </t>
                                                        </t>
                                                    </t>
                                                </select>
                                                <br/>
                                                <span style="line-height:40px;">Select A Room :</span>
                                                <check style="padding-left: 64px;">
                                                    <input t-att-name="'sel_%s' % str(td_product1['room_type_id'])"
                                                           t-att-id="td_product1['room_type_id']" type="Checkbox"
                                                           class="test" name=""/>
                                                </check>
                                                <div class="col-md-11" style="text-align:right;">
                                                    <input type="submit" value="Proceed" class="confirm_button"/>
                                                </div>
                                                <input type="hidden"
                                                       t-att-name="'type_%s' % str(td_product1['room_type_id'])"
                                                       t-att-value="td_product1['room_type_id']"/>
                                                <input type="hidden" name="len" t-att-value="length"/>
                                                <t t-if="td_product1['chkin']">
                                                    <t t-if="td_product1['chkout']">
                                                        <input type="hidden" name="chkin_id"
                                                               t-att-value="td_product1['chkin']"/>
                                                        <input type="hidden" name="chkout_id"
                                                               t-att-value="td_product1['chkout']"/>
                                                    </t>
                                                </t>
                                            </div>
                                        </div>
                                    </t>
                                    <div class="clearfix"></div>
                                </t>
                            </form>
                        </t>
                        <t t-else="">
                            <t t-if="not home">
                                No Room found for this date range ...!
                            </t>
                        </t>
                    </div>
                </div>
                <div class="oe_structure"/>
            </div>
        </t>
    </template>
</odoo>