# Translation of Odoo Server.
# This file contains the translation of the following modules:
#	* hotel_management
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-05-21 05:14+0000\n"
"PO-Revision-Date: 2020-05-21 05:14+0000\n"
"Last-Translator: <>\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_agent_commission_invoice_line
msgid " Commision Invoice Line"
msgstr "Комиссия по накладной"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_reservation_form1
msgid "(Compute)"
msgstr "Вычисление"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_reservation_form1
msgid "(Update History)"
msgstr "История обновлени"

#. module: hotel_management
#: selection:checkout.configuration,time:0
msgid "1 AM"
msgstr ""

#. module: hotel_management
#: selection:checkout.configuration,time:0
msgid "1 PM"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
msgid "1.00"
msgstr ""

#. module: hotel_management
#: selection:checkout.configuration,time:0
msgid "10 AM"
msgstr ""

#. module: hotel_management
#: selection:checkout.configuration,time:0
msgid "10 PM"
msgstr ""

#. module: hotel_management
#: selection:checkout.configuration,time:0
msgid "11 AM"
msgstr ""

#. module: hotel_management
#: selection:checkout.configuration,time:0
msgid "11 PM"
msgstr ""

#. module: hotel_management
#: selection:checkout.configuration,time:0
msgid "12 Mid Night"
msgstr ""

#. module: hotel_management
#: selection:checkout.configuration,time:0
msgid "12 Noon"
msgstr ""

#. module: hotel_management
#: selection:checkout.configuration,time:0
msgid "2 AM"
msgstr ""

#. module: hotel_management
#: selection:checkout.configuration,time:0
msgid "2 PM"
msgstr ""

#. module: hotel_management
#: selection:checkout.configuration,name:0
msgid "24 Hours"
msgstr ""

#. module: hotel_management
#: selection:checkout.configuration,time:0
msgid "3 AM"
msgstr ""

#. module: hotel_management
#: selection:checkout.configuration,time:0
msgid "3 PM"
msgstr ""

#. module: hotel_management
#: selection:checkout.configuration,time:0
msgid "4 AM"
msgstr ""

#. module: hotel_management
#: selection:checkout.configuration,time:0
msgid "4 PM"
msgstr ""

#. module: hotel_management
#: selection:checkout.configuration,time:0
msgid "5 AM"
msgstr ""

#. module: hotel_management
#: selection:checkout.configuration,time:0
msgid "5 PM"
msgstr ""

#. module: hotel_management
#: selection:checkout.configuration,time:0
msgid "6 AM"
msgstr ""

#. module: hotel_management
#: selection:checkout.configuration,time:0
msgid "6 PM"
msgstr ""

#. module: hotel_management
#: selection:checkout.configuration,time:0
msgid "7 AM"
msgstr ""

#. module: hotel_management
#: selection:checkout.configuration,time:0
msgid "7 PM"
msgstr ""

#. module: hotel_management
#: selection:checkout.configuration,time:0
msgid "8 AM"
msgstr ""

#. module: hotel_management
#: selection:checkout.configuration,time:0
msgid "8 PM"
msgstr ""

#. module: hotel_management
#: selection:checkout.configuration,time:0
msgid "9  PM"
msgstr ""

#. module: hotel_management
#: selection:checkout.configuration,time:0
msgid "9 AM"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_order_kot_report111
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_restaurant_order_kot_report111
msgid "<strong style=\"font-family :Times New Roman ;line-height: 200%; font-size: 30px;\">Kitchen Order Ticket</strong>"
msgstr "<strong style=\"font-family :Times New Roman ;line-height: 200%; font-size: 30px;\">Заказ на кухню</strong>"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
msgid "<strong> Date : </strong>"
msgstr "<strong> Дата : </strong>"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
msgid "<strong> Invoice Address : </strong>"
msgstr "<strong> Адрес Счета : </strong>"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_checkin_report
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_checkout_report
msgid "<strong>#No</strong>"
msgstr "<strong>#№</strong>"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
msgid "<strong>Authorized Signatory</strong>"
msgstr "<strong>Подпись Уполномоченного Лица</strong>"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
msgid "<strong>Cashier : </strong>"
msgstr "<strong>Кассир : </strong>"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_report
msgid "<strong>Check In</strong>"
msgstr "<strong>Заезд</strong>"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_report
msgid "<strong>Check Out</strong>"
msgstr "<strong>Выезд</strong>"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_checkin_report
msgid "<strong>Check-In-Date</strong>"
msgstr "<strong>Дата заезда</strong>"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_checkout_report
msgid "<strong>Check-Out-Date</strong>"
msgstr "<strong>Дата выезда</strong>"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_order_bill11
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_order_kot_report111
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_restaurant_order_bill11
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_restaurant_order_kot_report111
msgid "<strong>Date</strong>"
msgstr "<strong>Дата</strong>"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
msgid "<strong>Days</strong>"
msgstr "<strong>Дни</strong"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_order_kot_report111
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_restaurant_order_kot_report111
msgid "<strong>Food Item List</strong>"
msgstr "Список продуктов питани"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_order_bill11
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_order_kot_report111
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_restaurant_order_bill11
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_restaurant_order_kot_report111
msgid "<strong>Food Item</strong>"
msgstr "Пищевой продукт"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_checkin_report
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_checkout_report
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_report
msgid "<strong>Guest Name</strong>"
msgstr "Имя гостя"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
msgid "<strong>Guest's Signature</strong>"
msgstr "<strong>Подпись Гостя</strong>"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_room_report
msgid "<strong>No. of Times used</strong>"
msgstr "<strong>Использованное время</strong>"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_order_bill11
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_restaurant_order_bill11
msgid "<strong>Order List</strong>"
msgstr "<strong>Список заказов</strong>"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_order_bill11
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_order_kot_report111
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_restaurant_order_bill11
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_restaurant_order_kot_report111
msgid "<strong>Order Number</strong>"
msgstr "<strong>Номер заказа</strong>"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
msgid "<strong>PLEASE RETURN YOUR KEY ON DEPARTURE.</strong>"
msgstr "<strong>ПОЖАЛУЙСТА, ВЕРНИТЕ ВАШ КЛЮЧ ПРИ ОТЪЕЗДЕ.</strong>"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
msgid "<strong>Particulers</strong>"
msgstr "<strong>Подробный отчет</strong>"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
msgid "<strong>Pax</strong>"
msgstr "<strong>Мир</strong>"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
msgid "<strong>Qty</strong>"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_order_bill11
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_order_kot_report111
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_restaurant_order_bill11
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_restaurant_order_kot_report111
msgid "<strong>Quantity</strong>"
msgstr "<strong>Количество</strong>"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_order_bill11
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_restaurant_order_bill11
msgid "<strong>Rate</strong>"
msgstr "<strong>Тариф</strong>"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_report
msgid "<strong>Reservation No.</strong>"
msgstr "<strong>№ Бронирования</strong>"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_order_bill11
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_restaurant_order_bill11
msgid "<strong>Room Accommodation Invoice</strong>"
msgstr "<strong>Счет За Проживание</strong>"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_room_report
msgid "<strong>Room No.</strong>"
msgstr "<strong>№ Комнаты</strong>"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_checkin_report
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_checkout_report
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_report
msgid "<strong>Room No</strong>"
msgstr "<strong>№ Комнаты</strong>"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_order_bill11
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_order_kot_report111
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_restaurant_order_bill11
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_restaurant_order_kot_report111
msgid "<strong>Room Number</strong>"
msgstr "<strong>№ Комнаты</strong>"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_checkin_report
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_checkout_report
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_report
msgid "<strong>Room Type</strong>"
msgstr "<strong>Тип комнаты</strong>"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
msgid "<strong>SN</strong>"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_order_kot_report111
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_restaurant_order_kot_report111
msgid "<strong>Served By</strong>"
msgstr "<strong>Обслуживается</strong>"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_order_bill11
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_restaurant_order_bill11
msgid "<strong>SubTotal</strong>"
msgstr "<strong>Промежуточный итог</strong>"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_order_bill11
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_restaurant_order_bill11
msgid "<strong>Table Information</strong>"
msgstr "<strong>Информация о столе</strong>"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_order_kot_report111
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_restaurant_order_kot_report111
msgid "<strong>Table Number</strong>"
msgstr "<strong>Номер стола</strong>"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
msgid "<strong>Tax :</strong>"
msgstr "<strong>Налог :</strong>"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
msgid "<strong>Total Charges :</strong>"
msgstr "<strong>Суммарный заряд :</strong>"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_order_bill11
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_restaurant_order_bill11
msgid "<strong>Waiter Name</strong>"
msgstr "<strong>Имя официанта</strong>"

#. module: hotel_management
#: selection:hotel.management.config.settings,sale_pricelist_setting:0
msgid "A single sales price per product"
msgstr "Единая цена продажи с продукта"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_website_version
msgid "A/B Testing"
msgstr "А/Б Тестирование"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__unsplash_access_key
msgid "Access Key"
msgstr "Ключ доступа"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__access_warning
msgid "Access warning"
msgstr "Предупреждение доступа"

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_folio_laundry_line__qty_delivered_method
#: model:ir.model.fields,help:hotel_management.field_hotel_folio_transport_line__qty_delivered_method
#: model:ir.model.fields,help:hotel_management.field_hotel_food_line__qty_delivered_method
msgid "According to product configuration, the delivered quantity can be automatically computed by mechanism :\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Analytic From expenses: the quantity is the quantity sum from posted expenses\n"
"  - Timesheet: the quantity is the sum of hours recorded on tasks linked to this sale line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
""
msgstr "Согласно настройки товара, доставлена количество может автоматически вычисляться с помощью механизма: - Вручную количество на строке устанавливается вручную - Аналитика по расходам: количество - сумма от затраченных расходов - Табель: количество - это сумма часов, записанных на задании, связанные с этой строкой продажи - Складские перемещения: количество поступает от подтвержденных комплектувань"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_account_taxcloud
msgid "Account TaxCloud"
msgstr "Учет TaxCloud"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_account_accountant
msgid "Accounting"
msgstr "Учет"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__message_needaction
msgid "Action Needed"
msgstr "Требует внимания"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__activity_ids
msgid "Activities"
msgstr "Деятельность"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping__activity
msgid "Activity"
msgstr "Мероприятие"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__activity_state
msgid "Activity State"
msgstr "Этап действия"

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_hotel_restaurant_kitchen_order_tickets
msgid "Add BOM to restaurant module"
msgstr "Добавить спецификацию в модуль ресторан"

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__group_stock_adv_location
msgid "Add and customize route operations to process product moves in your warehouse(s): e.g. unload > quality control > stock for incoming products, pick > pack > ship for outgoing products. \n"
" You can also set putaway strategies on warehouse locations in order to send incoming products into specific child locations straight away (e.g. specific bins, racks)."
msgstr "Добавьте и настройте операции маршрута, чтобы обрабатывать перемещение товара в ваших складских помещениях: например, выгрузка&gt; контроль качества&gt; состав для входящих товаров, комплектования&gt; пакет&gt; отправка исходящих товаров. Вы также можете установить переходные стратегии на места расположения складских помещений, чтобы сразу отправлять входящие товары в конкретные дочерние места (например, специальные контейнеры, стеллажи)."

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_hotel_restaurant_order_list
msgid "Add restaurant inventory"
msgstr "Добавить инвентарь ресторана"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.roomwise_guestwise_report_view
msgid "Address"
msgstr "Адрес"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__adults
msgid "Adults"
msgstr "Взрослые"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__adv_amount
msgid "Advance Amount"
msgstr "Сумма Аванса"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__deposit_cost2
#: model_terms:ir.ui.view,arch_db:hotel_management.advance_payment_wizard1
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_folio_form_inherit_state
msgid "Advance Payment"
msgstr "Предоплата"

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_advance_payment_wizard
msgid "Advance Payment Detail Wizard"
msgstr "Мастер Детализации Авансового Платежа"

#. module: hotel_management
#: model:ir.actions.act_window,name:hotel_management.act_advance_payment_entry1
msgid "Advance Payment Entry"
msgstr "Предварительная Запись Оплата"

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__module_stock_landed_costs
msgid "Affect landed costs on reception operations and split them among products to update their cost price."
msgstr "Влияние на дополнительные расходы на операции приема и распределение их между товарами с целью обновления себестоимости."

#. module: hotel_management
#: selection:hotel.reservation,via:0
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice__partner_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__agent_id
#: model:ir.model.fields,field_description:hotel_management.field_res_partner__agent
#: model:ir.model.fields,field_description:hotel_management.field_res_users__agent
#: model_terms:ir.ui.view,arch_db:hotel_management.view_partner_property_form_commission
msgid "Agent"
msgstr "Агент"

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_agent_commission_invoice
msgid "Agent Commision Invoice"
msgstr "Комиссионный счет агента"

#. module: hotel_management
#: model:ir.ui.menu,name:hotel_management.menu_agent_commission
msgid "Agent Commission"
msgstr "агентские комиссионные"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice__name
msgid "Agent Commission ID"
msgstr "Идентификатор комиссии агента"

#. module: hotel_management
#: model:ir.actions.act_window,name:hotel_management.action_agent_commission_invoice_form_view
#: model:ir.ui.menu,name:hotel_management.menu_agent_commission_invoice
#: model_terms:ir.ui.view,arch_db:hotel_management.agent_commission_invoic_form_view1
#: model_terms:ir.ui.view,arch_db:hotel_management.agent_commission_invoice_tree_view
msgid "Agent Commission Invoice"
msgstr "Комиссионный Счет Агента"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.agent_commission_invoice_line_tree_view
msgid "Agent Commission Invoice Line"
msgstr "Комиссионный Счет Агента"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice__agent_invoice_ids
msgid "Agent Invoices"
msgstr "Счета Агентов"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__alias_domain
msgid "Alias Domain"
msgstr "Домен псевдонима"

#. module: hotel_management
#: model:ir.actions.act_window,name:hotel_management.action_hotel_reservation_tree_cancel
#: model:ir.ui.menu,name:hotel_management.menu_action_hotel_reservation_tree_cancel
msgid "All Cancelled Reservation"
msgstr "Отмененные Бронирования"

#. module: hotel_management
#: model:ir.actions.act_window,name:hotel_management.action_hotel_reservation_tree_confirm
#: model:ir.ui.menu,name:hotel_management.menu_action_hotel_reservation_tree_confirm
msgid "All Confirm Reservation"
msgstr "Подтвержденные Бронирование"

#. module: hotel_management
#: model:ir.actions.act_window,name:hotel_management.action_hotel_reservation_tree_done
#: model:ir.ui.menu,name:hotel_management.menu_action_hotel_reservation_tree_done
msgid "All Done Reservation"
msgstr "Выолненные Бронирование"

#. module: hotel_management
#: model:ir.actions.act_window,name:hotel_management.action_hotel_reservation_tree_draft11
#: model:ir.ui.menu,name:hotel_management.menu_action_hotel_reservation_tree_draft
msgid "All Draft Reservation"
msgstr "Неподтвержденные Бронирование"

#. module: hotel_management
#: model:ir.actions.act_window,name:hotel_management.action_hotel_reservation_tree_all
msgid "All Reservation"
msgstr "Бронирование"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_product_margin
msgid "Allow Product Margin"
msgstr "Позволить маржу товара"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_account_check_printing
msgid "Allow check printing and deposits"
msgstr "Разрешить печать чеков и депозиты"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_google_calendar
msgid "Allow the users to synchronize their calendar  with Google Calendar"
msgstr "Разрешить пользователям синхронизировать свой календарь с календарём Google"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_base_import
msgid "Allow users to import data from CSV/XLS/XLSX/ODS files"
msgstr "Позволяет пользователям импортировать данные из файлов CSV, XLS, XLSX, ODS"

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__group_sale_pricelist
msgid "Allows to manage different prices based on rules per category of customers.\n"
"                Example: 10% for retailers, promotion of 5 EUR on this product, etc."
msgstr "Позволяет устанавливать отдельные цены согласно правилам для отдельных категорий клиентов.\n"
"                    Например: 10% для розничных продавцов, скидка 5 евро при первой покупке, и т.д."

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__group_multi_currency
msgid "Allows to work in a multi currency environment"
msgstr "Разрешена работа в мультивалютной среде."

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__group_proforma_sales
msgid "Allows you to send pro-forma invoice."
msgstr "Позволяет вам отправлять предварительный счет"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_advance_payment_wizard__amt
msgid "Amount"
msgstr "Сумма"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_analytic_accounting
msgid "Analytic Accounting"
msgstr "Аналитический учёт"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__analytic_tag_ids
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__analytic_tag_ids
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__analytic_tag_ids
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_analytic_tags
msgid "Analytic Tags"
msgstr "Теги аналитики"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__analytic_line_ids
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__analytic_line_ids
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__analytic_line_ids
msgid "Analytic lines"
msgstr "Строки аналитики"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_management_config_settings
msgid "Apply"
msgstr "Применить"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping__approved_by
msgid "Approved By"
msgstr "Одобрено"

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__specific_user_account
msgid "Are newly created user accounts website specific"
msgstr "Являются ли вновь созданные учетные записи пользователя зависящими от сайта"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.arrival_dept_guest
msgid "Arrival Departure Report"
msgstr "Отчет по заездам"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.arrival_dept_guest
msgid "Arrival Time"
msgstr "Время прибытия"

#. module: hotel_management
#: model:ir.actions.report,name:hotel_management.report_arrival_dept_guest
msgid "Arrival/Depart Guest List"
msgstr "Прибытие / Отъезд Гостей"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_account_asset
msgid "Assets Management"
msgstr "Управление активами"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.view_rr_housekeeping_form
msgid "Assign"
msgstr "Назначать"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping__assign_to
msgid "Assign Method"
msgstr "Назначить Метод"

#. module: hotel_management
#: selection:rr.housekeeping,state:0
msgid "Assigned"
msgstr "Назначенный"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping__assigned_internal
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping__assigned_third_party
msgid "Assigned To"
msgstr "Ответственный"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_voip
msgid "Asterisk (VoIP)"
msgstr "Астериск (VoIP)"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_google_drive
msgid "Attach Google documents to any record"
msgstr "Прилагать документы Google к любой записи"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__message_attachment_count
msgid "Attachment Count"
msgstr "Количество вложений"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_partner_autocomplete
msgid "Auto-populate company data"
msgstr "Автоматически заполнять данные компании"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_account_invoice_extract
msgid "Automate Bill Processing"
msgstr "Автоматизировать обработку счета"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_currency_rate_live
msgid "Automatic Currency Rates"
msgstr "Автоматическое выставление курса валют"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_folio_form_inherit_state
msgid "Automatic Declaration"
msgstr "Автоматическая Декларация"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__automatic_invoice
msgid "Automatic Invoice"
msgstr "Автоматическое выставление счета"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_tables__avl_state
msgid "Availability Status"
msgstr "Состояние доступности"

#. module: hotel_management
#: selection:hotel.restaurant.tables,avl_state:0
msgid "Available"
msgstr "Доступный"

#. module: hotel_management
#: selection:hotel.menucard,product_nature:0
#: selection:hotel.restaurant.kitchen.order.tickets,product_nature:0
#: model:ir.ui.menu,name:hotel_management.menu_view_hotel_restaurant_kitchen_order_tickets_bot_inherit
#: selection:product.template,product_nature:0
msgid "BOT"
msgstr "BOT"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_restaurant_kitchen_order_tickets_tree_bot
msgid "BOT List"
msgstr "BOT List"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_kitchen_order_tickets__ordernobot
msgid "BOT Number"
msgstr "№ BOT"

#. module: hotel_management
#: model:ir.actions.act_window,name:hotel_management.open_view_hotel_restaurant_kitchen_order_tickets_form_tree_bot
msgid "BOT Order List"
msgstr "BOT Order List"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_account_yodlee
msgid "Bank Interface - Sync your bank feeds automatically"
msgstr "Банковский Интерфейс — Автосинхронизация с каналом вашего банка"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__account_bank_reconciliation_start
msgid "Bank Reconciliation Threshold"
msgstr "Порог согласования банковских выписок"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_stock_barcode
msgid "Barcode Scanner"
msgstr "Сканер штрих-кода"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
msgid "Base Currency Amt("
msgstr "Базовая Валюта Amt("

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_reservation_form1
msgid "Basic Info"
msgstr "Основная информация"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_stock_picking_batch
msgid "Batch Pickings"
msgstr "Комплектование партии"

#. module: hotel_management
#: selection:res.partner,reservation_warn:0
msgid "Blocking Message"
msgstr "Блокирующее Сообщение"

#. module: hotel_management
#: selection:hotel.restaurant.tables,avl_state:0
msgid "Booked"
msgstr "Забронированы"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.monthly_occupency_report_view
msgid "Booked Room"
msgstr "Забронированный Номер"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_reservation_form1
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_room_booking_history_gantt111
msgid "Booking Details"
msgstr "Детали Бронирования"

#. module: hotel_management
#: model:ir.actions.act_window,name:hotel_management.action_hotel_room_booking_history_tree
#: model:ir.ui.menu,name:hotel_management.menu_hotel_room_booking_history_tree
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_room_form_inherit_rentalss
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_room_booking_history_form1
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_room_booking_history_tree1
msgid "Booking History"
msgstr "История Бронирования"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_deposit_journal_entry_wizard1__booking_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_room_booking_history__booking_id
msgid "Booking Ref"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice_line__book_id
msgid "Booking Ref."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_account_budget
msgid "Budget Management"
msgstr "Управление бюджетом"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_mrp_byproduct
msgid "By-Products"
msgstr "По продуктам"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__cdn_url
msgid "CDN Base URL"
msgstr "Базовый URL CDN"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__cdn_filters
msgid "CDN Filters"
msgstr "CDN Фильтры"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__product_updatable
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__product_updatable
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__product_updatable
msgid "Can Edit Product"
msgstr "Может редактировать товар"

#. module: hotel_management
#: selection:hotel.restaurant.tables,state:0
#: selection:hotel.room.booking.history,state:0
#: model_terms:ir.ui.view,arch_db:hotel_management.advance_payment_wizard1
#: model_terms:ir.ui.view,arch_db:hotel_management.arrival_dept_guest_wizard_view
#: model_terms:ir.ui.view,arch_db:hotel_management.deposit_journal_entry_wizard1
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_wizard_view
#: model_terms:ir.ui.view,arch_db:hotel_management.monthly_occupancy_wizard_id
#: model_terms:ir.ui.view,arch_db:hotel_management.room_guest_wizard_view_new
#: model_terms:ir.ui.view,arch_db:hotel_management.view_folio_invoice_transfer_wizard
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_management_config_settings
#: model_terms:ir.ui.view,arch_db:hotel_management.view_rr_housekeeping_form
#: selection:rr.housekeeping,state:0
msgid "Cancel"
msgstr "Отменить"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_reservation_form1
msgid "Cancel Reservation"
msgstr "Аннулировать бронирование"

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_cancel_foilo_wizard
msgid "Cancel Wizard"
msgstr ""

#. module: hotel_management
#: selection:agent.commission.invoice,state:0
msgid "Canceled"
msgstr "Отмененный"

#. module: hotel_management
#: selection:hotel.reservation,state:0
#: selection:hotel.restaurant.order,state:0
#: selection:sale.order,state:0
msgid "Cancelled"
msgstr "Отменено"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_tables__capacity
msgid "Capacity"
msgstr "Вместимость"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__tax_exigibility
msgid "Cash Basis"
msgstr "Кассовая основа"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_cash_rounding
msgid "Cash Rounding"
msgstr "Округление наличных денег"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.roomwise_guestwise_report_view
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_room_booking_history_search
msgid "Check In"
msgstr "Заезд"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.roomwise_guestwise_report_view
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_room_booking_history_search
msgid "Check Out"
msgstr "Выезд"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_room_booking_history__check_in
#: model:ir.model.fields,field_description:hotel_management.field_hotel_room_booking_history__check_in_date
msgid "CheckIn Date"
msgstr "Дата Заезда"

#. module: hotel_management
#: model:ir.actions.report,name:hotel_management.hotel_checkin_details_report
msgid "CheckIn Detail"
msgstr "Детали заезда"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_checkin_report
msgid "CheckIn Guest List"
msgstr "Список заезжающих гостей"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_wizard_view
msgid "CheckIn List"
msgstr "Список Заездов"

#. module: hotel_management
#: selection:sale.order,state:0
msgid "CheckOut"
msgstr "Выезд"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_room_booking_history__check_out
#: model:ir.model.fields,field_description:hotel_management.field_hotel_room_booking_history__check_out_date
msgid "CheckOut Date"
msgstr "Дата Выезда"

#. module: hotel_management
#: model:ir.actions.report,name:hotel_management.hotel_checkout_details_report
msgid "CheckOut Detail"
msgstr "Детали Выезда"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_wizard_view
msgid "CheckOut List"
msgstr "Список Выездов"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_line__checkin
msgid "Checkin Date"
msgstr "Дата Заезда"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_folio_form_inherit_state
msgid "Checkout"
msgstr "Выезд"

#. module: hotel_management
#: model:ir.actions.act_window,name:hotel_management.checkout_configuration_action
#: model:ir.model,name:hotel_management.model_checkout_configuration
#: model_terms:ir.ui.view,arch_db:hotel_management.checkout_configuration_form
#: model_terms:ir.ui.view,arch_db:hotel_management.checkout_configuration_tree
msgid "Checkout Configuration"
msgstr "Настройка Выезда"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_line__checkout
msgid "Checkout Date"
msgstr "Дата Выезда"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_checkout_report
msgid "Checkout Guest List"
msgstr "Список Выезжающих Гостей"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.checkout_configuration_form
msgid "Checkout Info"
msgstr "Информация о выезде"

#. module: hotel_management
#: model:ir.ui.menu,name:hotel_management.checkout_configuration
msgid "Checkout Policy"
msgstr "Правила Выезда"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_checkout_configuration__name
msgid "Checkout Time"
msgstr "Время Выезда"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__childs
msgid "Children"
msgstr "Дети"

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_id_master
msgid "Clients ID details"
msgstr "Идентификационные данные клиентов"

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_hotel_resv_id_details
msgid "Clients ID details during reservation"
msgstr "Идентификационные данные клиентов при бронировании"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_pad
msgid "Collaborative Pads"
msgstr "Совместные планшеты"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__agent_comm
msgid "Commision"
msgstr "Комиссия"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice__commission_percentage
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice_line__commission_percentage
msgid "Commission %"
msgstr "% Комиссии"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice_line__commission_amt
#: model_terms:ir.ui.view,arch_db:hotel_management.agent_commission_invoic_form_view1
msgid "Commission Amount"
msgstr "Сумма Комиссии"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice_line__commission_line_id
msgid "Commission ID"
msgstr "Идентификатор комиссии"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.agent_commission_invoic_form_view1
msgid "Commission Line"
msgstr "Линии Комиссии"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_res_partner__commission
#: model:ir.model.fields,field_description:hotel_management.field_res_users__commission
msgid "Commission Percentage"
msgstr "комиссионный процент"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__invoice_reference_type
msgid "Communication"
msgstr "Общение"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_checkout_configuration__company_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__company_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__company_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__company_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__company_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_menucard__company_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__company_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_order__company_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_kitchen_order_tickets__company_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_order__company_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_reservation__company_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_tables__company_id
#: model:ir.model.fields,field_description:hotel_management.field_issue_material_details__company_id
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping__company_id
msgid "Company"
msgstr "Компания"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_database_configuration__company_name
msgid "Company Name"
msgstr "Название компании"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__has_chart_of_accounts
msgid "Company has a chart of accounts"
msgstr "Компания имеет план счетов"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_issue_material_details__complaint
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping__complaint
msgid "Complaint"
msgstr "Жалоба"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_management_config_settings
msgid "Configure Hotel Management"
msgstr "Настройка Управления Гостиницей"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__has_social_network
msgid "Configure Social Network"
msgstr "Настройка социальной сети"

#. module: hotel_management
#: selection:hotel.reservation,state:0
#: selection:hotel.room.booking.history,state:0
#: model_terms:ir.ui.view,arch_db:hotel_management.agent_commission_invoic_form_view1
#: model_terms:ir.ui.view,arch_db:hotel_management.cancel_foilo_wizard_form_view
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_reservation_form1
#: model_terms:ir.ui.view,arch_db:hotel_management.view_issue_material_details_form
#: model_terms:ir.ui.view,arch_db:hotel_management.view_rr_housekeeping_form
#: selection:issue.material.details,state:0
msgid "Confirm"
msgstr "Подтвердить"

#. module: hotel_management
#: selection:agent.commission.invoice,state:0
#: selection:hotel.restaurant.order,state:0
#: selection:hotel.restaurant.tables,state:0
#: selection:rr.housekeeping,state:0
msgid "Confirmed"
msgstr "Подтвержденный"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_stock_tracking_owner
msgid "Consignment"
msgstr "комиссионная торговля"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__cdn_activated
msgid "Content Delivery Network (CDN)"
msgstr "Сеть доставки контента"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_resv_id_details__country_id
msgid "Country"
msgstr "Страна"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__website_country_group_ids
msgid "Country Groups"
msgstr "Группы стран"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_sale_coupon
msgid "Coupons & Promotions"
msgstr "Купоны & акции"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_reservation_form1
msgid "Create Folio"
msgstr "Создать Счет"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.agent_commission_invoic_form_view1
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_folio_form_inherit_state
#: model_terms:ir.ui.view,arch_db:hotel_management.view_folio_invoice_transfer_wizard
msgid "Create Invoice"
msgstr "Создать Счет"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.deposit_journal_entry_wizard1
msgid "Create Journal Entry"
msgstr "Создание Записи В Журнале"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_crm_reveal
msgid "Create Leads/Opportunities from your website's traffic"
msgstr "Создайте лиды / случаю из посещений вашего сайта"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_advance_payment_wizard__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice_line__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_arrival_dept_guest_wizard__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_cancel_foilo_wizard__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_checkout_configuration__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_dashboard_url__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_database_configuration__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_deposit_journal_entry_wizard1__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_folio_invoice_transfer_wizard__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_line__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_wizard__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_hotel_resv_id_details__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_hotel_room_booking_history__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_id_master__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_issue_material__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_issue_material_details__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_monthly_occupancy_wizard__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_product_product_line__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_room_guestwise_wizard__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_line__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_line_wizard__create_uid
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_wizard__create_uid
msgid "Created by"
msgstr "Создано"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_advance_payment_wizard__create_date
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice__create_date
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice_line__create_date
#: model:ir.model.fields,field_description:hotel_management.field_arrival_dept_guest_wizard__create_date
#: model:ir.model.fields,field_description:hotel_management.field_cancel_foilo_wizard__create_date
#: model:ir.model.fields,field_description:hotel_management.field_checkout_configuration__create_date
#: model:ir.model.fields,field_description:hotel_management.field_dashboard_url__create_date
#: model:ir.model.fields,field_description:hotel_management.field_database_configuration__create_date
#: model:ir.model.fields,field_description:hotel_management.field_deposit_journal_entry_wizard1__create_date
#: model:ir.model.fields,field_description:hotel_management.field_folio_invoice_transfer_wizard__create_date
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__create_date
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__create_date
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__create_date
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__create_date
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__create_date
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_line__create_date
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_wizard__create_date
#: model:ir.model.fields,field_description:hotel_management.field_hotel_resv_id_details__create_date
#: model:ir.model.fields,field_description:hotel_management.field_hotel_room_booking_history__create_date
#: model:ir.model.fields,field_description:hotel_management.field_id_master__create_date
#: model:ir.model.fields,field_description:hotel_management.field_issue_material__create_date
#: model:ir.model.fields,field_description:hotel_management.field_issue_material_details__create_date
#: model:ir.model.fields,field_description:hotel_management.field_monthly_occupancy_wizard__create_date
#: model:ir.model.fields,field_description:hotel_management.field_product_product_line__create_date
#: model:ir.model.fields,field_description:hotel_management.field_room_guestwise_wizard__create_date
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping__create_date
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_line__create_date
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_line_wizard__create_date
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_wizard__create_date
msgid "Created on"
msgstr "Создан"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_account_invoice__create_date
msgid "Creation Date"
msgstr "Дата создания"

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_res_currency
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__currency_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__currency_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__currency_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__currency_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__currency_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_line__currency_id
msgid "Currency"
msgstr "Валюта"

#. module: hotel_management
#: selection:checkout.configuration,name:0
msgid "Custom"
msgstr "Пользовательский"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__report_footer
msgid "Custom Report Footer"
msgstr "Пользовательский колонтитул отчета"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_checkout_configuration__time
msgid "Custom Time"
msgstr "Обычное Время"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_deposit_journal_entry_wizard1__partner_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__order_partner_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__order_partner_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__order_partner_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_order__partner_id
msgid "Customer"
msgstr "Заказчик"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__auth_signup_uninvited
msgid "Customer Account"
msgstr "Учетная запись клиента"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_sale_delivery_address
msgid "Customer Addresses"
msgstr "Адреса заказчиков"

#. module: hotel_management
#: selection:arrival.dept.guest.wizard,arrival_dept:0
msgid "Customer Arrival"
msgstr "Прибытие Клиента"

#. module: hotel_management
#: model:ir.actions.act_window,name:hotel_management.action_arrival_dept_guest_wizard
#: model:ir.ui.menu,name:hotel_management.menu_action_arrival_dept_guest_wizard_id
msgid "Customer Arrival/ Departure List"
msgstr "Список Прибытия/ Отъезда Клиентов"

#. module: hotel_management
#: model:ir.actions.report,name:hotel_management.hotel_reservation_order_bill
#: model:ir.actions.report,name:hotel_management.hotel_restaurant_order_bill
msgid "Customer Bill"
msgstr "Счет Клиента"

#. module: hotel_management
#: selection:arrival.dept.guest.wizard,arrival_dept:0
msgid "Customer Departure"
msgstr "Отъезд Клиента"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice_line__partner_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_reservation__cname
msgid "Customer Name"
msgstr "Название заказчика"

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_reservation__access_url
msgid "Customer Portal URL"
msgstr "Ссылка на портал клиента"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_delivery_dhl
msgid "DHL Connector"
msgstr "Коннектор DHL"

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_arrival_dept_guest_wizard
msgid "Daily Customer Arrival/ Departure List"
msgstr "Ежедневный Список Прибытия/ Отъезда Клиентов"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_dashboard_url__url
#: model_terms:ir.ui.view,arch_db:hotel_management.dashboard_url_form
#: model_terms:ir.ui.view,arch_db:hotel_management.dashboard_url_tree
msgid "Dashboard URL"
msgstr ""

#. module: hotel_management
#: model:ir.actions.act_window,name:hotel_management.dashboard_url_configuration_action
msgid "Dashboard URL Configuration"
msgstr ""

#. module: hotel_management
#: model:ir.actions.act_window,name:hotel_management.database_configuration_action
#: model:ir.model,name:hotel_management.model_database_configuration
#: model:ir.ui.menu,name:hotel_management.database_configuration_submenu
#: model_terms:ir.ui.view,arch_db:hotel_management.database_configuration_form
#: model_terms:ir.ui.view,arch_db:hotel_management.database_configuration_tree
msgid "Database Configuration"
msgstr "Настройка базы данных"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_database_configuration__name
msgid "Database Name"
msgstr "Название базы данных"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice__current_date
#: model_terms:ir.ui.view,arch_db:hotel_management.monthly_occupency_report_view
msgid "Date"
msgstr "Дата"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__date_order
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping__date
msgid "Date Ordered"
msgstr "Дата Заказа"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_resv_id_details__date_birth
msgid "Date of Birth"
msgstr "Дата рождения"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__user_default_rights
msgid "Default Access Rights"
msgstr "Права доступа по умолчанию"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__crm_alias_prefix
msgid "Default Alias Name for Leads"
msgstr "Псевдоним по умолчанию для лидов"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__use_manufacturing_lead
msgid "Default Manufacturing Lead Time"
msgstr "Обычная длительность производственного процесса"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__purchase_tax_id
msgid "Default Purchase Tax"
msgstr "Налог на покупку по умолчанию"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__use_quotation_validity_days
msgid "Default Quotation Validity"
msgstr "Срок действия коммерческого предложения по умолчанию"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__quotation_validity_days
msgid "Default Quotation Validity (Days)"
msgstr "Срок действия коммерческого предложения по умолчанию (в днях)"

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__invoice_reference_type
msgid "Default Reference Type on Invoices."
msgstr "Тип ссылки по умолчанию на счетах."

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__sale_tax_id
msgid "Default Sale Tax"
msgstr "Налог с продаж по умолчанию"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__social_default_image
msgid "Default Social Share Image"
msgstr "Изображение `Поделиться в соцсетях` по умолчанию"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__use_sale_note
msgid "Default Terms & Conditions"
msgstr "Стандартные условия"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__website_default_lang_id
msgid "Default language"
msgstr "Язык по умолчанию"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__website_default_lang_code
msgid "Default language code"
msgstr "Код языка по умолчанию"

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__deposit_default_product_id
msgid "Default product used for payment advances"
msgstr "Стандартный продукт по предоплате"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__qty_delivered_manual
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__qty_delivered_manual
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__qty_delivered_manual
msgid "Delivered Manually"
msgstr "Доставлено вручную"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__qty_delivered
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__qty_delivered
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__qty_delivered
msgid "Delivered Quantity"
msgstr "Доставленное количество"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_sale_order_dates
msgid "Delivery Date"
msgstr "Дата доставки"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__customer_lead
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__customer_lead
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__customer_lead
msgid "Delivery Lead Time"
msgstr "Время выполнения доставки"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_stock_tracking_lot
msgid "Delivery Packages"
msgstr "Пакеты Доставки"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.arrival_dept_guest
msgid "Departure Time"
msgstr "Время вылета"

#. module: hotel_management
#: sql_constraint:hotel.reservation.line:0
msgid "Departure date must be greater than Arrival Date!"
msgstr "Дата отправления должна быть больше даты прибытия!"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_advance_payment_wizard__deposit_recv_acc
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__deposit_recv_acc
msgid "Deposit Account"
msgstr "депозитный счет"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__deposit_cost1
msgid "Deposit Cost"
msgstr "Стоимость Депозита"

#. module: hotel_management
#: model:ir.actions.act_window,name:hotel_management.act_deposit_journal_entry1
msgid "Deposit Journal Entry"
msgstr "Запись В Журнале Депозитов"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__deposit_default_product_id
msgid "Deposit Product"
msgstr "Депозит продукта"

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_deposit_journal_entry_wizard1
msgid "Deposit_journal_entry Detail Wizard"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_cancel_foilo_wizard__desc
#: model:ir.model.fields,field_description:hotel_management.field_deposit_journal_entry_wizard1__name
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__name
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__name
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__name
msgid "Description"
msgstr "Описание"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_issue_material__location_dest_id
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_line__dest_locatiion
msgid "Destination Location"
msgstr "Место назначения"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__digest_id
msgid "Digest Email"
msgstr "Электронная почта дайджеста"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__digest_emails
msgid "Digest Emails"
msgstr "Электронные почты дайджеста"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_website_sale_digital
msgid "Digital Content"
msgstr "Цифровой контент"

#. module: hotel_management
#: selection:hotel.reservation,via:0
msgid "Direct"
msgstr "Прямое"

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__group_products_in_bills
msgid "Disable this option to use a simplified versions of vendor bills, where products are hidden."
msgstr "Выключите эту опцию для использования упрощенных версий счетов поставщиков, где товары скрыты."

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__discount
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__discount
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__discount
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_line__discount
msgid "Discount (%)"
msgstr "Скидка (%)"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_discount_per_so_line
msgid "Discounts"
msgstr "Скидки"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_lot_on_delivery_slip
msgid "Display Lots & Serial Numbers"
msgstr "Отобразить партийные и серийные номера"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_advance_payment_wizard__display_name
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice__display_name
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice_line__display_name
#: model:ir.model.fields,field_description:hotel_management.field_arrival_dept_guest_wizard__display_name
#: model:ir.model.fields,field_description:hotel_management.field_cancel_foilo_wizard__display_name
#: model:ir.model.fields,field_description:hotel_management.field_checkout_configuration__display_name
#: model:ir.model.fields,field_description:hotel_management.field_dashboard_url__display_name
#: model:ir.model.fields,field_description:hotel_management.field_database_configuration__display_name
#: model:ir.model.fields,field_description:hotel_management.field_deposit_journal_entry_wizard1__display_name
#: model:ir.model.fields,field_description:hotel_management.field_folio_invoice_transfer_wizard__display_name
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__display_name
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__display_name
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__display_name
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__display_name
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__display_name
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_line__display_name
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_wizard__display_name
#: model:ir.model.fields,field_description:hotel_management.field_hotel_resv_id_details__display_name
#: model:ir.model.fields,field_description:hotel_management.field_hotel_room_booking_history__display_name
#: model:ir.model.fields,field_description:hotel_management.field_id_master__display_name
#: model:ir.model.fields,field_description:hotel_management.field_issue_material__display_name
#: model:ir.model.fields,field_description:hotel_management.field_issue_material_details__display_name
#: model:ir.model.fields,field_description:hotel_management.field_monthly_occupancy_wizard__display_name
#: model:ir.model.fields,field_description:hotel_management.field_product_product_line__display_name
#: model:ir.model.fields,field_description:hotel_management.field_report_hotel_management_arrival_dept_guest__display_name
#: model:ir.model.fields,field_description:hotel_management.field_report_hotel_management_hotel_report_view__display_name
#: model:ir.model.fields,field_description:hotel_management.field_report_hotel_management_hotel_reservation_checkin_report__display_name
#: model:ir.model.fields,field_description:hotel_management.field_report_hotel_management_hotel_reservation_checkout_report__display_name
#: model:ir.model.fields,field_description:hotel_management.field_report_hotel_management_hotel_reservation_report__display_name
#: model:ir.model.fields,field_description:hotel_management.field_report_hotel_management_hotel_reservation_room_report__display_name
#: model:ir.model.fields,field_description:hotel_management.field_report_hotel_management_monthly_occupency_report_view__display_name
#: model:ir.model.fields,field_description:hotel_management.field_report_hotel_management_roomwise_guestwise_report_view__display_name
#: model:ir.model.fields,field_description:hotel_management.field_room_guestwise_wizard__display_name
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping__display_name
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_line__display_name
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_line_wizard__display_name
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_wizard__display_name
msgid "Display Name"
msgstr "Отображаемое Имя"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__qr_code
msgid "Display SEPA QR code"
msgstr "Отобразить SEPA QR-код"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__display_type
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__display_type
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__display_type
msgid "Display Type"
msgstr "Тип экрана"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__test
msgid "Do nat make separate Invoices"
msgstr "Не делайте отдельных счетов"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__external_report_layout_id
msgid "Document Template"
msgstr "Шаблон документа"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_resv_id_details__client_id
msgid "Document Type"
msgstr "Тип документа"

#. module: hotel_management
#: selection:agent.commission.invoice,state:0
#: selection:hotel.reservation,state:0
#: selection:hotel.restaurant.order,state:0
#: selection:hotel.room.booking.history,state:0
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_folio_form_inherit_state
#: model_terms:ir.ui.view,arch_db:hotel_management.view_issue_material_details_form
#: model_terms:ir.ui.view,arch_db:hotel_management.view_rr_housekeeping_form
#: selection:issue.material.details,state:0
#: selection:rr.housekeeping,state:0
#: selection:sale.order,state:0
msgid "Done"
msgstr "Сделано"

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_folio_laundry_line__is_downpayment
#: model:ir.model.fields,help:hotel_management.field_hotel_folio_transport_line__is_downpayment
#: model:ir.model.fields,help:hotel_management.field_hotel_food_line__is_downpayment
msgid "Down payments are made when creating invoices from a sales order. They are not copied when duplicating a sales order."
msgstr "Авансовые платежи создаются на основании заказа. Они не копируются вместе с заказом при дублировании."

#. module: hotel_management
#: selection:agent.commission.invoice,state:0
#: selection:hotel.reservation,state:0
#: selection:hotel.restaurant.order,state:0
#: selection:hotel.restaurant.tables,state:0
#: selection:hotel.room.booking.history,state:0
#: selection:issue.material.details,state:0
#: selection:rr.housekeeping,state:0
msgid "Draft"
msgstr "Черновик"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__dummy
msgid "Dummy"
msgstr "Манекен"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_account_reports
msgid "Dynamic Reports"
msgstr "Динамические отчеты"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_l10n_eu_service
msgid "EU Digital Goods VAT"
msgstr "НДС на цифровые товары по правилам ЕС"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_delivery_easypost
msgid "Easypost Connector"
msgstr "Коннектор Easypost"

#. module: hotel_management
#: selection:hotel.restaurant.tables,state:0
msgid "Edit"
msgstr "Редактировать"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__template_id
msgid "Email Template"
msgstr "Шаблон письма"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__auth_signup_reset_password
msgid "Enable password reset from Login page"
msgstr "Включить сброс пароля со страницы входа"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__name
msgid "Event type"
msgstr "Тип события"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__currency_exchange_journal_id
msgid "Exchange Gain or Loss Journal"
msgstr "Журнал Курсовых Прибылей или Убытков"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_account_invoice__exchange_rate
msgid "Exchange Rate"
msgstr "Обменный курс"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice__recv_acc
msgid "Expense Account"
msgstr "Счет расходов"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_product_expiry
msgid "Expiration Dates"
msgstr "Сроки Годности"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__external_email_server_default
msgid "External Email Servers"
msgstr "Внешние Почтовые Сервера"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__social_facebook
msgid "Facebook Account"
msgstr "Учетная запись на Facebook"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__fail_counter
msgid "Fail Mail"
msgstr "Проблемные письма"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__favicon
msgid "Favicon"
msgstr "Значок"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_delivery_fedex
msgid "FedEx Connector"
msgstr "Коннектор FedEx"

#. module: hotel_management
#: selection:hotel.resv.id.details,gender:0
msgid "Female"
msgstr "Женщина"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.monthly_occupancy_wizard_id
#: model_terms:ir.ui.view,arch_db:hotel_management.room_guest_wizard_view_new
msgid "Fill The Dates"
msgstr "Заполнить даты"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.arrival_dept_guest_wizard_view
msgid "Fill The Details"
msgstr "Заполнить детали"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_fiscal_year
msgid "Fiscal Years"
msgstr "Отчетные периоды"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_order__flag1
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_order__flag
msgid "Flag"
msgstr "Флаг"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__folio_id
msgid "Folio"
msgstr "Счет"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_resv_id_details__folio_id
msgid "Folio Id"
msgstr "Номер счета"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_folio_invoice_transfer_wizard__folio_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio__laundry_line_ids
msgid "Folio Ref"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_folio_invoice_transfer_wizard
msgid "Folio invoice transfer Wizard"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__folio_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__folio_id
msgid "Folio ref"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_hotel_folio_calendar_view
msgid "Folios"
msgstr "Счета"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_account_reports_followup
msgid "Follow-up Levels"
msgstr "Уровни напоминаний"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__message_follower_ids
msgid "Followers"
msgstr "Подписчики"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__message_channel_ids
msgid "Followers (Channels)"
msgstr "Подписчики (Каналы)"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__message_partner_ids
msgid "Followers (Partners)"
msgstr "Подписчики (Партнеры)"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_folio_form_inherit_state
msgid "Food Line"
msgstr "Линия Питания"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio__food_lines
msgid "Food Lines"
msgstr "Линии Питания"

#. module: hotel_management
#: model:product.category,name:hotel_management.banq_management_category
msgid "Foods"
msgstr "Продукты"

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__report_footer
msgid "Footer text displayed at the bottom of all reports."
msgstr "Текст подвала отображается в нижней части всех отчётов."

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_project_forecast
msgid "Forecasts"
msgstr "Прогнозы"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_arrival_dept_guest_wizard__date_start
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_wizard__date_start
#: model:ir.model.fields,field_description:hotel_management.field_monthly_occupancy_wizard__start_date
#: model:ir.model.fields,field_description:hotel_management.field_room_guestwise_wizard__date_start
msgid "From Date"
msgstr "С даты"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__gds_id
msgid "GDS ID"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_resv_id_details__gender
msgid "Gender"
msgstr "Пол"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_reservation_order_form_inherit1
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_restaurant_order_form_inherit_pragtech
msgid "Generate KOT/BOT"
msgstr "Создавать KOT/BOT"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__social_github
msgid "GitHub Account"
msgstr "Учетная запись на GitHub"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__has_google_analytics
msgid "Google Analytics"
msgstr "Google Аналитика"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__has_google_analytics_dashboard
msgid "Google Analytics Dashboard"
msgstr "Панель приборов Google Analytics"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__google_analytics_key
msgid "Google Analytics Key"
msgstr "Ключ Google Analytics"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__google_management_client_id
msgid "Google Client ID"
msgstr "ID клиента Google"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__google_management_client_secret
msgid "Google Client Secret"
msgstr "Секретный ключ клиента Google"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__has_google_maps
msgid "Google Maps"
msgstr "Google Картa"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__google_maps_api_key
msgid "Google Maps API Key"
msgstr "Google Maps API ключ"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_google_spreadsheet
msgid "Google Spreadsheet"
msgstr "Google Электронные таблицы"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__social_googleplus
msgid "Google+ Account"
msgstr "Учетная запись Google+"

#. module: hotel_management
#: selection:rr.housekeeping,source:0
msgid "Guest"
msgstr "Гость"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__partner_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_resv_id_details__partner_name
#: model_terms:ir.ui.view,arch_db:hotel_management.arrival_dept_guest
#: model_terms:ir.ui.view,arch_db:hotel_management.roomwise_guestwise_report_view
msgid "Guest Name"
msgstr "Имя гостя"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__has_accounting_entries
msgid "Has Accounting Entries"
msgstr "Имеет бухгалтерские записи"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.agent_commission_invoic_form_view1
msgid "History"
msgstr "История"

#. module: hotel_management
#: model:ir.actions.report,name:hotel_management.report_hotel_folio1
msgid "Hotel Boarding Invoice"
msgstr "Счет Проформа"

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_hotel_folio
msgid "Hotel Folio Inherit Adding POS ORDER TABS"
msgstr ""

#. module: hotel_management
#: model:ir.ui.menu,name:hotel_management.hotel_management_reporting_analysis_menu
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_management_config_settings
msgid "Hotel Management"
msgstr "Управление Отелем"

#. module: hotel_management
#: model:res.groups,name:hotel_management.group_hotel_reservation_user
msgid "Hotel Management / Reseravation User"
msgstr "Управление Отелем / Пользователь Бронирований"

#. module: hotel_management
#: model:res.groups,name:hotel_management.group_restaurant_bot_user
msgid "Hotel Management / Restaurant BOT User"
msgstr "Управление Отелем / пользователь бара"

#. module: hotel_management
#: model:res.groups,name:hotel_management.group_restaurant_kot_user
msgid "Hotel Management / Restaurant KOT User"
msgstr "Управление Отелем / пользователь кухни"

#. module: hotel_management
#: model:res.groups,name:hotel_management.group_restaurant_user
msgid "Hotel Management / Restaurant User"
msgstr "Управление Отелем / пользователь ресторана"

#. module: hotel_management
#: model:res.groups,name:hotel_management.group_hotel_reservation_manager
msgid "Hotel Management/ Reseravation Manager"
msgstr "Управление Отелем / Менеджер По Бронированию"

#. module: hotel_management
#: model:res.groups,name:hotel_management.group_restaurant_manager
msgid "Hotel Management/ Restaurant Manager"
msgstr "Управление Отелем / Менеджер Ресторана"

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_hotel_menucard
msgid "Hotel Menucard Inherit Adding Point_of_sale category"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_res_partner__reservation_warn
#: model:ir.model.fields,field_description:hotel_management.field_res_users__reservation_warn
msgid "Hotel Reservation"
msgstr "Бронирование Гостиниц"

#. module: hotel_management
#: model:ir.actions.act_window,name:hotel_management.action_hotel_reservation_wizard
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_wizard_view
msgid "Hotel Reservation Report"
msgstr "Отчет О Бронировании Гостиницы"

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_hotel_room
msgid "Hotel Room"
msgstr "Гостиничный номер"

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_hotel_reservation_wizard
msgid "Hotel reservation Wizard"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_line__rr_line_id
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_line_wizard__rr_line_id
msgid "Housekeeping line id"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_advance_payment_wizard__id
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice__id
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice_line__id
#: model:ir.model.fields,field_description:hotel_management.field_arrival_dept_guest_wizard__id
#: model:ir.model.fields,field_description:hotel_management.field_cancel_foilo_wizard__id
#: model:ir.model.fields,field_description:hotel_management.field_checkout_configuration__id
#: model:ir.model.fields,field_description:hotel_management.field_dashboard_url__id
#: model:ir.model.fields,field_description:hotel_management.field_database_configuration__id
#: model:ir.model.fields,field_description:hotel_management.field_deposit_journal_entry_wizard1__id
#: model:ir.model.fields,field_description:hotel_management.field_folio_invoice_transfer_wizard__id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_line__id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_wizard__id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_resv_id_details__id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_room_booking_history__id
#: model:ir.model.fields,field_description:hotel_management.field_id_master__id
#: model:ir.model.fields,field_description:hotel_management.field_issue_material__id
#: model:ir.model.fields,field_description:hotel_management.field_issue_material_details__id
#: model:ir.model.fields,field_description:hotel_management.field_monthly_occupancy_wizard__id
#: model:ir.model.fields,field_description:hotel_management.field_product_product_line__id
#: model:ir.model.fields,field_description:hotel_management.field_report_hotel_management_arrival_dept_guest__id
#: model:ir.model.fields,field_description:hotel_management.field_report_hotel_management_hotel_report_view__id
#: model:ir.model.fields,field_description:hotel_management.field_report_hotel_management_hotel_reservation_checkin_report__id
#: model:ir.model.fields,field_description:hotel_management.field_report_hotel_management_hotel_reservation_checkout_report__id
#: model:ir.model.fields,field_description:hotel_management.field_report_hotel_management_hotel_reservation_report__id
#: model:ir.model.fields,field_description:hotel_management.field_report_hotel_management_hotel_reservation_room_report__id
#: model:ir.model.fields,field_description:hotel_management.field_report_hotel_management_monthly_occupency_report_view__id
#: model:ir.model.fields,field_description:hotel_management.field_report_hotel_management_roomwise_guestwise_report_view__id
#: model:ir.model.fields,field_description:hotel_management.field_room_guestwise_wizard__id
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping__id
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_line__id
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_line_wizard__id
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_wizard__id
msgid "ID"
msgstr "Номер"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_resv_id_details__name
msgid "ID Card Number"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_id_master__id_code
msgid "ID Code"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_folio_form_inherit_state
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_reservation_form1
msgid "ID Details"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio__id_line_ids
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__id_line_ids
msgid "ID Line"
msgstr ""

#. module: hotel_management
#: model:ir.actions.act_window,name:hotel_management.id_master_action
#: model:ir.ui.menu,name:hotel_management.id_master_submenu
#: model_terms:ir.ui.view,arch_db:hotel_management.id_master_form
#: model_terms:ir.ui.view,arch_db:hotel_management.id_master_tree
msgid "ID Master"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_id_master__name
msgid "ID Name"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_resv_id_details_form
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_resv_id_details_tree
msgid "Identification Details"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_reservation__message_unread
msgid "If checked new messages require your attention."
msgstr "Если отмечено, новые сообщения будут требовать вашего внимания."

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_reservation__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Если отмечено - новые сообщения требуют Вашего внимания."

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_reservation__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Если обозначено, некоторые сообщения имеют ошибку доставки."

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__social_default_image
msgid "If set, replaces the company logo as the default social share image."
msgstr "Если установлено, заменяет логотип компании как типичное изображение на `Поделиться в соцсети`."

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__alias_domain
msgid "If you have setup a catch-all email domain redirected to the Odoo server, enter the domain name here."
msgstr "Если у вас установлен всеохватывающие почтовый домен, перенаправляющий на сервер Odoo, введите здесь имя домена."

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_sale_shop__shop_img
msgid "Image"
msgstr "Изображение"

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_folio_laundry_line__product_image
#: model:ir.model.fields,help:hotel_management.field_hotel_folio_transport_line__product_image
#: model:ir.model.fields,help:hotel_management.field_hotel_food_line__product_image
msgid "Image of the product variant (Big-sized image of product template if false). It is automatically resized as a 1024x1024px image, with aspect ratio preserved."
msgstr "Изображение варианта товара (Изображение образца товара большого размера, если значение \"ошибочно\"). Его размер изменяется автоматически на изображение 1024x1024 пкс, с сохраненным соотношением сторон."

#. module: hotel_management
#: selection:hotel.management.config.settings,module_procurement_jit:0
msgid "Immediately after sales order confirmation"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_account_bank_statement_import_qif
msgid "Import .qif files"
msgstr "Импорт файлов .qif"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_account_bank_statement_import_csv
msgid "Import in .csv format"
msgstr "Импорт в формате .csv"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_account_bank_statement_import_ofx
msgid "Import in .ofx format"
msgstr "Импорт в формате .ofx"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_account_bank_statement_import_camt
msgid "Import in CAMT.053 format"
msgstr "Импорт в формате CAMT.053"

#. module: hotel_management
#: selection:sale.order,state:0
msgid "In Progress"
msgstr "в процессе"

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_hotel_reservation_order
msgid "Includes Hotel Reservation Order"
msgstr "Включает В Себя Заказ Бронирования Отеля"

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_hotel_restaurant_order
msgid "Includes Hotel Restaurant Order"
msgstr "Включает В Себя Заказ Отельного Ресторана"

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_hotel_restaurant_reservation
msgid "Includes Hotel Restaurant Reservation"
msgstr "Включает В Себя Бронирование Гостиничного Ресторана"

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_hotel_restaurant_tables
msgid "Includes Hotel Restaurant Table"
msgstr "Включает В Себя Столик В Ресторане Отеля"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_display_incoterm
msgid "Incoterms"
msgstr "Инкотермс"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__social_instagram
msgid "Instagram Account"
msgstr "Учетная запись Instagram"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__partner_autocomplete_insufficient_credit
msgid "Insufficient credit"
msgstr "недостаточный кредит"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_pos_mercury
msgid "Integrated Card Payments"
msgstr "Встроенные платежи по картам"

#. module: hotel_management
#: selection:rr.housekeeping,assign_to:0
msgid "Internal"
msgstr "Внутренний"

#. module: hotel_management
#: selection:rr.housekeeping,source:0
msgid "Internal Observation"
msgstr "Внутреннее Наблюдение"

#. module: hotel_management
#: selection:hotel.reservation,source:0
msgid "Internal Reservation"
msgstr "Внутреннее Резервирование"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_account_intrastat
msgid "Intrastat"
msgstr "Интрастат"

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_account_invoice
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_management_config_settings
msgid "Invoice"
msgstr "Счёт"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice__commission_line
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__invoice_lines
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__invoice_lines
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__invoice_lines
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_folio_form_inherit_state
msgid "Invoice Lines"
msgstr "Позиции счета"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_account_payment
msgid "Invoice Online Payment"
msgstr "Выставить счет на онлайн-оплату"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__invoice_status
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__invoice_status
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__invoice_status
msgid "Invoice Status"
msgstr "Статус счета"

#. module: hotel_management
#: selection:hotel.management.config.settings,default_invoice_policy:0
msgid "Invoice what is delivered"
msgstr ""

#. module: hotel_management
#: selection:hotel.management.config.settings,default_invoice_policy:0
msgid "Invoice what is ordered"
msgstr ""

#. module: hotel_management
#: selection:agent.commission.invoice,state:0
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__invoiced
msgid "Invoiced"
msgstr "Счёт выставлен"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__qty_invoiced
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__qty_invoiced
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__qty_invoiced
msgid "Invoiced Quantity"
msgstr "Фактурное Количество"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__default_invoice_policy
msgid "Invoicing Policy"
msgstr "Политика выставления счетов"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.roomwise_guestwise_report_view
msgid "Is Checkin"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.roomwise_guestwise_report_view
msgid "Is Checkout"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__message_is_follower
msgid "Is Follower"
msgstr "Подписчик"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__is_downpayment
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__is_downpayment
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__is_downpayment
msgid "Is a down payment"
msgstr "Аванс"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__is_expense
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__is_expense
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__is_expense
msgid "Is expense"
msgstr "Есть расходом"

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_folio_laundry_line__is_expense
#: model:ir.model.fields,help:hotel_management.field_hotel_folio_transport_line__is_expense
#: model:ir.model.fields,help:hotel_management.field_hotel_food_line__is_expense
msgid "Is true if the sales order line comes from an expense or a vendor bills"
msgstr "Правильно, если строка заказа на продажу поступает из расходов или счетов поставщиков"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.view_issue_material1
msgid "Issue"
msgstr ""

#. module: hotel_management
#: model:ir.actions.act_window,name:hotel_management.action_issue_material
#: model:ir.actions.act_window,name:hotel_management.open_issue_material_details_tree_new1
#: model:ir.model,name:hotel_management.model_issue_material
#: model:ir.ui.menu,name:hotel_management.menu_open_issue_material_details_new1
#: model_terms:ir.ui.view,arch_db:hotel_management.view_issue_material_details_form
#: model_terms:ir.ui.view,arch_db:hotel_management.view_issue_material_details_tree
msgid "Issue Material"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_issue_material_details
msgid "Issue Material Details"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_issue_material_details__name
msgid "Issue Slip"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_resv_id_details__issuing_auth
msgid "Issuing Authority"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_order_list__product_id
msgid "Item Name"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_advance_payment_wizard__journal_id
#: model:ir.model.fields,field_description:hotel_management.field_deposit_journal_entry_wizard1__journal_id
msgid "Journal"
msgstr "Журнал"

#. module: hotel_management
#: selection:hotel.menucard,product_nature:0
#: selection:hotel.restaurant.kitchen.order.tickets,product_nature:0
#: model:ir.ui.menu,name:hotel_management.menu_view_hotel_restaurant_kitchen_order_tickets_inherit
#: selection:product.template,product_nature:0
msgid "KOT"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_restaurant_kitchen_order_tickets_tree
msgid "KOT List"
msgstr ""

#. module: hotel_management
#: model:ir.actions.act_window,name:hotel_management.open_view_hotel_restaurant_kitchen_order_tickets_form_tree
msgid "KOT Order List"
msgstr ""

#. module: hotel_management
#: selection:hotel.management.config.settings,product_weight_in_lbs:0
msgid "Kilogram"
msgstr ""

#. module: hotel_management
#: model:ir.actions.report,name:hotel_management.hotel_reservation_order_kot
msgid "Kitchen Order Ticket"
msgstr ""

#. module: hotel_management
#: model:ir.actions.report,name:hotel_management.hotel_res_order_kot
msgid "Kitchen Order Tickets"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_auth_ldap
msgid "LDAP Authentication"
msgstr "LDAP Авторизация"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_stock_landed_costs
msgid "Landed Costs"
msgstr "Стоимость с издержками"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__language_ids
msgid "Languages"
msgstr "Языки"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_advance_payment_wizard____last_update
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice____last_update
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice_line____last_update
#: model:ir.model.fields,field_description:hotel_management.field_arrival_dept_guest_wizard____last_update
#: model:ir.model.fields,field_description:hotel_management.field_cancel_foilo_wizard____last_update
#: model:ir.model.fields,field_description:hotel_management.field_checkout_configuration____last_update
#: model:ir.model.fields,field_description:hotel_management.field_dashboard_url____last_update
#: model:ir.model.fields,field_description:hotel_management.field_database_configuration____last_update
#: model:ir.model.fields,field_description:hotel_management.field_deposit_journal_entry_wizard1____last_update
#: model:ir.model.fields,field_description:hotel_management.field_folio_invoice_transfer_wizard____last_update
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line____last_update
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line____last_update
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line____last_update
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings____last_update
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation____last_update
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_line____last_update
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_wizard____last_update
#: model:ir.model.fields,field_description:hotel_management.field_hotel_resv_id_details____last_update
#: model:ir.model.fields,field_description:hotel_management.field_hotel_room_booking_history____last_update
#: model:ir.model.fields,field_description:hotel_management.field_id_master____last_update
#: model:ir.model.fields,field_description:hotel_management.field_issue_material____last_update
#: model:ir.model.fields,field_description:hotel_management.field_issue_material_details____last_update
#: model:ir.model.fields,field_description:hotel_management.field_monthly_occupancy_wizard____last_update
#: model:ir.model.fields,field_description:hotel_management.field_product_product_line____last_update
#: model:ir.model.fields,field_description:hotel_management.field_report_hotel_management_arrival_dept_guest____last_update
#: model:ir.model.fields,field_description:hotel_management.field_report_hotel_management_hotel_report_view____last_update
#: model:ir.model.fields,field_description:hotel_management.field_report_hotel_management_hotel_reservation_checkin_report____last_update
#: model:ir.model.fields,field_description:hotel_management.field_report_hotel_management_hotel_reservation_checkout_report____last_update
#: model:ir.model.fields,field_description:hotel_management.field_report_hotel_management_hotel_reservation_report____last_update
#: model:ir.model.fields,field_description:hotel_management.field_report_hotel_management_hotel_reservation_room_report____last_update
#: model:ir.model.fields,field_description:hotel_management.field_report_hotel_management_monthly_occupency_report_view____last_update
#: model:ir.model.fields,field_description:hotel_management.field_report_hotel_management_roomwise_guestwise_report_view____last_update
#: model:ir.model.fields,field_description:hotel_management.field_room_guestwise_wizard____last_update
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping____last_update
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_line____last_update
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_line_wizard____last_update
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_wizard____last_update
msgid "Last Modified on"
msgstr "Последнее изменение"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_advance_payment_wizard__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice_line__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_arrival_dept_guest_wizard__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_cancel_foilo_wizard__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_checkout_configuration__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_dashboard_url__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_database_configuration__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_deposit_journal_entry_wizard1__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_folio_invoice_transfer_wizard__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_line__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_wizard__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_hotel_resv_id_details__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_hotel_room_booking_history__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_id_master__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_issue_material__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_issue_material_details__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_monthly_occupancy_wizard__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_product_product_line__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_room_guestwise_wizard__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_line__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_line_wizard__write_uid
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_wizard__write_uid
msgid "Last Updated by"
msgstr "Последний раз обновил"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_advance_payment_wizard__write_date
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice__write_date
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice_line__write_date
#: model:ir.model.fields,field_description:hotel_management.field_arrival_dept_guest_wizard__write_date
#: model:ir.model.fields,field_description:hotel_management.field_cancel_foilo_wizard__write_date
#: model:ir.model.fields,field_description:hotel_management.field_checkout_configuration__write_date
#: model:ir.model.fields,field_description:hotel_management.field_dashboard_url__write_date
#: model:ir.model.fields,field_description:hotel_management.field_database_configuration__write_date
#: model:ir.model.fields,field_description:hotel_management.field_deposit_journal_entry_wizard1__write_date
#: model:ir.model.fields,field_description:hotel_management.field_folio_invoice_transfer_wizard__write_date
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__write_date
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__write_date
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__write_date
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__write_date
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__write_date
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_line__write_date
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_wizard__write_date
#: model:ir.model.fields,field_description:hotel_management.field_hotel_resv_id_details__write_date
#: model:ir.model.fields,field_description:hotel_management.field_hotel_room_booking_history__write_date
#: model:ir.model.fields,field_description:hotel_management.field_id_master__write_date
#: model:ir.model.fields,field_description:hotel_management.field_issue_material__write_date
#: model:ir.model.fields,field_description:hotel_management.field_issue_material_details__write_date
#: model:ir.model.fields,field_description:hotel_management.field_monthly_occupancy_wizard__write_date
#: model:ir.model.fields,field_description:hotel_management.field_product_product_line__write_date
#: model:ir.model.fields,field_description:hotel_management.field_room_guestwise_wizard__write_date
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping__write_date
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_line__write_date
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_line_wizard__write_date
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_wizard__write_date
msgid "Last Updated on"
msgstr "Последнее обновление"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
msgid "Laundry"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio__laundry_invoice_ids
msgid "Laundry Related Invoices"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_folio_form_inherit_state
msgid "Laundry Related invoices"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_use_lead
msgid "Leads"
msgstr "Инициативы"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_line__line_id
msgid "Line"
msgstr "Строка"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__show_line_subtotals_tax_selection
msgid "Line Subtotals Tax Display"
msgstr "Отображать суммарные строки налога"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_website_links
msgid "Link Trackers"
msgstr "трекеры ссылки"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__social_linkedin
msgid "LinkedIn Account"
msgstr "Учетная запись LinkedIn"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__auto_done_setting
msgid "Lock Confirmed Sales"
msgstr "Заблокировать подтверждены продажи"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_res_partner__login_password
#: model:ir.model.fields,field_description:hotel_management.field_res_users__login_password
msgid "Login Password"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_stock_production_lot
msgid "Lots & Serial Numbers"
msgstr "Партии и серийные номера"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_mrp_routings
msgid "MRP Work Orders"
msgstr "Рабочие заказ виробицтва"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__message_main_attachment_id
msgid "Main Attachment"
msgstr "Основное прикрепления"

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__currency_id
msgid "Main currency of the company."
msgstr "Основная валюта компании."

#. module: hotel_management
#: selection:hotel.resv.id.details,gender:0
msgid "Male"
msgstr "Мужчина"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_inter_company_rules
msgid "Manage Inter Company"
msgstr "Управление межфирменным взаимодействием"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_multi_company
msgid "Manage multiple companies"
msgstr "Управление несколькими компаниями"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__generate_lead_from_alias
msgid "Manual Assignation of Emails"
msgstr "Ручное назначение емейлов"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_folio_form_inherit_state
msgid "Manual Description"
msgstr ""

#. module: hotel_management
#: selection:hotel.management.config.settings,module_procurement_jit:0
msgid "Manually or based on automatic scheduler"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__manufacturing_lead
msgid "Manufacturing Lead Time"
msgstr "Время производства"

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__use_security_lead
msgid "Margin of error for dates promised to customers. Products will be scheduled for delivery that many days earlier than the actual promised date, to cope with unexpected delays in the supply chain."
msgstr "Маржа ошибки для дат, обещанных покупателям. Товары запланированы на доставку на много дней раньше, чем фактическая обещанная дата, чтобы справиться с неожиданными задержками в цепочке поставок."

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__security_lead
msgid "Margin of error for dates promised to customers. Products will be scheduled for procurement and delivery that many days earlier than the actual promised date, to cope with unexpected delays in the supply chain."
msgstr "Допустимый предел погрешности для дат, обещанных клиентам. Закупка и поставка товаров будет назначена на несколько дней раньше, чем фактически обещанная дата, чтобы справиться с неожиданными задержками в системе поставок."

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_sale_margin
msgid "Margins"
msgstr "Маржа"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_mrp_mps
msgid "Master Production Schedule"
msgstr "Мастер планирования производства"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_line__info_id
msgid "Material Id"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.view_issue_material1
msgid "Material Issue"
msgstr ""

#. module: hotel_management
#: model:ir.actions.report,name:hotel_management.max_hotel_room_report
msgid "Max Room Detail"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__message_has_error
msgid "Message Delivery error"
msgstr "Ошибка доставки сообщения"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_res_partner__reservation_msg
#: model:ir.model.fields,field_description:hotel_management.field_res_users__reservation_msg
msgid "Message for Hotel Reservation"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__message_ids
msgid "Messages"
msgstr "Сообщения"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__qty_delivered_method
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__qty_delivered_method
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__qty_delivered_method
msgid "Method to update delivered qty"
msgstr "Метод обновления доставленной количества"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__propagation_minimum_delta
msgid "Minimum Delta for Propagation"
msgstr "Минимальная дельта для распространения"

#. module: hotel_management
#: model:ir.actions.act_window,name:hotel_management.action_monthly_occupancy_wizard_report
#: model:ir.actions.report,name:hotel_management.monthly_occupency_qweb
#: model:ir.ui.menu,name:hotel_management.menu_action_monthly_occupancy_report
#: model_terms:ir.ui.view,arch_db:hotel_management.monthly_occupency_report_view
msgid "Monthly Occupancy Report"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_multi_currency
msgid "Multi-Currencies"
msgstr "Мультивалютность"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_stock_adv_location
msgid "Multi-Step Routes"
msgstr "многошаговые маршруты"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_stock_multi_warehouses
msgid "Multi-Warehouses"
msgstr "несколько складов"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_multi_website
msgid "Multi-website"
msgstr "Несколько веб-сайтов"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__pos_sales_price
msgid "Multiple Product Prices"
msgstr "Несколько цен на товар"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__multi_sales_price
msgid "Multiple Sales Prices per Product"
msgstr "На товар указано несколько цен"

#. module: hotel_management
#: selection:hotel.management.config.settings,multi_sales_price_method:0
#: selection:hotel.management.config.settings,pos_pricelist_setting:0
#: selection:hotel.management.config.settings,sale_pricelist_setting:0
msgid "Multiple prices per product (e.g. customer segments, currencies)"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.arrival_dept_guest_wizard_view
#: model_terms:ir.ui.view,arch_db:hotel_management.monthly_occupancy_wizard_id
#: model_terms:ir.ui.view,arch_db:hotel_management.room_guest_wizard_view_new
msgid "My Button"
msgstr "Моя Пуговица"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.view_shop_form_inherit
msgid "Name"
msgstr "Название"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice_line__name
msgid "Name Inv Lines"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
msgid "Net Amt("
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Дедлайн следующему шагу"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__activity_summary
msgid "Next Activity Summary"
msgstr "Резюме следующего мероприятия"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__activity_type_id
msgid "Next Activity Type"
msgstr "Тип следующему шагу"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.cancel_foilo_wizard_form_view
msgid "No"
msgstr ""

#. module: hotel_management
#: selection:res.partner,reservation_warn:0
msgid "No Message"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__use_propagation_minimum_delta
msgid "No Rescheduling Propagation"
msgstr "Есть распространения расписания"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_line__number_of_days
msgid "Number Of Days"
msgstr "Количество дне	"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__number_of_rooms
msgid "Number Of Rooms"
msgstr "Количество комнат"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__message_needaction_counter
msgid "Number of Actions"
msgstr "Количество действий"

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_folio_laundry_line__customer_lead
#: model:ir.model.fields,help:hotel_management.field_hotel_folio_transport_line__customer_lead
#: model:ir.model.fields,help:hotel_management.field_hotel_food_line__customer_lead
msgid "Number of days between the order confirmation and the shipping of the products to the customer"
msgstr "Количество дней между подтверждением заказа и отгрузкой продукции заказчику"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__message_has_error_counter
msgid "Number of error"
msgstr "количество ошибок"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__language_count
msgid "Number of languages"
msgstr "количество языков"

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_reservation__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Количество сообщений, требующих внимания"

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_reservation__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Количество сообщений с ложной дставкою"

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_reservation__message_unread_counter
msgid "Number of unread messages"
msgstr "Количество непрочитанных сообщений"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__portal_confirmation_pay
msgid "Online Payment"
msgstr "платеж онлайн"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__portal_confirmation_sign
msgid "Online Signature"
msgstr "Онлайн-подпись"

#. module: hotel_management
#: selection:hotel.restaurant.order,state:0
#: model_terms:ir.ui.view,arch_db:hotel_management.view_restaurant_order_filter_inherit
msgid "Order Done"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_restaurant_kitchen_order_tickets_form_inheritance
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_restaurant_reservation_form_inherit1
msgid "Order List"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__order_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__order_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__order_id
msgid "Order Reference"
msgstr "Ссылка на заказ"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio__state
#: model:ir.model.fields,field_description:hotel_management.field_sale_order__state
msgid "Order State"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__state
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__state
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__state
msgid "Order Status"
msgstr "Статус заказа"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_route_so_lines
msgid "Order-Specific Routes"
msgstr "Специальные маршруты на заказе"

#. module: hotel_management
#: selection:hotel.reservation,activity_state:0
msgid "Overdue"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__pos_pricelist_setting
msgid "POS Pricelists"
msgstr "Прайс-листы точки продаж"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__product_packaging
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__product_packaging
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__product_packaging
msgid "Package"
msgstr "Упаковка"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.agent_commission_invoic_form_view1
msgid "Paid"
msgstr "Оплачено"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__paperformat_id
msgid "Paper format"
msgstr "Размер бумаги"

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_res_partner
msgid "Partner"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_room_booking_history__partner_id
msgid "Partner Name"
msgstr "Имя партнера"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_database_configuration__password
msgid "Password"
msgstr "Пароль"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.advance_payment_wizard1
msgid "Payment"
msgstr "Платеж"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_advance_payment_wizard__payment_date
#: model:ir.model.fields,field_description:hotel_management.field_deposit_journal_entry_wizard1__payment_date
msgid "Payment Date"
msgstr "Дата Платежа"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio__account_move_ids
#: model:ir.model.fields,field_description:hotel_management.field_sale_order__account_move_ids
msgid "Payment Details"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.monthly_occupency_report_view
msgid "Percentage"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_crm_phone_validation
msgid "Phone Formatting"
msgstr "телефонный формат"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__default_picking_policy
msgid "Picking Policy"
msgstr "политика упаковки"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_account_plaid
msgid "Plaid Connector"
msgstr "Plaid Коннектор"

#. module: hotel_management
#: selection:hotel.reservation,activity_state:0
msgid "Planned"
msgstr "Планируемый"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.view_issue_material1
msgid "Plz choose the Warehouse Location"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__access_url
msgid "Portal Access URL"
msgstr "Ссылка для доступа на портал"

#. module: hotel_management
#: selection:hotel.management.config.settings,product_weight_in_lbs:0
msgid "Pound"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_line__price
msgid "Price"
msgstr "Цена"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__price_reduce
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__price_reduce
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__price_reduce
msgid "Price Reduce"
msgstr "Уменьшение цены"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__price_reduce_taxexcl
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__price_reduce_taxexcl
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__price_reduce_taxexcl
msgid "Price Reduce Tax excl"
msgstr "Цена, уменьшенная на налог исключенный"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__price_reduce_taxinc
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__price_reduce_taxinc
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__price_reduce_taxinc
msgid "Price Reduce Tax inc"
msgstr "Цена, уменьшенная на налог включенный"

#. module: hotel_management
#: selection:hotel.management.config.settings,pos_pricelist_setting:0
#: selection:hotel.management.config.settings,sale_pricelist_setting:0
msgid "Price computed from formulas (discounts, margins, roundings)"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice__pricelist_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__pricelist_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_order__pricelist_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_kitchen_order_tickets__pricelist_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_order__pricelist_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_reservation__pricelist_id
msgid "Pricelist"
msgstr "Прайс-лист"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__sale_pricelist_setting
msgid "Pricelists"
msgstr "Прайс-листы"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__multi_sales_price_method
msgid "Pricelists Method"
msgstr "Метод прайс-листов"

#. module: hotel_management
#: selection:hotel.management.config.settings,multi_sales_price_method:0
msgid "Prices computed from formulas (discounts, margins, roundings)"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__invoice_is_print
msgid "Print"
msgstr "Печать"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__snailmail_duplex
msgid "Print Both sides"
msgstr "Печатать с обеих сторон"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__snailmail_color
msgid "Print In Color"
msgstr "Печать в цвете"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.arrival_dept_guest_wizard_view
#: model_terms:ir.ui.view,arch_db:hotel_management.monthly_occupancy_wizard_id
#: model_terms:ir.ui.view,arch_db:hotel_management.room_guest_wizard_view_new
msgid "Print Report"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_proforma_sales
msgid "Pro-Forma Invoice"
msgstr "Счет-проформа"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__product_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__product_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__product_id
#: model:ir.model.fields,field_description:hotel_management.field_product_product_line__product_product_id
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_line__product_id
msgid "Product"
msgstr "Продукт"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_line__product_line_ids
msgid "Product Details"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_room_booking_history__product_id
msgid "Product ID"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__product_image
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__product_image
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__product_image
msgid "Product Image"
msgstr "Изображения продукта "

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_mrp_plm
msgid "Product Lifecycle Management (PLM)"
msgstr "Управление жизненным циклом продукта (ЖЦП)"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_room_booking_history__name
msgid "Product Name"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_h_activity__product_nature
#: model:ir.model.fields,field_description:hotel_management.field_hotel_menucard__product_nature
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_kitchen_order_tickets__product_nature
#: model:ir.model.fields,field_description:hotel_management.field_hotel_room__product_nature
#: model:ir.model.fields,field_description:hotel_management.field_hotel_room_amenities__product_nature
#: model:ir.model.fields,field_description:hotel_management.field_hotel_services__product_nature
#: model:ir.model.fields,field_description:hotel_management.field_product_product__product_nature
#: model:ir.model.fields,field_description:hotel_management.field_product_template__product_nature
msgid "Product Nature"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_stock_packaging
msgid "Product Packagings"
msgstr "упаковка товара"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_issue_material_details__repair_ids
msgid "Product Replacement info"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.view_issue_material_details_form
#: model_terms:ir.ui.view,arch_db:hotel_management.view_rr_housekeeping_form
msgid "Product Requirement"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_product_template
msgid "Product Template"
msgstr "Шаблон продукта"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__product_no_variant_attribute_value_ids
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__product_no_variant_attribute_value_ids
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__product_no_variant_attribute_value_ids
msgid "Product attribute values that do not create variants"
msgstr "Значения атрибутов товара, которые не создают варианты"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_product_product_line__product_line_id
msgid "Product line id"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_product_product_line__qty
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_line__qty
msgid "Qty"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_quality_control
msgid "Quality"
msgstr "Качество"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__product_uom_qty
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__product_uom_qty
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__product_uom_qty
msgid "Quantity"
msgstr "Количество"

#. module: hotel_management
#: selection:sale.order,state:0
msgid "Quotation"
msgstr "Предложение цен"

#. module: hotel_management
#: selection:sale.order,state:0
msgid "Quotation Sent"
msgstr "Коммерческое предложение отправлено"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
msgid "Rate("
msgstr "Тариф"

#. module: hotel_management
#: selection:rr.housekeeping,activity:0
msgid "Repair"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.view_issue_material_details_form
#: model_terms:ir.ui.view,arch_db:hotel_management.view_rr_housekeeping_form
msgid "Repair / Repalcement Info"
msgstr "Информация О Ремонте / Замене"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_issue_material__rr_line_ids
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping__rr_line_ids
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_wizard__rr_line_ids
msgid "Repair / Replacement Info"
msgstr "Информация О Ремонте / Замене"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.view_rr_housekeeping_form
#: model_terms:ir.ui.view,arch_db:hotel_management.view_rr_housekeeping_tree
msgid "Repair Housekeeping"
msgstr ""

#. module: hotel_management
#: selection:rr.housekeeping,activity:0
msgid "Replace"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_arrival_dept_guest_wizard__arrival_dept
msgid "Report For"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.arrival_dept_guest_wizard_view
#: model_terms:ir.ui.view,arch_db:hotel_management.monthly_occupancy_wizard_id
#: model_terms:ir.ui.view,arch_db:hotel_management.room_guest_wizard_view_new
msgid "Report Wizard"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping__name
msgid "Req No"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_issue_material_details__request_id
msgid "Request Number"
msgstr ""

#. module: hotel_management
#: model:ir.actions.act_window,name:hotel_management.open_rr_housekeeping_form_tree_new1
#: model:ir.ui.menu,name:hotel_management.menu_open_rr_housekeeping_form_tree_new1
msgid "Request for Repair / Replacement"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping__requested_by
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping__requested_by_partner
msgid "Requested By"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__use_propagation_minimum_delta
msgid "Rescheduling applies to any chain of operations (e.g. Make To Order, Pick Pack Ship). In the case of MTO sales, a vendor delay (updated incoming date) impacts the expected delivery date to the customer. \n"
" This option allows to not propagate the rescheduling if the change is not critical."
msgstr "Изменение времени применяется к любому цепочки операций (например, сделать на заказ, Выбрать пакет). В случае продажи на заказ, задержка поставщика (обновление входящей даты) влияет на ожидаемую дату доставки клиенту. Эта опция позволяет не распространять перепланировки, если изменение не является критической."

#. module: hotel_management
#: model:ir.actions.act_window,name:hotel_management.open_hotel_reservation_form_tree11
#: model:ir.model,name:hotel_management.model_hotel_reservation
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_procurement_jit
#: model:ir.ui.menu,name:hotel_management.main_menu_hotel_reservation_tree_all
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_reservation_form1
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_reservation_graph
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_reservation_tree1
msgid "Reservation"
msgstr "Резервирование"

#. module: hotel_management
#: model:ir.actions.report,name:hotel_management.hotel_reservation_details_report
msgid "Reservation Detail"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_resv_id_details__reservation_id
msgid "Reservation Id"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_hotel_reservation_line
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__reservation_line
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_reservation_form1
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_reservation_line_from
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_reservation_line_tree
msgid "Reservation Line"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_report
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_wizard_view
msgid "Reservation List"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__reservation_no
msgid "Reservation No"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_advance_payment_wizard__reservation_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio__reservation_id
msgid "Reservation Ref"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__total_tax
msgid "Reservation Tax"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__module_procurement_jit
msgid "Reserving products manually in delivery orders or by running the scheduler is advised to better manage priorities in case of long customer lead times or/and frequent stock-outs."
msgstr "Резервируя товары вручную в заказах на доставку или управляя планировщиком, рекомендуется лучше управлять приоритетами в случае долгосрочного обслуживания клиентов и / или частого извлечения запасов."

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__activity_user_id
msgid "Responsible User"
msgstr "Ответственный"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
msgid "Restaurant"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_folio_form_inherit_state
msgid "Restaurant Lines"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_account_deferred_revenue
msgid "Revenue Recognition"
msgstr "Признание Дохода"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_order__room_no
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_reservation__room_no
#: model:ir.model.fields,field_description:hotel_management.field_hotel_room_booking_history__history_id
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping__room_no
#: model_terms:ir.ui.view,arch_db:hotel_management.arrival_dept_guest
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_folio_form_inherit_state
#: model_terms:ir.ui.view,arch_db:hotel_management.roomwise_guestwise_report_view
msgid "Room No"
msgstr "Номер комнаты"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_line__room_number
msgid "Room Number"
msgstr "Номер комнаты"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
msgid "Room Rate("
msgstr "Тариф комнаты"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_room__room_folio_ids
msgid "Room Rental History"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_line__categ_id
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_reservation_line_from
msgid "Room Type"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_room_report
msgid "Room Usage Report"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_wizard_view
msgid "Room Used Maximum"
msgstr ""

#. module: hotel_management
#: model:ir.actions.act_window,name:hotel_management.action_room_guestwise_wizard_report
#: model:ir.ui.menu,name:hotel_management.menu_action_room_guestwise_report
#: model_terms:ir.ui.view,arch_db:hotel_management.roomwise_guestwise_report_view
msgid "Room and Guestwise Report"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_room_guestwise_wizard
msgid "Room wise Guest wise Wizard"
msgstr ""

#. module: hotel_management
#: model:ir.actions.report,name:hotel_management.roomwise_guestwise_qweb
msgid "Roomwise and Guestwise"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__route_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__route_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__route_id
msgid "Route"
msgstr "Маршрут"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_account_sepa
msgid "SEPA Credit Transfer (SCT)"
msgstr "Кредитные переводы SEPA (SCT)"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_warning_sale
msgid "Sale Order Warnings"
msgstr "Предупреждение заказ на продажу"

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_sale_order
msgid "Sales Order"
msgstr "Заказы продаж"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.sale_view_order_tree1
msgid "Sales Orders"
msgstr "Заказы продаж"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__salesman_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__salesman_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__salesman_id
msgid "Salesperson"
msgstr "Продавец"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_room_booking_history_search
msgid "Search Booking History"
msgstr "Поиск Истории Бронирования"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__security_lead
msgid "Security Lead Time"
msgstr "Надежный временной цикл"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__use_security_lead
msgid "Security Lead Time for Sales"
msgstr "Безопасность времени проведения для продаж"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__access_token
msgid "Security Token"
msgstr "Токен безопасности"

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__manufacturing_lead
msgid "Security days for each manufacturing operation."
msgstr "Дни безопасности для каждой технологической операции."

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__invoice_is_email
msgid "Send Email"
msgstr "Отправить эл. письмо"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__invoice_is_snailmail
msgid "Send by Post"
msgstr "Отправить по почте"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__sequence
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__sequence
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__sequence
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_order_list__name
msgid "Sequence"
msgstr "Нумерация"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.arrival_dept_guest
msgid "Serial No."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_deposit_journal_entry_wizard1__service_cost
msgid "Service Cost"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__company_share_partner
msgid "Share partners to all companies"
msgstr "Поделиться партнерами со всеми компаниями"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__company_share_product
msgid "Share product to all companies"
msgstr "Поделится продуктом со всеми компаниями"

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__company_share_partner
msgid "Share your partners to all companies defined in your instance.\n"
" * Checked : Partners are visible for every companies, even if a company is defined on the partner.\n"
" * Unchecked : Each company can see only its partner (partners where company is defined). Partners not related to a company are visible for all companies."
msgstr "Поделитесь своими партнёрами со всеми компаниями, определёнными в вашем экземпляре.\n"
"* Установлено: Партнёры являются видимыми для каждой компании, даже если компания определена у партнёра.\n"
"* Снято: Каждая компания может видеть только своего партнёра (партнёров компании, где определена). Партнёры, не относящиеся к какой-нибудь компании, видимы для всех компаний."

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__company_share_product
msgid "Share your product to all companies defined in your instance.\n"
" * Checked : Product are visible for every company, even if a company is defined on the partner.\n"
" * Unchecked : Each company can see only its product (product where company is defined). Product not related to a company are visible for all companies."
msgstr "Предоставьте свой продукт всем компаниям, определенным в вашем экземпляре.\n"
"  * Проверено: Продукт виден для каждой компании, даже если компания определена для партнера.\n"
"  * Не проверено: Каждая компания может видеть только свой продукт (продукт, в котором определена компания). Продукт, не связанный с компанией, видимый для всех компаний."

#. module: hotel_management
#: selection:hotel.management.config.settings,default_picking_policy:0
msgid "Ship all products at once"
msgstr ""

#. module: hotel_management
#: selection:hotel.management.config.settings,default_picking_policy:0
msgid "Ship products as soon as available, with back orders"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_delivery
msgid "Shipping Costs"
msgstr "Затраты на доставку"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_checkout_configuration__shop_id
#: model:ir.model.fields,field_description:hotel_management.field_h_activity__shop_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_menucard__shop_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__shop_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_order__shop_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_kitchen_order_tickets__shop_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_order__shop_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_reservation__shop_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_tables__shop_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_room__shop_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_room_amenities__shop_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_services__shop_id
#: model:ir.model.fields,field_description:hotel_management.field_issue_material_details__shop_id
#: model:ir.model.fields,field_description:hotel_management.field_product_product__shop_id
#: model:ir.model.fields,field_description:hotel_management.field_product_template__shop_id
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping__shop_id
msgid "Shop"
msgstr ""

#. module: hotel_management
#: sql_constraint:checkout.configuration:0
msgid "Shop must be unique !"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__show_effect
msgid "Show Effect"
msgstr "Показать результат"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_show_line_subtotals_tax_included
msgid "Show line subtotals with taxes (B2C)"
msgstr "Показать сумму строк без налогов (B2C)"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_show_line_subtotals_tax_excluded
msgid "Show line subtotals without taxes (B2B)"
msgstr "Показывать строки подытогов без налогов (для юридических лиц)"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_product_pricelist
msgid "Show pricelists On Products"
msgstr "Показать прайс-листы на продукты"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_pricelist_item
msgid "Show pricelists to customers"
msgstr "Показать прайс клиентам"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__source
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping__source
msgid "Source"
msgstr "Источник"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_issue_material__location_id
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_line__source_locatiion
msgid "Source Location"
msgstr "Первоначальное расположение"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__source_origin
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__source_origin
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__source_origin
msgid "Source Origin"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_product_email_template
msgid "Specific Email"
msgstr "Специфический емейл"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__specific_user_account
msgid "Specific User Account"
msgstr "Специальный учетную запись"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__state
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_order__state
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_order_list__states
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_tables__state
#: model:ir.model.fields,field_description:hotel_management.field_hotel_room_booking_history__state
#: model:ir.model.fields,field_description:hotel_management.field_issue_material_details__state
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping__state
msgid "State"
msgstr "Регион"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_folio_form_inherit_state
msgid "States"
msgstr "Регионы"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice__state
msgid "Status"
msgstr "Статус"

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_reservation__activity_state
msgid "Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr "Этап основан на действиях Просроченный: срок исполнения уже прошел Сегодня: дата действия сегодня Запланировано: будущие действия."

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__move_ids
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__move_ids
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__move_ids
msgid "Stock Moves"
msgstr "Движения запасов"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_stock_multi_locations
msgid "Storage Locations"
msgstr "местонахождение хранения"

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__group_stock_multi_locations
msgid "Store products in specific locations of your warehouse (e.g. bins, racks) and to track inventory accordingly."
msgstr "Храните товары в определенных местах своего состава (например, коробки, стеллажи) и в соответствии отслеживайте запасы."

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_line__sub_total1
msgid "Sub Total"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_order_bill11
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_restaurant_order_bill11
msgid "Sub Total :"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_subtask_project
msgid "Sub-tasks"
msgstr "Подзадачи"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_order__amount_subtotal
msgid "SubTotal"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__price_subtotal
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__price_subtotal
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__price_subtotal
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_order__amount_subtotal
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_order_list__price_subtotal
msgid "Subtotal"
msgstr "Подытог"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_tables__name
msgid "Table number"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
msgid "Tariff"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_hr_timesheet
msgid "Task Logs"
msgstr "Логи задач"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_order__amount_tax
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_order__amount_tax
msgid "Tax"
msgstr "Налог"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_order_bill11
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_restaurant_order_bill11
msgid "Tax :"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__tax_cash_basis_journal_id
msgid "Tax Cash Basis Journal"
msgstr "Журнал Налога на Наличной Основе "

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_reservation_line_from
msgid "Tax On Product"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__tax_calculation_rounding_method
msgid "Tax calculation rounding method"
msgstr "Метод округления при расчете налога"

#. module: hotel_management
#: selection:hotel.management.config.settings,show_line_subtotals_tax_selection:0
msgid "Tax-Excluded"
msgstr ""

#. module: hotel_management
#: selection:hotel.management.config.settings,show_line_subtotals_tax_selection:0
msgid "Tax-Included"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio__amount_tax
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__tax_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__tax_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__tax_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_line__taxes_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_order_list__tax_id
#: model:ir.model.fields,field_description:hotel_management.field_sale_order__amount_tax
msgid "Taxes"
msgstr "Налоги"

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_folio_laundry_line__display_type
#: model:ir.model.fields,help:hotel_management.field_hotel_folio_transport_line__display_type
#: model:ir.model.fields,help:hotel_management.field_hotel_food_line__display_type
msgid "Technical field for UX purpose."
msgstr "Техническое поле для назначения UX."

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__chart_template_id
msgid "Template"
msgstr "Шаблон"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__auth_signup_template_user_id
msgid "Template user for new users created through signup"
msgstr "Шаблон новых пользователей, созданных в процессе регистрации"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__sale_note
msgid "Terms & Conditions"
msgstr "Сроки и условия"

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__currency_exchange_journal_id
msgid "The accounting journal where automatic exchange differences will be registered"
msgstr "Журнал бухучета, в котором будут зарегистрированы автоматические курсовые разницы"

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_folio__amount_untaxed
#: model:ir.model.fields,help:hotel_management.field_hotel_reservation__total_tax
#: model:ir.model.fields,help:hotel_management.field_sale_order__amount_untaxed
msgid "The amount without tax."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__account_bank_reconciliation_start
msgid "The bank reconciliation widget won't ask to reconcile payments older than this date.\n"
"               This is useful if you install accounting after having used invoicing for some time and\n"
"               don't want to reconcile all the past payments with bank statements."
msgstr "Виджет согласования банковской выписки не потребует согласования платежей старше этой даты. Это полезно, если вы устанавливаете бухгалтерский учет после того, как в течение некоторого времени используете выставления счетов, и не хотите согласовывать все прошлые платежи с банковскими выписками."

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__automatic_invoice
msgid "The invoice is generated automatically and available in the customer portal when the transaction is confirmed by the payment acquirer.\n"
"The invoice is marked as paid and the payment is registered in the payment journal defined in the configuration of the payment acquirer.\n"
"This mode is advised if you issue the final invoice at the order and not after the delivery."
msgstr "Счет-фактура создается автоматически и доступен на портале клиентов, когда операция подтверждена эквайером платежа. Счет-фактуру обозначено как оплачено, а платеж зарегистрирован в платежном журнале, определенном в настройках получателя платежа. Этот режим рекомендуется, если вы выдаете выставляете счет-фактуру в заказе, а не после доставки."

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_folio__amount_tax
#: model:ir.model.fields,help:hotel_management.field_sale_order__amount_tax
msgid "The tax amount."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__module_pos_mercury
msgid "The transactions are processed by Vantiv. Set your Vantiv credentials on the related payment journal."
msgstr "Операции обрабатываются Vantiv. Установите учетные данные Vantiv в журнале платежей."

#. module: hotel_management
#: selection:rr.housekeeping,assign_to:0
msgid "Third Party"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__module_account_batch_payment
msgid "This allows you grouping payments into a single batch and eases the reconciliation process.\n"
"-This installs the account_batch_payment module."
msgstr "Это позволяет группировать платежи в один пакет и облегчает процесс согласования. - Это устанавливает модуль account_batch_payment."

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_sale_shop__shop_img
msgid "This field holds the image for this shop, limited to 1024x1024px"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__favicon
msgid "This field holds the image used to display a favicon on the website."
msgstr "Это поле содержит изображение, используемое для отображения фавиконки на веб-сайте."

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__website_default_lang_code
msgid "This field is used to set/get locales for user"
msgstr "Это поле используется для установки/получения локалий пользователя"

#. module: hotel_management
#: selection:hotel.reservation,source:0
msgid "Through GDS"
msgstr ""

#. module: hotel_management
#: selection:hotel.reservation,source:0
msgid "Through Web"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_wizard__date_end
#: model:ir.model.fields,field_description:hotel_management.field_monthly_occupancy_wizard__end_date
#: model:ir.model.fields,field_description:hotel_management.field_room_guestwise_wizard__date_end
msgid "To Date"
msgstr ""

#. module: hotel_management
#: selection:sale.order,state:0
msgid "To Invoice"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__qty_to_invoice
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__qty_to_invoice
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__qty_to_invoice
msgid "To Invoice Quantity"
msgstr "Количество счетов-фактур"

#. module: hotel_management
#: selection:hotel.reservation,activity_state:0
msgid "Today"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice__total_amt
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio__amount_total
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__price_total
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__price_total
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__price_total
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_order__amount_total
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_order__amount_total
#: model:ir.model.fields,field_description:hotel_management.field_sale_order__amount_total
msgid "Total"
msgstr "Всего"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_reservation_order_bill11
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_restaurant_order_bill11
msgid "Total :"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio__total_advance
#: model:ir.model.fields,field_description:hotel_management.field_sale_order__total_advance
msgid "Total Advance Payment"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
msgid "Total Amt("
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_agent_commission_invoice_line__tour_cost
msgid "Total Cost"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.monthly_occupency_report_view
msgid "Total Gross"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.monthly_occupency_report_view
msgid "Total Laundry"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio__remaining_amt
#: model:ir.model.fields,field_description:hotel_management.field_sale_order__remaining_amt
msgid "Total Remaining Amount"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.monthly_occupency_report_view
msgid "Total Rent"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__total_cost1
msgid "Total Reservation cost"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.monthly_occupency_report_view
msgid "Total Rest"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.monthly_occupency_report_view
msgid "Total Service"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__price_tax
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__price_tax
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__price_tax
#: model_terms:ir.ui.view,arch_db:hotel_management.monthly_occupency_report_view
msgid "Total Tax"
msgstr "сумма налога"

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__module_product_expiry
msgid "Track following dates on lots & serial numbers: best before, removal, end of life, alert. \n"
" Such dates are set automatically at lot/serial number creation based on values set on the product (in days)."
msgstr "Отслеживайте следующие даты на партиях и серийных номерах: лучше до, удаления, окончание срока действия, уведомления. Такие даты устанавливаются автоматически при создании партии / серийного номера на основе значений, установленных на товаре (в днях)."

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_folio_invoice_transfer_wizard__trans_folio_id
msgid "Transfer Folio Ref"
msgstr ""

#. module: hotel_management
#: model:ir.actions.act_window,name:hotel_management.action_folio_invoice_transfer_wizard
#: model_terms:ir.ui.view,arch_db:hotel_management.view_folio_invoice_transfer_wizard
msgid "Transfer Invoice"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio__transfer_invoice_ids
#: model:ir.model.fields,field_description:hotel_management.field_sale_order__transfer_invoice_ids
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_folio_form_inherit_state
msgid "Transfer Invoice Details"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_base_gengo
msgid "Translate Your Website with Gengo"
msgstr "Переложите ваш сайт с Gengo"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
msgid "Transport"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio__transport_line_ids
msgid "Transport Line"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio__transport_invoice_ids
msgid "Transport Related Invoices"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_folio_form_inherit_state
msgid "Transport Related invoices"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__social_twitter
msgid "Twitter Account"
msgstr "Twitter аккаунт"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_product_product_line__uom
#: model:ir.model.fields,field_description:hotel_management.field_rr_housekeeping_line__uom
msgid "UOM"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_delivery_ups
msgid "UPS Connector"
msgstr "коннектор UPS"

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__cdn_filters
msgid "URL matching those filters will be rewritten using the CDN Base URL"
msgstr "URL сопоставления этих фильтров будут переписаны с использованием CDN Base URL"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_delivery_usps
msgid "USPS Connector"
msgstr "коннектор USPS"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__price_unit
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__price_unit
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__price_unit
msgid "Unit Price"
msgstr "Цена за ед."

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__product_uom
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__product_uom
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__product_uom
msgid "Unit of Measure"
msgstr "Единица измерения"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_uom
msgid "Units of Measure"
msgstr "Единицы измерения"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__message_unread
msgid "Unread Messages"
msgstr "Непрочитанные Сообщения"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Счетчик непрочитанных сообщений"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_web_unsplash
msgid "Unsplash Image Library"
msgstr "Библиотека изображений Unsplash"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio__amount_untaxed
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__untaxed_amt
#: model:ir.model.fields,field_description:hotel_management.field_sale_order__amount_untaxed
msgid "Untaxed Amount"
msgstr "Сумма без налога"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__untaxed_amount_to_invoice
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__untaxed_amount_to_invoice
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__untaxed_amount_to_invoice
msgid "Untaxed Amount To Invoice"
msgstr "Облагаемая налогом сумма для выставления в счете-фактуре"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__untaxed_amount_invoiced
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__untaxed_amount_invoiced
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__untaxed_amount_invoiced
msgid "Untaxed Invoiced Amount"
msgstr "Облагаемая налогом сумма счета-фактуры"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_folio_form_inherit_sale
msgid "Update history"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_project_rating
msgid "Use Rating on Project"
msgstr "Используйте оценки в проекте"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_account_sepa_direct_debit
msgid "Use SEPA Direct Debit"
msgstr "Использовать прямой дебет SEPA"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_account_batch_payment
msgid "Use batch payments"
msgstr "Используйте оплату пакетом"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_auth_oauth
msgid "Use external authentication providers (OAuth)"
msgstr "Использовать внешние аутентификационные сервисы (OAuth)"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_sale_pricelist
msgid "Use pricelists to adapt your price per customers"
msgstr "Используйте прайс-листы для установки персональных цен для клиентов"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_products_in_bills
msgid "Use products in vendor bills"
msgstr "Используйте товары в счетах поставщика"

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_management_config_settings__website_country_group_ids
msgid "Used when multiple websites have the same domain."
msgstr "Используется, когда несколько веб-сайтов имеют один и тот же домен."

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_database_configuration__user_name
msgid "User Name"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__product_custom_attribute_value_ids
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__product_custom_attribute_value_ids
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__product_custom_attribute_value_ids
msgid "User entered custom product attribute values"
msgstr "Пользователь ввел значения атрибутов специального товара"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_resv_id_details__valid_from
msgid "Valid From"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_resv_id_details__valid_to
msgid "Valid To"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_product_variant
msgid "Variants and Options"
msgstr "Варианты и опции"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__via
msgid "Via"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.deposit_journal_entry_wizard1
msgid "Visa Journal Entry"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation_order__waitername1
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_order__waiter_name1
msgid "Waiter Name"
msgstr ""

#. module: hotel_management
#: selection:res.partner,reservation_warn:0
msgid "Warning"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_warning_stock
msgid "Warnings for Stock"
msgstr "Предупреждение для склада"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__group_warning_account
msgid "Warnings in Invoices"
msgstr "Предупреждение в счетах"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__website_company_id
msgid "Website Company"
msgstr "Компания сайта"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__website_domain
msgid "Website Domain"
msgstr "Домен сайта"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_reservation__website_message_ids
msgid "Website Messages"
msgstr "Сообщения с сайта"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__website_name
msgid "Website Name"
msgstr "Название сайта"

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_reservation__website_message_ids
msgid "Website communication history"
msgstr "История общения с сайта"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__product_weight_in_lbs
msgid "Weight unit of measure"
msgstr "Единица измерения веса"

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_reservation_line__room_number
msgid "Will list out all the rooms that belong to selected shop."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_restaurant_order__partner_id
#: model:ir.model.fields,help:hotel_management.field_hotel_restaurant_reservation__cname
msgid "Will show customer name corresponding to selected room no."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_restaurant_order__room_no
#: model:ir.model.fields,help:hotel_management.field_hotel_restaurant_reservation__room_no
msgid "Will show list of currently occupied room no that belongs to selected shop."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_restaurant_order__shop_id
#: model:ir.model.fields,help:hotel_management.field_hotel_restaurant_reservation__shop_id
msgid "Will show list of shop that belongs to allowed companies of logged-in user."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_checkout_configuration__shop_id
msgid "Will show list of shop that belongs to allowed companies of logged-in user. \n"
" -Assign a shop to configure shop-wise check out policy."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_h_activity__shop_id
#: model:ir.model.fields,help:hotel_management.field_hotel_menucard__shop_id
#: model:ir.model.fields,help:hotel_management.field_hotel_room__shop_id
#: model:ir.model.fields,help:hotel_management.field_hotel_room_amenities__shop_id
#: model:ir.model.fields,help:hotel_management.field_hotel_services__shop_id
#: model:ir.model.fields,help:hotel_management.field_product_product__shop_id
#: model:ir.model.fields,help:hotel_management.field_product_template__shop_id
msgid "Will show list of shop that belongs to allowed companies of logged-in user. \n"
" -Assigning a shop will make product exclusive for selected shop."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_restaurant_tables__shop_id
msgid "Will show list of shop that belongs to allowed companies of logged-in user. \n"
" -Assigning shop name to which this table no belongs to."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_mrp_workorder
msgid "Work Orders"
msgstr "Производственные задания"

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.cancel_foilo_wizard_form_view
msgid "Yes"
msgstr ""

#. module: hotel_management
#: constraint:res.currency:0
msgid "You Can not have more than 1 base currencies."
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,help:hotel_management.field_hotel_folio_laundry_line__order_partner_id
#: model:ir.model.fields,help:hotel_management.field_hotel_folio_transport_line__order_partner_id
#: model:ir.model.fields,help:hotel_management.field_hotel_food_line__order_partner_id
msgid "You can find a customer by its Name, TIN, Email or Internal Reference."
msgstr "Вы можете найти клиента по его имени, ИНН, электронной почте или внутренним ссылке."

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__social_youtube
msgid "Youtube Account"
msgstr "Учетная запись на Youtube"

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__module_delivery_bpost
msgid "bpost Connector"
msgstr "коннектор bpost"

#. module: hotel_management
#: model:ir.actions.act_window,name:hotel_management.action_cancel_on_datecheck
msgid "cancel folio"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.hotel_report_view
msgid "currency exchange rate :"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_dashboard_url
msgid "dashboard.url"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__folio_id
msgid "folio_id"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_transport_line__transport_line_id
#: model:ir.model.fields,field_description:hotel_management.field_hotel_food_line__food_line_id
msgid "food_line_id"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_hotel_food_line
msgid "hotel Food line"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_hotel_service_line
msgid "hotel Service line"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_hotel_folio_laundry_line
msgid "hotel folio laundry line"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_hotel_folio_transport_line
msgid "hotel folio transport line"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_hotel_folio_line
msgid "hotel folio1 room line"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_hotel_management_config_settings
msgid "hotel.management.config.settings"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_hotel_room_booking_history
msgid "hotel.room.booking.history"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_folio_laundry_line__laundry_line_id
msgid "laundry ref"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_monthly_occupancy_wizard
msgid "monthly.occupancy.wizard"
msgstr ""

#. module: hotel_management
#: model_terms:ir.ui.view,arch_db:hotel_management.view_hotel_management_config_settings
msgid "or"
msgstr "или"

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_product_product_line
msgid "product.product.line"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_order_list__previous_qty
msgid "quantities"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_order_list__product_qty
msgid "quantity"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_report_hotel_management_arrival_dept_guest
msgid "report.hotel_management.arrival_dept_guest"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_report_hotel_management_hotel_report_view
msgid "report.hotel_management.hotel_report_view"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_report_hotel_management_hotel_reservation_checkin_report
msgid "report.hotel_management.hotel_reservation_checkin_report"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_report_hotel_management_hotel_reservation_checkout_report
msgid "report.hotel_management.hotel_reservation_checkout_report"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_report_hotel_management_hotel_reservation_report
msgid "report.hotel_management.hotel_reservation_report"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_report_hotel_management_hotel_reservation_room_report
msgid "report.hotel_management.hotel_reservation_room_report"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_report_hotel_management_monthly_occupency_report_view
msgid "report.hotel_management.monthly_occupency_report_view"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_report_hotel_management_roomwise_guestwise_report_view
msgid "report.hotel_management.roomwise_guestwise_report_view"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_room_booking_history__category_id
msgid "room category"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_rr_housekeeping_line
msgid "rr.housekeeping.line"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_rr_housekeeping_line_wizard
msgid "rr_housekeeping_line_wizard"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_rr_housekeeping_wizard
msgid "rr_housekeeping_wizard"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_sale_shop
msgid "sale shop used for territory name"
msgstr ""

#. module: hotel_management
#: model:ir.model,name:hotel_management.model_rr_housekeeping
msgid "test"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_restaurant_order_list__total
msgid "total"
msgstr ""

#. module: hotel_management
#: model:ir.model.fields,field_description:hotel_management.field_hotel_management_config_settings__website_id
msgid "website"
msgstr "веб-сайт"

