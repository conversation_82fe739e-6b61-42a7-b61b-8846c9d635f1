<?xml version="1.0" encoding="UTF-8"?>
<!-- Copyright (c) 2017-Present Webkul Software Pvt. Ltd. (<https://webkul.com/>) -->
<!-- See LICENSE file for full copyright and licensing details. -->
<!-- License URL: https://store.webkul.com/license.html/ -->

<odoo>
    <template id="website_layout" inherit_id="website.layout" name="FB Pixel Snippet" priority="210">
      <xpath expr="//meta[@name='generator']" position="before">
        <t t-if="website and website.fb_pixel_key and not editable">
          &lt;!-- Facebook Pixel Code --&gt;
          <script id='fb_pixel'>
            !function(f,b,e,v,n,t,s)
            {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
            n.callMethod.apply(n,arguments):n.queue.push(arguments)};
            if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
            n.queue=[];t=b.createElement(e);t.async=!0;
            t.src=v;s=b.getElementsByTagName(e)[0];
            s.parentNode.insertBefore(t,s)}(window, document,'script',
            'https://connect.facebook.net/en_US/fbevents.js');
            fbq('init', '<t t-esc="website.fb_pixel_key"/>');
            fbq('track', 'PageView');
          </script>
          <noscript><img height="1" width="1" style="display:none"
            t-attf-src="https://www.facebook.com/tr?id=#{website.fb_pixel_key}&amp;ev=PageView&amp;noscript=1"
          /></noscript>
          &lt;!-- End Facebook Pixel Code --&gt;
        </t>
        <t t-else="">
          <script id='fb_pixel'>
            fbq=function(){
              console.log("Facebook pixel event will not trigger for website admin.");
            }
          </script>
        </t>
      </xpath>
    </template>

    <template id="products" inherit_id="website_sale.products" name="Pixel search track" priority="210">
      <xpath expr="//div[@id='wrap']" position="before">
        <t t-if="website and website.fb_pixel_key and not editable and search">
          <script>
            fbq('track', 'Search', {
              search_string: '<t t-esc='search'/>',
            });
          </script>
        </t>
      </xpath>
    </template>

    <template id="product" inherit_id="website_sale.product" name="Pixel product track" priority="210">
        <xpath expr="//input[@name='product_id']" position="after">
          <input type="hidden" class="default_code" name="default_code" t-att-value="product_variant.default_code" />
        </xpath>
        <xpath expr="//div[@id='wrap']" position="after">
          <t t-if="website and website.fb_pixel_key and not editable">
            <script>
              fbq('track', 'ViewContent', {
                value: <t t-esc="combination_info['price']"/>,
                currency: '<t t-esc="website.currency_id.name"/>',
                content_ids: ['<t t-esc="combination_info['default_code']"/>'],
                content_type: 'Product',
              });
            </script>
          </t>
        </xpath>
        <xpath expr="//a[@id='add_to_cart']" position="attributes">
          <t t-if="website and website.fb_pixel_key and not editable">
            <attribute name="t-attf-onclick" separator=" " add="
              fbq('track', 'AddToCart', {
                content_name: '#{product.name}',
                content_category: '#{category.name if category else ''}',
                content_ids: [document.getElementsByName('default_code')[0].value],
                content_type: 'product',
                value: document.querySelector('.oe_price .oe_currency_value').textContent,
                currency: '#{website.currency_id.name}'
              });" />
          </t>
        </xpath>
    </template>

    <template id="checkout_button" inherit_id="website_sale.navigation_buttons" name="Pixel checkout track">
      <xpath expr="//a[@name='website_sale_main_button']" position="attributes">
        <t t-if="website and website.fb_pixel_key and not editable">
          <attribute name="t-attf-onclick" separator=" " add="
            fbq('track', 'InitiateCheckout', {
              value: '#{website.sale_get_order().amount_total}',
              currency: '#{website.currency_id.name}'
            });"
            />
        </t>
      </xpath>
    </template>


    <template id="payment" inherit_id="website_sale.payment" name="Pixel payment track" priority="210">
      <xpath expr="//div[@id='address_on_payment']" position="after">
        <t t-if="website and website.fb_pixel_key and not editable">
          <script>
            fbq('track', 'AddPaymentInfo');
          </script>
        </t>
      </xpath>
    </template>
  
    <template id="confirmation" inherit_id="website_sale.confirmation" name="Pixel purchase track" priority="210">
      <xpath expr="//div[@id='oe_structure_website_sale_confirmation_3']" position="after">
        <t t-if="website and website.fb_pixel_key and not editable">
          <script>
            fbq('track', 'Purchase', {
              value: <t t-esc="website_sale_order.amount_total"/>,
              currency: '<t t-esc="website_sale_order.pricelist_id.currency_id.name"/>',
              'products': [
                <t t-foreach="website_sale_order.order_line" t-as="line">
                  {
                    'id': '<t t-if="line.product_id.default_code" t-esc="line.product_id.default_code"/><t t-else="" t-esc="line.product_id.id" />',
                    'quantity': '<t t-esc="line.product_uom_qty"/>',
                    'item_price': '<t t-esc="line.price_unit"/>',
                  },
                </t>
              ]
            });
          </script>
        </t>
      </xpath>
    </template>
</odoo>
