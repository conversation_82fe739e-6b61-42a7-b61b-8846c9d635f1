# -*- coding: utf-8 -*-
{
    'name': 'Advanced Appointment Manager',
    'version': '********.0',
    'category': 'Website',
    'sequence': 5,
    'summary': 'A complete appointment workflow with website booking, approval, and check-in management3.',
    'description': """
This module provides a comprehensive appointment management system by combining a user-friendly website booking process with a powerful backend workflow.
""",
    'author': 'Zoe AI & Co-creator',
    'depends': [
        'base',
        'portal',
        'calendar',
        'mail',
        'appointment',
        'website_appointment'
    ],
    'data': [
        'security/ir.model.access.csv',
        'security/appointment_security.xml',
        'data/email_templates.xml',
        'data/appointment_cron.xml',
        'views/appointment_settings_views.xml',
        'views/appointment_views.xml',
        'views/website_templates.xml',
        'views/portal_templates.xml',
    ],
    'installable': True,
    'application': True,
    'license': 'LGPL-3',
}