<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) 2016-Present Webkul Software Pvt. Ltd. (<https://webkul.com/>) -->
<!-- See LICENSE file for full copyright and licensing details. -->
<!-- License URL : https://store.webkul.com/license.html/ -->

<odoo>
    <data>
        <record id="aliexpress_product_product_tree_view" model="ir.ui.view">
            <field name="name">aliexpress.product.product.tree</field>
            <field name="model">product.product</field>
            <field name="priority" eval="20"/>
            <field name="arch" type="xml">
                <tree string="Product Variants">
                    <field name="default_code"/>
                    <field name="name"/>
                    <field name="product_template_attribute_value_ids" widget="many2many_tags" groups="product.group_product_variant"/>
                    <field name="lst_price"/>
                    <!-- <field name="price" invisible="not context.get('pricelist',False)"/> -->
                    <field name="is_drop_ship_product" column_invisible="1"/>
                    <field name="aliexpress_qty" invisible="is_drop_ship_product == False"/>
                    <field name="uom_id" options="{'no_open': True, 'no_create': True}" groups="uom.group_uom"/>
                    <field name="barcode"/>
                    <field name="product_tmpl_id" column_invisible="1"/>

                    <field name="active" column_invisible="1"/>
                </tree>
            </field>
        </record>

        <record id="aliexpress_product_product_kanban_view" model="ir.ui.view">
            <field name="name">Aliexpress Product Kanban</field>
            <field name="model">product.product</field>
            <field name="inherit_id" ref="product.product_kanban_view"/>
            <field name="arch" type="xml">
                <field name="lst_price" position="after">
                    <field name="is_drop_ship_product"/>
                    <field name="aliexpress_qty"/>
                    <span t-if="record.is_drop_ship_product.raw_value">Quantity: <field name="aliexpress_qty"></field></span>
                </field>
            </field>
        </record>

        <record id="aliexpress_product_product_action" model="ir.actions.act_window">
            <field name="name">Product Variants</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">product.product</field>
            <field name="view_mode">kanban,tree,form</field>
            <!-- <field name="context">{"search_default_drop_shipping":1}</field> -->
            <field name="domain">[('is_drop_ship_product','=', True)]</field>
            <field name="search_view_id" ref="product.product_search_form_view"/>
            <field
				name="view_ids"
				eval="[
				(5, 0, 0),
				(0, 0, {'sequence': 1, 'view_mode': 'tree', 'view_id': ref('aliexpress_product_product_tree_view')}),
                (0, 0, {'sequence': 2, 'view_mode': 'form', 'view_id': ref('product.product_normal_form_view')}),
				(0, 0, {'sequence': 3, 'view_mode': 'kanban', 'view_id': ref('product.product_kanban_view')})
			]"/>
            <field name="help" type="html">
              <p class="oe_view_nocontent_create">
                Click to define a new product.
              </p><p>
                You must define a product for everything you sell, whether it's
                a physical product, a consumable or a service you offer to
                customers.
              </p><p>
                The product form contains information to simplify the sale
                process: price, notes in the quotation, accounting data,
                procurement methods, etc.
              </p>
            </field>
        </record>

        <record id="aliexpress_product_template_search_view" model="ir.ui.view">
            <field name="name">aliexpress.product.template.search</field>
            <field name="model">product.template</field>
            <field name="inherit_id" ref="product.product_template_search_view"/>
            <field name="arch" type="xml">
                <filter name="inactive" position="before">
                    <filter string="Drop Shipping" name="drop_shipping" domain="[('is_drop_ship_product','=', True)]"/>
                    <separator/>
                </filter>
            </field>
        </record>

        <record id="aliexpress_product_template_tree_view" model="ir.ui.view">
            <field name="name">aliexpress.product.template.product.tree</field>
            <field name="model">product.template</field>
            <field name="priority" eval="20"/>
            <field name="arch" type="xml">
                <tree string="Product">
                    <field name="sequence" widget="handle"/>
                    <field name="default_code"/>
                    <field name="name"/>
                    <field name="list_price"/>
                    <field name="categ_id"/>
                    <field name="type"/>
                    <field name="is_drop_ship_product" column_invisible="1"/>
                    <field name="aliexpress_total_qty" invisible="is_drop_ship_product == False"/>
                    <field name="uom_id" options="{'no_open': True, 'no_create': True}" groups="uom.group_uom"/>

                    <field name="active" column_invisible="1"/>
                </tree>
            </field>
        </record>

        <record id="aliexpress_product_template_kanban_view" model="ir.ui.view">
            <field name="name">aliexpress.product.template.product.kanban</field>
            <field name="model">product.template</field>
            <field name="inherit_id" ref="stock.product_template_kanban_stock_view"/>
            <field name="arch" type="xml">
                <field name="list_price" position="after">
                    <field name="is_drop_ship_product"/>
                    <field name="aliexpress_total_qty"/>
                </field>
                <xpath expr="//div/field[@name='qty_available']/.." position="replace">
                    <li t-if="record.type.raw_value == 'product'">On hand:
                        <field t-if="!record.is_drop_ship_product.raw_value" name="qty_available"/>
                        <field t-if="record.is_drop_ship_product.raw_value" name="aliexpress_total_qty"/>
                        <field name="uom_id"/>
                    </li>
                </xpath>
            </field>
        </record>

        <record id="aliexpress_product_template_form_view" model="ir.ui.view">
            <field name="name">aliexpress.product.template.product.form</field>
            <field name="model">product.template</field>
            <field name="inherit_id" ref="product.product_template_form_view"/>
            <field name="arch" type="xml">
                <xpath expr="//div[@name='options']" position="inside">
                    <div>
                        <field name="is_drop_ship_product" readonly="1"/>
                        <label for="is_drop_ship_product"/>
                    </div>
                </xpath>
                <notebook position="after">
					<div style="border-left: 3px solid #eee;background-color: #fcf8f2;border-color: #f0ad4e;padding: 1px 5px 1px 5px;border-radius: 5px;" invisible="is_drop_ship_product == False">
						<h4>
							<i class="fa fa-info-circle" aria-hidden="true"></i>
							This product is imported from AliExpress.
                            <!-- <br/><br/><br/>
                            Aliexpress product link: <field name="ali_url" widget="link"/> -->
						</h4>
					</div>
				</notebook>
            </field>
        </record>

        <record id="drop_shipping_product_template_action" model="ir.actions.act_window">
            <field name="name">Products</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">product.template</field>
            <field name="view_mode">kanban,tree,form</field>
            <field name="view_id" ref="product.product_template_kanban_view"/>
            <field name="domain">[('is_drop_ship_product','=', True)]</field>
            <!-- <field name="context">{"search_default_drop_shipping":1}</field> -->
            <field
				name="view_ids"
				eval="[
				(5, 0, 0),
				(0, 0, {'sequence': 1, 'view_mode': 'tree', 'view_id': ref('aliexpress_product_template_tree_view')}),
                (0, 0, {'sequence': 2, 'view_mode': 'form', 'view_id': ref('product.product_template_only_form_view')}),
				(0, 0, {'sequence': 3, 'view_mode': 'kanban', 'view_id': ref('product.product_template_kanban_view')})
			]"/>
            <field name="help" type="html">
                <p class="oe_view_nocontent_create">
                    Click to define a new product.
                </p><p>
                    You must define a product for everything you sell, whether it's a physical product, a consumable or a service you offer to customers.
                </p><p>
                    The product form contains information to simplify the sale process: price, notes in the quotation, accounting data, procurement methods, etc.
                </p>
            </field>
        </record>

        <record id="aliexpress_product_attribute_tree_view" model="ir.ui.view">
            <field name="name">aliexpress.product.attribute.tree</field>
            <field name="model">product.attribute</field>
            <field name="inherit_id" ref="product.attribute_tree_view"/>
            <field name="arch" type="xml">
                <field name="create_variant" position="after">
                    <field name="comb_id"/>
                </field>
            </field>
        </record>

        <record id="aliexpress_product_attribute_form_view" model="ir.ui.view">
            <field name="name">aliexpress.product.attribute.form</field>
            <field name="model">product.attribute</field>
            <field name="inherit_id" ref="product.product_attribute_view_form"/>
            <field name="arch" type="xml">
                <field name="create_variant" position="after">
                    <field name="comb_id"/>
                </field>
            </field>
        </record>

        <record id="aliexpress_product_attribute_value_tree_view" model="ir.ui.view">
            <field name="name">aliexpress.product.attribute.value.tree</field>
            <field name="model">product.attribute.value</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="name"/>
                    <field name="attribute_id"/>
                    <field name="display_type"/>
                    <field name="is_custom"/>
                    <field name="comb_id"/>
                </tree>
            </field>
        </record>

        <record id="aliexpress_product_attribute_value_form_view" model="ir.ui.view">
            <field name="name">aliexpress.product.attribute.value.form</field>
            <field name="model">product.attribute.value</field>
            <field name="arch" type="xml">
                <form>
                    <sheet>
                        <group>
                            <group>
                                <field name="name" string="Name"/>
                                <field name="attribute_id"/>
                                <field name="display_type"/>
                            </group>
                            <group>
                                <field name="create_date" readonly="1"/>
                                <field name="is_custom"/>
                            </group>
                        </group>
                        <group>
                            <group string="Aliexpress Details">
                                <field name="comb_id" string="Combination ID"/>
                                <field name="ali_img_url_50" string="Image URL(50px)"/>
                                <field name="ali_img_url_640" string="Image URL(640px)"/>
                            </group>
                            <group>
                                <field name="ali_image" widget="image" class="oe_avatar" string="Image"/>
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="aliexpress_product_attribute_value_action" model="ir.actions.act_window">
            <field name="name">Attributes Values</field>
            <field name="res_model">product.attribute.value</field>
            <field name="view_mode">tree,form</field>
        </record>

    </data>
</odoo>
