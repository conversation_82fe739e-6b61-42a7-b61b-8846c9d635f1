<?xml version="1.0" encoding="utf-8"?>
<odoo>
  <data>
      <template id="appointment_pending_template" name="Appointment Pending">
        <t t-call="website.layout">
          <div id="wrap" class="oe_structure oe_empty">
            <style>
              .appointment-status-badge {
                background-color: #ffc107 !important;
                color: #212529 !important;
                font-weight: bold;
                padding: 8px 12px;
                border-radius: 6px;
                display: inline-block;
              }
              .appointment-details {
                background-color: #f8f9fa;
                border-radius: 8px;
                padding: 20px;
                margin-bottom: 20px;
              }
              .appointment-details strong {
                color: #495057;
                font-weight: 600;
              }
              .appointment-details span {
                color: #212529;
              }
            </style>
            <div class="container py-5">
                <div class="row">
                    <div class="col-lg-8 offset-lg-2 text-center">
                        <div class="alert alert-success" role="alert">
                            <i class="fa fa-check-circle fa-3x mb-3 text-success"></i>
                            <h1 class="mb-4">Thank You!</h1>
                        </div>

                        <div class="card shadow-sm">
                            <div class="card-body p-4">
                                <h3 class="text-muted mb-4">
                                    <t t-if="event">Your appointment request for <strong t-out="event.name"/> has been submitted.</t>
                                    <t t-else="">Your appointment request has been submitted.</t>
                                </h3>

                                <t t-if="event">
                                    <div class="appointment-details">
                                        <div class="row text-left mb-4">
                                            <div class="col-md-6">
                                                <strong>Appointment Details:</strong><br/>
                                                <span t-out="event.name"/><br/>
                                                <t t-if="event.start">
                                                    <i class="fa fa-calendar text-primary"></i> <span t-out="event.start" t-options="{'widget': 'datetime', 'format': 'MMMM d, yyyy'}"/><br/>
                                                    <i class="fa fa-clock-o text-primary"></i> <span t-out="event.start" t-options="{'widget': 'datetime', 'format': 'h:mm a'}"/>
                                                    <t t-if="event.stop"> - <span t-out="event.stop" t-options="{'widget': 'datetime', 'format': 'h:mm a'}"/></t>
                                                </t>
                                            </div>
                                            <div class="col-md-6">
                                                <strong>Status:</strong><br/>
                                                <span class="appointment-status-badge">Pending Approval</span><br/><br/>
                                                <t t-if="customer">
                                                    <strong>Contact:</strong><br/>
                                                    <span t-out="customer.name"/><br/>
                                                    <span t-out="customer.email"/>
                                                </t>
                                            </div>
                                        </div>
                                    </div>
                                </t>

                                <div class="alert alert-info">
                                    <h5><i class="fa fa-info-circle"></i> What happens next?</h5>
                                    <ul class="text-left mb-0">
                                        <li>Your appointment is now <strong>pending review</strong> by our team</li>
                                        <li>You will receive a <strong>confirmation email</strong> once your appointment is approved</li>
                                        <li>If you don't have a customer account yet, you'll receive an <strong>invitation email</strong> to set up your password</li>
                                        <li>You can check the status of your appointments anytime in your customer portal</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="mt-4">
                            <a href="/" class="btn btn-outline-secondary me-3">Return to Homepage</a>
                            <a href="/my/appointments" class="btn btn-primary">View My Appointments</a>
                        </div>
                    </div>
                </div>
            </div>
          </div>
        </t>
      </template>
  </data>
</odoo>