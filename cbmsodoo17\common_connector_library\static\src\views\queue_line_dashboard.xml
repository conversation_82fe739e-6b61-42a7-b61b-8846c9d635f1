<?xml version="1.0" encoding="UTF-8"?>
<templates>
    <t t-name="common_connector_library.QueueLineDashboard">
        <div class="o_queue_line_dashboard container-fluid py-4 border-bottom bg-white">
            <div class="row justify-content-between gap-3 gap-lg-0">
                <div class="col-12 col-lg-5 col-xl-5 col-xxl-5 flex-grow-1 flex-lg-grow-0 flex-shrink-0">
                    <div class="grid gap-4">
                        <div class="g-col-3 g-col-sm-2 d-flex align-items-center py-2 justify-content-center text-center">
                            <div class="text-center">All</div>
                        </div>
                        <div class="g-col-9 g-col-sm-10 grid gap-1">
                            <div class="g-col-4 p-0" title="Draft Queue Lines">
                                <a href="#" class="btn btn-primary w-100 h-100 border-0 rounded-0 text-capitalize fw-normal">
                                    <div class="fs-2">Draft</div>
                                </a>
                            </div>
                            <div class="g-col-4 p-0" title="Failed Queue Lines">
                                <a href="#" class="btn btn-primary w-100 h-100 border-0 rounded-0 text-capitalize fw-normal">
                                    <div class="fs-2">Failed</div>
                                </a>
                            </div>
                            <div class="g-col-4 p-0" title="Done Queue Lines">
                                <a href="#" class="btn btn-primary w-100 h-100 border-0 rounded-0 text-capitalize fw-normal">
                                    <div class="fs-2">Done</div>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="grid gap-4">
                        <div class="g-col-3 g-col-sm-2 d-flex align-items-center py-2 justify-content-center text-center">
                            <div class="text-center">Queue Lines</div>
                        </div>
                        <div class="g-col-9 g-col-sm-10 grid gap-2">
                            <div class="g-col-4 p-0" t-on-click="onActionClicked" title="Draft Queue Lines" filter_name="all_draft" context='{"action": "all_draft"}'>
                                <a href="#" class="btn btn-light d-flex align-items-center w-100 h-100 p-0 border-0 bg-100 fw-normal">
                                    <div class="w-100 p-2" t-out="values['all_draft'][0]"/>
                                </a>
                            </div>
                            <div class="g-col-4 p-0" t-on-click="onActionClicked" title="Failed Queue Lines" filter_name="all_failed" context='{"action": "all_failed"}'>
                                <a href="#" class="btn btn-light d-flex align-items-center w-100 h-100 p-0 border-0 bg-100 fw-normal">
                                    <div class="w-100 p-2" t-out="values['all_failed'][0]"/>
                                </a>
                            </div>
                            <div class="g-col-4 p-0" t-on-click="onActionClicked" title="Done Queue Lines" filter_name="all_done" context='{"action": "all_done"}'>
                                <a href="#" class="btn btn-light d-flex align-items-center w-100 h-100 p-0 border-0 bg-100 fw-normal">
                                    <div class="w-100 p-2" t-out="values['all_done'][0]"/>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-lg-7 col-xl-7 col-xxl-7 flex-shrink-0">
                    <div class="grid gap-4">
                        <div class="g-col-3 g-col-sm-2 d-flex align-items-center py-2 justify-content-end text-end justify-content-lg-start text-lg-start"/>
                        <div class="g-col-9 g-col-sm-10 grid gap-1">
                            <div class="g-col-3 p-0" title="Draft Queue Lines">
                                <a href="#" class="btn btn-primary w-100 h-100 border-0 rounded-0 text-capitalize fw-normal">
                                    <div class="fs-2">Created</div>
                                </a>
                            </div>
                            <div class="g-col-3 p-0" title="Draft Queue Lines">
                                <a href="#" class="btn btn-primary w-100 h-100 border-0 rounded-0 text-capitalize fw-normal">
                                    <div class="fs-2">Draft</div>
                                </a>
                            </div>
                            <div class="g-col-3 p-0" title="Failed Queue Lines">
                                <a href="#" class="btn btn-primary w-100 h-100 border-0 rounded-0 text-capitalize fw-normal">
                                    <div class="fs-2">Failed</div>
                                </a>
                            </div>
                            <div class="g-col-3 p-0" title="Done Queue Lines">
                                <a href="#" class="btn btn-primary w-100 h-100 border-0 rounded-0 text-capitalize fw-normal">
                                    <div class="fs-2">Done</div>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="grid gap-4">
                        <div class="g-col-3 g-col-sm-2 d-flex align-items-center py-2 justify-content-end text-end justify-content-lg-start text-lg-start">
                            <div>Today's Queue Lines</div>
                        </div>
                        <div class="g-col-9 g-col-sm-10 grid gap-2">
                            <div class="g-col-3 p-0" t-on-click="onActionClicked" title="Today's Created Queue Lines" filter_name="today" context='{"action": "today"}'>
                                <a href="#" class="btn btn-light d-flex align-items-center w-100 h-100 p-0 border-0 bg-100 fw-normal">
                                    <div class="w-100 p-2" t-out="values['today'][0]"/>
                                </a>
                            </div>
                            <div class="g-col-3 p-0" t-on-click="onActionClicked" title="Today's Draft Queue Lines" filter_name="today_draft" context='{"action": "today_draft"}'>
                                <a href="#" class="btn btn-light d-flex align-items-center w-100 h-100 p-0 border-0 bg-100 fw-normal">
                                    <div class="w-100 p-2" t-out="values['today_draft'][0]"/>
                                </a>
                            </div>
                            <div class="g-col-3 p-0" t-on-click="onActionClicked" title="Today's Failed Queue Lines" filter_name="today_failed" context='{"action": "today_failed"}'>
                                <a href="#" class="btn btn-light d-flex align-items-center w-100 h-100 p-0 border-0 bg-100 fw-normal">
                                    <div class="w-100 p-2" t-out="values['today_failed'][0]"/>
                                </a>
                            </div>
                            <div class="g-col-3 p-0" t-on-click="onActionClicked" title="Today's Done Queue Lines" filter_name="today_done" context='{"action": "today_done"}'>
                                <a href="#" class="btn btn-light d-flex align-items-center w-100 h-100 p-0 border-0 bg-100 fw-normal">
                                    <div class="w-100 p-2" t-out="values['today_done'][0]"/>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="grid gap-4">
                        <div class="g-col-3 g-col-sm-2 d-flex align-items-center py-2 justify-content-end text-end justify-content-lg-start text-lg-start">
                           <div>Yesterday's Queue Lines</div>
                        </div>
                        <div class="g-col-9 g-col-sm-10 grid gap-2">
                            <div class="g-col-3 p-0" t-on-click="onActionClicked" title="Yesterday's Created Queue Lines" filter_name="yesterday" context='{"action": "yesterday"}'>
                                <a href="#" class="btn btn-light d-flex align-items-center w-100 h-100 p-0 border-0 bg-100 fw-normal">
                                    <div class="w-100 p-2" t-out="values['yesterday'][0]"/>
                                </a>
                            </div>
                            <div class="g-col-3 p-0" t-on-click="onActionClicked" title="Yesterday's Draft Queue Lines" filter_name="yesterday_draft" context='{"action": "yesterday_draft"}'>
                                <a href="#" class="btn btn-light d-flex align-items-center w-100 h-100 p-0 border-0 bg-100 fw-normal">
                                    <div class="w-100 p-2" t-out="values['yesterday_draft'][0]"/>
                                </a>
                            </div>
                            <div class="g-col-3 p-0" t-on-click="onActionClicked" title="Yesterday's Failed Queue Lines" filter_name="yesterday_failed" context='{"action": "yesterday_failed"}'>
                                <a href="#" class="btn btn-light d-flex align-items-center w-100 h-100 p-0 border-0 bg-100 fw-normal">
                                    <div class="w-100 p-2" t-out="values['yesterday_failed'][0]"/>
                                </a>
                            </div>
                            <div class="g-col-3 p-0" t-on-click="onActionClicked" title="Yesterday's Done Queue Lines" filter_name="yesterday_done" context='{"action": "yesterday_done"}'>
                                <a href="#" class="btn btn-light d-flex align-items-center w-100 h-100 p-0 border-0 bg-100 fw-normal">
                                    <div class="w-100 p-2" t-out="values['yesterday_done'][0]"/>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </t>
</templates>
