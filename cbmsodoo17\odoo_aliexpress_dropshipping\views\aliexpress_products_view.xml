<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) 2016-Present Webkul Software Pvt. Ltd. (<https://webkul.com/>) -->
<!-- See LICENSE file for full copyright and licensing details. -->
<!-- License URL : https://store.webkul.com/license.html/ -->
<odoo>
    <data>

        <record id="aliexpress_product_tree_view" model="ir.ui.view">
            <field name="name">aliexpress.product.tree</field>
            <field name="model">aliexpress.product</field>
            <field name="arch" type="xml">
                <tree string="Product">
                    <field name="name"/>
                    <field name="ali_product_id"/>
                    <field name="create_date" string="Imported On"/>
                    <field name="state"/>
                    <button type="object" name="open_aliexpress_product_url" string="View" class="oe_highlight"/>
                </tree>
            </field>
        </record>

        <record id="aliexpress_product_form_view" model="ir.ui.view">
            <field name="name">aliexpress.product.form.view</field>
            <field name="model">aliexpress.product</field>
            <field name="arch" type="xml">
                <form>
                    <header>
						<field name="state" widget="statusbar" statusbar_visible="new,need_update,updated"/>
                        <button name="manual_fetch_and_update_feed_data_in_product" invisible="id != False and state not in ['new']" string="Create Now" type="object" class="btn-primary"/>
                        <button name="manual_fetch_and_update_feed_data_in_product" string="Update Now" invisible="state not in ['need_update','updated']" type="object" class="btn-primary"/>
                        <button name="product_need_to_be_updated" invisible="state in ['new','need_update']" string="Need To Update" type="object" class="btn"/>
                        <button name="open_aliexpress_product_url" string="View On Aliexpress" class="btn" type="object" invisible="product_url == False"/>
					</header>
                    <sheet>
                        <field name="id" invisible="True"/>
						<div class="oe_button_box" name="dropship_button_box">

						</div>
                        <div class="oe_title">
							<label class="oe_edit_only" for="name" string="Product Name"/>
							<h1><field name="name"/></h1>
						</div>
                        <group>
                            <group>
                                <field name="title" readonly="state == 'updated'"/>
                                <field name="description" readonly="state == 'updated'"/>
                            </group>
                        </group>
                        <group>
                            <field name="product_url" readonly="state == 'updated'" widget="url"/>
                        </group>
                        <notebook>
                            <page name="general_info" string="General">
                                <group>
                                    <group>
                                        <field name="price" readonly="state == 'updated'"/>
                                        <field name="ali_product_id" readonly="state == 'updated'"/>
                                        <field name="product_id" readonly="state == 'updated'" invisible="product_id == False"/>
                                    </group>
                                    <group>
                                        <field name="create_date" string="Imported On" readonly="1" />
                                        <field name="shipping_from" invisible="shipping_from == False" readonly="state == 'updated'"/>
                                        <field name="shipping_time" readonly="state == 'updated'"/>
                                    </group>
                                </group>
                            </page>
                            <page name="images" string="Images">
                                <field name="product_images"/>
                            </page>
                            <page name="aliexpress_feed_data" string="Feed Data">
                                <group>
                                    <field name="aliexpress_feed_data" readonly="1"/>
                                </group>
                                <group>
                                    <group>
                                        <field name="ali_feed_last_updated" readonly="1"/>
                                    </group>
                                </group>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="aliexpress_product_action_view" model="ir.actions.act_window">
            <field name="name">Aliexpress Products</field>
            <field name="res_model">aliexpress.product</field>
            <field name="view_mode">tree,form</field>
        </record>

        <record id="aliexpress_feed_need_to_update_server_action" model="ir.actions.server">
            <field name="name">Need To Update</field>
            <field name="type">ir.actions.server</field>
            <field name="state">code</field>
            <field name="model_id" ref="model_aliexpress_product"/>
            <field name="binding_model_id" ref="model_aliexpress_product"/>
            <field name="code">
                action = records.product_need_to_be_updated()
            </field>
        </record>

    </data>
</odoo>
