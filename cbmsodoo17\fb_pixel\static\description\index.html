<!-- Copyright (c) 2017-Present Webkul Software Pvt. Ltd. (<https://webkul.com/>) -->
<!-- See LICENSE file for full copyright and licensing details. -->
<!-- License URL : https://store.webkul.com/license.html/ -->
<head>
	<meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=yes">

	<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css" integrity="sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm" crossorigin="anonymous">

	<script src="https://ajax.googleapis.com/ajax/libs/jquery/2.1.1/jquery.min.js"></script>

	<script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js" integrity="sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl" crossorigin="anonymous"></script>

</head>

<section class="oe_container">
    <section class="oe_container" id="wk_header">
        <div class="mt32 mb32">
            <h2 class="text-center" style="color:#000">
                Facebook Pixel Integration
            </h2>
            <h5 class="mt8 mb8 text-center" style="font-weight:500; font-size:18px; color:#484848; font-family:'Montserrat', sans-serif">
                <i>Collect data and lead to good conversion via Facebook Pixel in Odoo!!</i>
            </h5>
        </div>
        <div class="row mt16 mb16 py-2 m-1" style="font-weight:500; font-size:18px; color:#484848; font-family:'Montserrat', sans-serif">
            <div class="container col-md-6  border-right" style="font-weight:500;line-height: 24px;font-size:16px;color:#484848;font-family:'Montserrat', sans-serif; vertical-align: middle; display: grid;">
                <p class="mb4 mt4 mr8">
                   Now, study the movement of visitors on your Odoo website via Facebook Pixel. It sends customer behaviour data from Odoo website to Facebook Pixel. This module lets you easily deploy various tracking events to collect useful data from Odoo website for analysis. 
                </p>
            </div>
            <div class="container col-md-6 ">
                <h4 class="ml-md-3">Information</h4>
                <div class="mt-2 ml-md-3" style="display:flex">
                    <span class="ml-1 mr-2 my-auto"><img src="screenshot/icon-user-guide.png" alt="user-guide"></span>
                    <div style="font-weight: 600;font-size: 14px;line-height: 10px;" class="mt-2">
                        User Guide
                    </div>
                </div>
                <div class="ml-md-3" style="display:flex;">
                    <span style="font-size:15px; margin-top:0.2rem">https://webkul.com/blog/odoo-facebook-pixel-integration</span>
                </div>
            </div>
        </div>
    </section>
    <section class="oe_container" style="font-family: 'Montserrat', sans-serif;font-style: normal;font-weight: 600;font-size: 16px;line-height: 26px;color: #747474;">
        <div class="card">
            <div class="card-body row">
                <div class="col-8">
                    <img src="icon-1.png" class="d-inline" alt="">
                    <p class="d-inline ml8" style="font-weight: 600;font-size: 18px;color:#333333;">What is Facebook Pixel?</p>
                    <p class="mt16" style="font-weight: 600; font-size: 16px; color:#646464;">Facebook Pixel is an analytical tool which collects data about various actions taken by the visitors on your Odoo website. It even lets you deploy various retargeting strategies to bring back the customer and convert potential leads.</p>
                    <div style="font-size: 16px; font-weight: 600;line-height: 27px;letter-spacing: 0px;text-align: left; color:#828282;">
                        <ul>
                            <li>Make sure that your ads are being shown to the right people.</li>
                            <li>Build advertising audiences.</li>
                            <li>Unlock additional Facebook advertising tools.</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-body">
                <img src="icon-2.png" class="d-inline" alt="">
                <p class="d-inline ml8" style="font-weight: 600;font-size: 18px;color:#333333;">Track your visitors footsteps via Facebook pixel Integration in Odoo!!</p>
                <div style="font-size: 16px; font-weight: 600;line-height: 27px;letter-spacing: 0px;text-align: left; color:#828282;">
                    <ul>
                        <li>The module allows you to send data related to customers' behaviour on your website.</li>
                        <li>The data can be used to study the actions of customers and take the best measures to increase conversions and bring in more leads.</li>
                        <li>Data for actions such as Pageviews, Searches, Add to cart, Initiate Checkout, Add Payment Info, Purchase and Content Viewed is directly sent to the Facebook Pixels.</li>
                        <li>The data is collected and sent to the Facebook Pixel Analytical tool. With this, you will be able to detect the places that need optimization for good conversion rates.</li>
                        <li>It even lets you target the right audience by advertising the right products and increasing your sales and profits.</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <section class="oe_container d-flex justify-content-center">
            <div style="width:90%; margin-top:30px">
                <div style="font-family:Montserrat; font-weight:600; font-size:24px; line-height:40px; color:#333333">
                    <p>Detailed Features List</p>
                    <p style="font-size:16px; font-weight:normal; color:#828282;">Below is the list of features of Facebook Pixel Integration</p>
                </div>
    
                <div class="row d-flex justify-content-center">
                    <div class="row" style="width:95%">
                        <div class="col-md-6 col-sm-12" style="margin-top:10px; margin-bottom:20px">
                            <div class="card oe-padded h-100 ml" style="min-height:100px">
                                <div class="card-body">
                                    <p class="card-title" style="font-family:Montserrat; font-weight:600; font-size:18px; line-height:25px; color:#333333">Configure the Facebook Pixel analytical tool with Odoo</p>
                                    <div class="card-text">
                                        <ul style="font-family:Montserrat; font-weight:normal; font-size:16px; line-height:25px; color:#555555">
                                            <li>The Odoo Facebook Pixel Integration module configures the Facebook Pixel analytical tool with Odoo website.</li>
                                            <li>Once configured, it sends customer behaviour data from Odoo website to Facebook Pixel.</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-sm-12" style="margin-top:10px; margin-bottom:20px">
                            <div class="card oe-padded h-100" style="min-height:100px">
                                <div class="card-body">
                                    <p class="card-title" style="font-family:Montserrat; font-weight:600; font-size:18px; line-height:25px; color:#333333">Track your customer's behaviour on Odoo with facebook pixel</p>
                                    <div class="card-text">
                                        <ul style="font-family:Montserrat; font-weight:normal; font-size:16px; line-height:25px; color:#555555">
                                            <li>This module lets you easily deploy various tracking events to collect useful data from Odoo website for analysis.</li>
                                            <li>Data related to events such as Search, Add to cart, Initiate checkout, Add payment info, Purchase and View content is sent to the Facebook Pixel.</li>
                                            <li>Implemented Enhanced Ecommerce features like Add to cart, Purchase, etc.</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <div>
            <!-- <h2>Screenshots</h2> -->
            <div class="feature-tab">
                <ul class="nav nav-tabs d-flex justify-content-center" id="myTab" role="tablist"
                    style="background-color: transparent;">
                    <li class="nav-item" style="color: rgb(255, 255, 255);background-color:#2335D7; border-left: 1.5px solid;">
                        <span class="nav-link active" id="tab-screenshot" data-bs-toggle="tab"
                            style="padding: 7px 20px;font-weight: 600;" href="#tab-screenshot-content"
                            role="tab" aria-controls="configuration" aria-selected="true">
                            Pixel Configuration
                        </span>
                    </li>
                    <li class="nav-item" style="color: rgb(255, 255, 255);background-color:#2335D7; border-left: 1.5px solid;">
                        <span class="nav-link " id="screenshot1-id" data-bs-toggle="tab"
                            style="padding: 7px 20px;font-weight: 600;" href="#screenshot1"
                            role="tab" aria-controls="configuration" aria-selected="true">
                            Events and Analytics
                        </span>
                    </li>
                    
                    <li class="nav-item" style="color: rgb(255, 255, 255);background-color:#2335D7; border-left: 1.5px solid;">
                        <span class="nav-link " id="screenshot2-id" data-bs-toggle="tab"
                            style="padding: 7px 20px;font-weight: 600;" href="#screenshot2"
                            role="tab" aria-controls="configuration" aria-selected="true">
                            Understanding event data
                        </span>
                    </li>
                </ul>
            </div>

             <div class="card-body tab-content p-0">
                <div class="tab-pane fade in show active text-center" id="tab-screenshot-content" role="tabpanel" aria-labelledby="tab-screenshot">
                    <div class="card p-2">
                        <div class="card-body p-3 text-left" style="font-weight: normal; font-size: 16px;">
                            <p>Setup an account in Facebook Pixel & get pixel id from the account. For more info <b><a href="https://www.facebook.com/business/help/***************" target="new"> Click Here</a></b>.</p>
                            <p>Simple configuration for back-end user which will be easily available from <b><i> Website > Configuration > Settings > Website > Facebook Pixel</i></b>.</p>
                            <p>Enable Facebook Pixel Setting & then fill pixel id.</p>
                        </div>
                        
                        <div class="card-body p-3 mb-2" style="box-shadow: 1px 1px 8px 0 rgba(0,0,0,0.16);">
                            <a class="oe_pic_ctr" href="screenshot/screenshot_002_1.jpg" target="_blank">
                                <img class="oe_picture" src="screenshot/screenshot_002_1.jpg" alt="screenshot" style="padding: 10px;">
                            </a>
                        </div>
                    
                        <div class="card-body p-3 mb-2" style="box-shadow: 1px 1px 8px 0 rgba(0,0,0,0.16);">
                            <a class="oe_pic_ctr" href="screenshot/screenshot_002_2.jpg" target="_blank">
                                <img class="oe_picture" src="screenshot/screenshot_002_2.jpg" alt="screenshot" style="padding: 10px;">
                            </a>
                        </div>
                    </div>
                </div>
                <div class="tab-pane fade text-center" id="screenshot1" role="tabpanel" aria-labelledby="screenshot1-id">
                    <div class="card p-2">
                        <div class="card-body p-3 text-left" style="font-weight: normal; font-size: 16px;">
                            <p>An event is any action on your website that can be tracked, such as when someone clicks a button or visits a page. With Facebook pixel, module adds the events that matter to odoo ecommerce, and give them categories (like Add to Cart or Purchase) that reflect a visitor's action.</p>
                            <p>Check <b><a href="https://www.facebook.com/business/help/***************" target="new"> Pixel standard events <i class="fa fa-external-link" aria-hidden="true"></i></a></b></p>
                            <ul>
                                <li>Search</li>
                                <li>Add to cart</li>
                                <li>Initiate checkout</li>
                                <li>Add payment info</li>
                                <li>Purchase</li>
                                <li>View content (ex: product page, landing page)</li>
                            </ul>
                        </div>

                        <div class="card-body p-3 mb-2" style="box-shadow: 1px 1px 8px 0 rgba(0,0,0,0.16);">
                            <a class="oe_pic_ctr" href="screenshot/screenshot_003.jpg" target="_blank">
                                <img class="oe_picture" src="screenshot/screenshot_003.jpg" alt="screenshot" style="padding: 10px;">
                            </a>
                        </div>

                        <div class="card-body p-3 text-left" style="font-weight: normal; font-size: 16px;">
                            <p>Now track and report website traffic in facebook analytics platform.</p>
                        </div>

                        <div class="card-body p-3 mb-2" style="box-shadow: 1px 1px 8px 0 rgba(0,0,0,0.16);">
                            <a class="oe_pic_ctr" href="screenshot/screenshot_004.jpg" target="_blank">
                                <img class="oe_picture" src="screenshot/screenshot_004.jpg" alt="screenshot" style="padding: 10px;">
                            </a>
                        </div>
                        
                    </div>
                </div>
                <div class="tab-pane fade text-center" id="screenshot2" role="tabpanel" aria-labelledby="screenshot1-id">
                    <div class="card p-2">
                        <div class="card-body p-3 text-left" style="font-weight: normal; font-size: 16px;">
                            <p>After you install this module you can see your pixel event data on the Pixel page. </p>
                            <p>On the Data Sources tab, you can measure and optimize your ad campaigns that are leveraging your pixel event data.</p>
                        </div>

                        <div class="card-body p-3 mb-2" style="box-shadow: 1px 1px 8px 0 rgba(0,0,0,0.16);">
                            <a class="oe_pic_ctr" href="screenshot/screenshot_005.jpg" target="_blank">
                                <img class="oe_picture" src="screenshot/screenshot_005.jpg" alt="screenshot" style="padding: 10px;">
                            </a>
                        </div>
                        
                        <div class="card-body p-3 mb-2" style="box-shadow: 1px 1px 8px 0 rgba(0,0,0,0.16);">
                            <a class="oe_pic_ctr" href="screenshot/screenshot_006.jpg" target="_blank">
                                <img class="oe_picture" src="screenshot/screenshot_006.jpg" alt="screenshot" style="padding: 10px;">
                            </a>
                        </div>
                        
                        <div class="card-body p-3 mb-2" style="box-shadow: 1px 1px 8px 0 rgba(0,0,0,0.16);">
                            <a class="oe_pic_ctr" href="screenshot/screenshot_007.jpg" target="_blank">
                                <img class="oe_picture" src="screenshot/screenshot_007.jpg" alt="screenshot" style="padding: 10px;">
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</section>

<section class="oe_container oe_dark">
    <h2 class="oe_slogan">Other Related Modules</h2>
    <div class="row">
        <div class="col-md-4">
            <a href='https://apps.odoo.com/apps/modules/12.0/piwik_website_analytics' target="new">
                <img class="oe_picture oe_screenshot img-rounded" src="https://apps.odoocdn.com/apps/assets/12.0/piwik_website_analytics/Banner.png"/>
            </a>
        </div>
        <div class="col-md-4">
            <a href='https://apps.odoo.com/apps/modules/14.0/odoo_google_tag_manager' target="new">
                <img class="oe_picture oe_screenshot img-rounded" src="https://apps.odoocdn.com/apps/assets/14.0/odoo_google_tag_manager/Banner.png"/>
            </a>
        </div>
        <div class="col-md-4">
            <a href='https://apps.odoo.com/apps/modules/14.0/google_rich_snippets' target="new">
                <img class="oe_picture oe_screenshot img-rounded" src="https://apps.odoocdn.com/apps/assets/14.0/google_rich_snippets/Banner.png"/>
            </a>
        </div>
    </div>
</section>

<section class="pt32" id="webkul_support">
    <div class="row">
        <div class="col-12">
            <h2 class="text-center">Help and Support</h2>
            <p class="mb-2 text-center" style="font-size:16px; color:#333; font-weight:400">Get Immediate support for any of your query</p>
        </div>
        <div class="col-12">
            <p class="text-center px-5" style="font-size:14px; color:#555; font-weight:normal">You will get 90 days free support for any doubt, queries, and bug fixing (excluding data recovery) or any type of issue related to this module.</p>
        </div>
        <div class="mx-auto col-lg-9 mb-4 oe_screenshot">
            <div class="row align-items-center justify-content-center mx-0 p-3">
                <div class="col-sm-2 text-center pr-0">
                    <img src="screenshot/mail.png" alt="mail" class="img img-fluid">
                </div>
                <div class="col-xl-7 col-sm-10">
                    <p class="my-2" style="color:#555; font-weight:bold">Write a mail to us:</p>
                    <b class="text-primary" style="font-size:18px"><EMAIL></b>
                    <p class="my-2" style="font-size:14px; color:#777; font-weight:normal">Any queries or want any extra features? Just drop a mail to our support.</p>
                </div>
                <div class="col-xl-3 offset-xl-0 float-xl-right col-sm-10 offset-sm-2 float-left mt16">
                    <a href="mailto:<EMAIL>" style="padding:10px 22px; background-color:#2335D7; font-size:14px; color:#fff"><i class="fa fa-pencil-square-o" style="color:white; margin-right:4px"></i>Write To US</a>
                </div>
            </div>
        </div>
        <div class="mx-auto col-lg-9 oe_screenshot">
            <div class="row align-items-center justify-content-center mx-0 p-3">
                <div class="col-sm-2 text-center pr-0">
                    <img src="screenshot/support-icon.png" alt="support-icon" class="img img-fluid">
                </div>
                <div class="col-sm-10 ">
                    <p class="my-2" style="font-weight:bold; color:#555">Get in touch with our Expert:</p>
                    <b class="text-primary text-break" style="font-size:18px">https://webkul.uvdesk.com/en/customer/create-ticket/</b>
                    <p class="my-2" style="font-weight:normal; font-size:14px; color:#777">Have any technical queries, want extra features, or anything else? Our team is here to answer all your questions. Just Raise A Support Ticket.</p>
                </div>
            </div>
        </div>
    </div>
</section>
