<?xml version="1.0" encoding="UTF-8"?>

<templates>
    <t t-name="ks_dn_advance.ks_list_view_layout_2">
        <t t-if="state.list_view_data">
            <table id="ksListViewTable" class="table table-dark ks_dn_table ks_list_view_layout_2"
                   t-att-data-model="state.list_view_data['model']">
                <thead class="ks_dn_thead">
                    <t t-call="ks_dn_advance.ks_dns_list_view_header_2"></t>
                </thead>
                <tbody class="ks_table_body">
                    <t t-call="ks_dashboard_ninja.ks_list_view_tmpl"></t>
                </tbody>
            </table>
        </t>
        <t t-else="">
            No Data Present
        </t>
    </t>
    <t t-name="ks_dn_advance.ks_dns_list_view_header_2">

        <t t-if="calculation_type == 'custom' &amp; !isDrill">
            <tr>
                <t t-set="count" t-value="0"/>
                <t t-foreach="state.list_view_data['label']" t-as="table_header" t-key="table_header_index">
                    <th class="ks_dn_th list_header ks_dn_asc" t-att-data-item-id="item_id" t-on-click="_ksSortAscOrder"
                        t-att-data-fields="state.list_view_data['fields'][count]"
                        t-att-data-store="state.list_view_data['store']">
                        <t t-esc="table_header"/>
                        <t t-if="calculation_type == 'custom' &amp; !isDrill">
                            <t t-if="state.list_view_data['store']">
                                <button title="Sort button"
                                        type="button"
                                        class="ks_sort_icon ks_dashboard_item_action ks_sort_down ks_list_heder_hover ks_border_aliceblue"
                                        t-att-data-item-id="item_id"
                                        t-att-data-fields="state.list_view_data['fields'][count]"
                                        aria-expanded="true">
                                    <i class="fa fa-angle-double-down"/>
                                </button>
                                <button title="Sort button"
                                        type="button"
                                        class="ks_dashboard_item_action ks_sort_up ks_plus ks_sort_icon ks_list_heder_hover ks_border_aliceblue"
                                        t-att-data-item-id="item_id"
                                        t-att-data-fields="state.list_view_data['fields'][count]"
                                        aria-expanded="true">
                                    <i class="fa fa-angle-double-up"/>
                                </button>
                            </t>
                            <t t-set="count" t-value="count+1"/>
                        </t>
                    </th>
                </t>
                <t t-if="calculation_type == 'custom'">
                    <th class="ks_dn_th"/>
                </t>
            </tr>
        </t>
        <t t-else="">
            <tr>

                <t t-foreach="state.list_view_data['label']" t-as="table_header" t-key="table_header_index">
                    <th class="ks_dn_th">
                        <t t-esc="table_header"/>
                    </th>
                </t>
                <t t-if="calculation_type == 'custom'">
                    <th class="ks_dn_th"/>
                </t>
            </tr>
        </t>
    </t>

    <t t-name="ks_dn_advance.ks_list_view_layout_3">
        <t t-if="state.list_view_data">
            <table id="ksListViewTable" class="table table-bordered ks_list_view_layout_3 ks_dn_table ks_table_layput_3"
                   t-att-data-model="state.list_view_data['model']">
                <thead class="ks_dn_thead">
                    <t t-call="ks_dn_advance.ks_dns_list_view_header_3"></t>
                </thead>
                <tbody class="ks_table_body">
                    <t t-call="ks_dashboard_ninja.ks_list_view_tmpl"></t>
                </tbody>
            </table>
        </t>
        <t t-else="">
            No Data Present
        </t>
    </t>
    <t t-name="ks_dn_advance.ks_dns_list_view_header_3">
        <t t-if="calculation_type == 'custom' &amp; !isDrill">
            <tr>
                <t t-set="count" t-value="0"/>
                <t t-foreach="state.list_view_data['label']" t-as="table_header" t-key="table_header_index">
                    <th class="ks_dn_th list_header ks_dn_asc" t-att-data-item-id="item_id" t-on-click="_ksSortAscOrder"
                        t-att-data-fields="state.list_view_data['fields'][count]"
                        t-att-data-store="state.list_view_data['store']">
                        <t t-esc="table_header"/>
                        <t t-if="calculation_type == 'custom' &amp; !isDrill">
                            <t t-if="state.list_view_data['store']">
                                <button title="Sort button"
                                        type="button"
                                        class="ks_sort_icon ks_dashboard_item_action ks_sort_down ks_list_heder_hover ks_border_aliceblue"
                                        t-att-data-item-id="item_id"
                                        t-att-data-fields="state.list_view_data['fields'][count]"
                                        aria-expanded="true">
                                    <i class="fa fa-angle-double-down"/>
                                </button>
                                <button title="Sort button"
                                        type="button"
                                        class="ks_dashboard_item_action ks_sort_up ks_plus ks_sort_icon ks_list_heder_hover ks_border_aliceblue"
                                        t-att-data-item-id="item_id"
                                        t-att-data-fields="state.list_view_data['fields'][count]"
                                        aria-expanded="true">
                                    <i class="fa fa-angle-double-up"/>
                                </button>
                            </t>
                            <t t-set="count" t-value="count+1"/>
                        </t>
                    </th>
                </t>
                <t t-if="calculation_type == 'custom'">
                    <th class="ks_dn_th"/>
                </t>
            </tr>
        </t>
        <t t-else="">
            <tr>

                <t t-foreach="state.list_view_data['label']" t-as="table_header" t-key="table_header_index">
                    <th class="ks_dn_th">
                        <t t-esc="table_header"/>
                    </th>
                </t>
                <t t-if="calculation_type == 'custom'">
                    <th class="ks_dn_th"/>
                </t>
            </tr>
        </t>
    </t>
    <t t-name="ks_dn_advance.ks_list_view_layout_4">
        <t t-if="state.list_view_data">
            <table id="ksListViewTable" class="table table-hover ks_dn_table ks_list_view_layout_4"
                   t-att-data-model="state.list_view_data['model']">
                <thead class="ks_dn_thead">
                    <t t-call="ks_dn_advance.ks_dns_list_view_header_4"></t>
                </thead>
                <tbody class="ks_table_body">
                    <t t-call="ks_dashboard_ninja.ks_list_view_tmpl"></t>
                </tbody>
            </table>
        </t>
        <t t-else="">
            No Data Present
        </t>
    </t>
    <t t-name="ks_dn_advance.ks_dns_list_view_header_4">
        <t t-if="calculation_type == 'custom' &amp; !isDrill">
            <tr>
                <t t-set="count" t-value="0"/>
                <t t-foreach="state.list_view_data['label']" t-as="table_header" t-key="table_header_index">
                    <th class="ks_dn_th list_header ks_dn_asc" t-att-data-item-id="item_id" t-on-click="_ksSortAscOrder"
                        t-att-data-fields="state.list_view_data['fields'][count]"
                        t-att-data-store="state.list_view_data['store']">
                        <t t-esc="table_header"/>
                        <t t-if="calculation_type == 'custom' &amp; !isDrill">
                            <t t-if="state.list_view_data['store']">
                                <button title="Sort button"
                                        type="button"
                                        class="ks_sort_icon ks_dashboard_item_action ks_sort_down ks_list_heder_hover ks_border_aliceblue"
                                        t-att-data-item-id="item_id"
                                        t-att-data-fields="state.list_view_data['fields'][count]"
                                        aria-expanded="true">
                                    <i class="fa fa-angle-double-down"/>
                                </button>
                                <button title="Sort button"
                                        type="button"
                                        class="ks_dashboard_item_action ks_sort_up ks_plus ks_sort_icon ks_list_heder_hover ks_border_aliceblue"
                                        t-att-data-item-id="item_id"
                                        t-att-data-fields="state.list_view_data['fields'][count]"
                                        aria-expanded="true">
                                    <i class="fa fa-angle-double-up"/>
                                </button>
                            </t>
                            <t t-set="count" t-value="count+1"/>
                        </t>
                    </th>
                </t>
                <t t-if="calculation_type == 'custom'">
                    <th class="ks_dn_th"/>
                </t>
            </tr>
        </t>
        <t t-else="">
            <tr>

                <t t-foreach="state.list_view_data['label']" t-as="table_header" t-key="table_header_index">
                    <th class="ks_dn_th">
                        <t t-esc="table_header"/>
                    </th>
                </t>
                <t t-if="calculation_type == 'custom'">
                    <th class="ks_dn_th"/>
                </t>
            </tr>
        </t>
    </t>

    <t t-name="ks_dn_advance.Ksdashboardlistview" t-inherit="ks_dashboard_ninja.Ksdashboardlistview" t-inherit-mode="extension">
        <xpath expr="//div[hasclass('card-body')]" position="replace">
            <t t-debug="pdb"/>
            <div class="card-body table-responsive">
                <t t-if="state.list_view_data">
                    <t t-if="layout == 'layout_2'">
                        <t t-call="ks_dn_advance.ks_list_view_layout_2"/>
                    </t>
                    <t t-elif="layout == 'layout_3'">
                        <t t-call="ks_dn_advance.ks_list_view_layout_3"/>
                    </t>
                    <t t-elif="layout == 'layout_4'">
                        <t t-call="ks_dn_advance.ks_list_view_layout_4"/>
                    </t>
                    <t t-else="">
                        <t t-call="ks_dashboard_ninja.ks_list_view_table"/>
                    </t>
                </t>
                <t t-else="">
                    No Data Present
                </t>
            </div>
        </xpath>
    </t>
    <t t-name="ks_dn_advance.ks_list_view_container" t-inherit="ks_dashboard_ninja.ks_list_view_container"
       t-inherit-mode="extension">
        <xpath expr="//div[hasclass('ksMaxTableContent')]" position="replace">
            <div class="card-body table-responsive ksMaxTableContent">
                <t t-if="state.list_view_data">
                    <t t-if="layout == 'layout_2'">
                        <t t-call="ks_dn_advance.ks_list_view_layout_2"/>
                    </t>
                    <t t-elif="layout == 'layout_3'">
                        <t t-call="ks_dn_advance.ks_list_view_layout_3"/>
                    </t>
                    <t t-elif="layout == 'layout_4'">
                        <t t-call="ks_dn_advance.ks_list_view_layout_4"/>
                    </t>
                    <t t-else="">
                        <t t-call="ks_dashboard_ninja.ks_list_view_table"/>
                    </t>
                </t>
                <t t-else="">
                    No Data Present
                </t>
            </div>
        </xpath>
    </t>
    <t t-inherit="ks_dashboard_ninja.ks_list_view_tmpl" t-inherit-mode="extension">
        <xpath expr="//tr[hasclass('ks_tr')]" position="replace">
            <tr class="ks_tr" t-att-data-record-id="table_row['id']" t-att-data-domain="table_row['domain']"
                t-att-data-item-Id="item_id"
                t-att-data-sequence="table_row['sequence']" t-att-data-last_seq="table_row['last_seq']">
                <t t-set="ks_rec_count" t-value="0"/>
                <t t-foreach="table_row['data']" t-as="row_data" t-key="row_data_index">
                    <t t-if="table_row['ks_column_type'][ks_rec_count]==='html'">
                        <td class="ks_list_canvas_click" t-on-click="onChartCanvasClick">
                            <!--                            <t t-out="row_data"/>-->
                            <t t-raw="row_data"/>
                        </td>
                        <t t-set="ks_rec_count" t-value="ks_rec_count+1"/>
                    </t>
                    <t t-else="">
                        <td class="ks_list_canvas_click" t-on-click="onChartCanvasClick">
                            <t t-esc="row_data" />
                        </td>
                        <t t-set="ks_rec_count" t-value="ks_rec_count+1"/>
                    </t>

                </t>
                <t t-if="calculation_type == 'custom'">
                    <td class="ks_info">
                        <t t-if="ks_show_records">
                        <i id="ks_item_info" t-att-data-model="state.list_view_data['model']"
                           t-att-data-list-type="state.list_view_data['list_view_type']"
                           t-att-data-groupby="state.list_view_data['groupby']"
                           t-att-data-record-id="table_row['id']" t-att-data-item-id="item_id"
                           t-att-data-list-view-type="list_type"
                           t-on-click="ksOnListItemInfoClick"
                           class="fa fa-pencil"/>
                        </t>
                    </td>
                </t>
            </tr>
        </xpath>
    </t>

    <t t-inherit="ks_dashboard_ninja.ks_list_view_table" t-inherit-mode="extension">
        <xpath expr="//table[hasclass('ks_list_view_layout_1')]" position="replace">

            <t t-if="state.list_view_data">

                <table id="ksListViewTable" class="table table-hover ks_dn_table ks_list_view_layout_1"
                       t-att-data-model="state.list_view_data['model']">

                    <thead class="ks_dn_thead">

                        <t t-call="ks_dn_advance.ks_dns_list_view_header"></t>

                    </thead>

                    <tbody class="ks_table_body">
                        <t t-call="ks_dashboard_ninja.ks_list_view_tmpl"/>
                    </tbody>
                </table>

            </t>
            <t t-else="">
                No Data Present
            </t>
        </xpath>
    </t>
    <t t-name="ks_dn_advance.ks_dns_list_view_header">
        <t t-if="calculation_type == 'custom' &amp; !isDrill">
            <tr style="color:black">
                <t t-set="count" t-value="0"/>
                <t t-foreach="state.list_view_data['label']" t-as="table_header" t-key="table_header_index">
                    <th class="ks_dn_th list_header ks_dn_asc" t-att-data-item-id="item_id" t-on-click="_ksSortAscOrder"
                        t-att-data-fields="state.list_view_data['fields'][count]"
                        t-att-data-store="state.list_view_data['store']">
                        <t t-esc="table_header"/>
                        <t t-if="calculation_type == 'custom' &amp; !isDrill">
                            <t t-if="state.list_view_data['store']">
                                <button title="Sort button"
                                        type="button"
                                        class="ks_sort_icon ks_dashboard_item_action ks_sort_down ks_list_heder_hover ks_border_aliceblue"
                                        t-att-data-item-id="item_id"
                                        t-att-data-fields="state.list_view_data['fields'][count]"
                                        aria-expanded="true">
                                    <i class="fa fa-angle-double-down"/>
                                </button>
                                <button title="Sort button"
                                        type="button"
                                        class="ks_dashboard_item_action ks_sort_up ks_plus ks_sort_icon ks_list_heder_hover ks_border_aliceblue"
                                        t-att-data-item-id="item_id"
                                        t-att-data-fields="state.list_view_data['fields'][count]"
                                        aria-expanded="true">
                                    <i class="fa fa-angle-double-up"/>
                                </button>
                            </t>
                            <t t-set="count" t-value="count+1"/>
                        </t>
                    </th>
                </t>
                <t t-if="calculation_type == 'custom'">
                    <th class="ks_dn_th"/>
                </t>
            </tr>
        </t>
        <t t-else="">
            <tr style="color:black">

                <t t-foreach="state.list_view_data['label']" t-as="table_header" t-key="table_header_index">
                    <th class="ks_dn_th">
                        <t t-esc="table_header"/>
                    </th>
                </t>
                <t t-if="calculation_type == 'custom'">
                    <th class="ks_dn_th"/>
                </t>
            </tr>
        </t>

    </t>
</templates>