<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) 2016-Present Webkul Software Pvt. Ltd. (<https://webkul.com/>) -->
<!-- See LICENSE file for full copyright and licensing details. -->
<!-- License URL : https://store.webkul.com/license.html/ -->

<odoo>
    <data noupdate="1">

        <function model="ir.default" name="set" eval="('res.config.settings', 'ds_instant_product', True)"/>
        <function model="ir.default" name="set" eval="('res.config.settings', 'ds_fetch_feature', True)"/>
        <function model="ir.default" name="set" eval="('res.config.settings', 'ds_fetch_description', True)"/>
        <function model="ir.default" name="set" eval="('res.config.settings', 'ds_fetch_packaging', True)"/>
        <function model="ir.default" name="set" eval="('res.config.settings', 'ds_display_shipping_time', True)"/>
        <function model="ir.default" name="set" eval="('res.config.settings', 'ds_auto_published', True)"/>
        <function model="ir.default" name="set" eval="('res.config.settings', 'ds_price_options', 'same')"/>

    </data>
</odoo>
