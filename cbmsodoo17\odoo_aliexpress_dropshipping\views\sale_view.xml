<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) 2016-Present Webkul Software Pvt. Ltd. (<https://webkul.com/>) -->
<!-- See LICENSE file for full copyright and licensing details. -->
<!-- License URL : https://store.webkul.com/license.html/ -->

<odoo>
    <data>

        <record id="aliexpress_sale_order_tree_view" model="ir.ui.view">
            <field name="name">Sale Orders</field>
            <field name="model">sale.order</field>
            <field name="priority" eval="20"/>
            <field name="arch" type="xml">
                <tree string="Sales Orders" decoration-bf="message_needaction==True" decoration-muted="state=='cancel'">
                    <field name="message_needaction" column_invisible="1"/>
                    <field name="currency_id" column_invisible="1"/>
                    <field name="state" column_invisible="1"/>
                    <field name="name" string="Order Number"/>
                    <field name="date_order"/>
                    <field name="partner_id"/>
                    <field name="user_id"/>
                    <field name="amount_total" sum="Total Tax Included" widget="monetary"/>
                    <field name="invoice_status"/>
                    <button type="object" name="create_sale_order_on_aliexpress" string="Place Order" class="oe_highlight"/>
                </tree>
            </field>
        </record>

        <record id="aliexpress_sale_order_search_view_ecommerce" model="ir.ui.view">
            <field name="name">aliexpress.sale.order.ecommerce.search</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="website_sale.view_sales_order_filter_ecommerce"/>
            <field name="arch" type="xml">
                <filter name="order_abandoned" position="after">
                    <separator/>
                    <filter string="Drop Shipping" name="drop_shipping" domain="[('is_dropshipping_order','=', True)]"/>
                </filter>
            </field>
        </record>

        <record id="aliexpress_sale_order_form_view" model="ir.ui.view">
            <field name="name">aliexpress.sale.order.form</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale.view_order_form"/>
            <field name="arch" type="xml">
                <field name="currency_id" position="after">
                    <field name="is_dropshipping_order" invisible="1"/>
                </field>
                <xpath expr="//header" position="inside">
                    <button string="Place Order" class="oe_highlight" name="create_sale_order_on_aliexpress" type="object" invisible="state != 'sale' or is_dropshipping_order != True" />
                </xpath>
            </field>
        </record>

        <record id="aliexpress_sale_order_action" model="ir.actions.act_window">
            <field name="name">Sales Orders</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">sale.order</field>
            <field name="view_mode">tree,kanban,form,calendar,pivot,graph</field>
            <field name="search_view_id" ref="sale.sale_order_view_search_inherit_sale"/>
            <!-- <field name="context">{"search_default_drop_shipping":1}</field> -->
            <field name="domain">[('state', 'not in', ('draft', 'sent', 'cancel')),('is_dropshipping_order','=', True)]</field>
            <field name="help" type="html">
                <p class="oe_view_nocontent_create">
                    Create a Quotation, the first step of a new sale.
                </p><p>
                    Once the quotation is confirmed, it becomes a sales order.
                    You'll be able to invoice it and collect payments.
                    From the <i>Sales Orders</i> menu, you can track delivery
                    orders or services.
                </p>
            </field>
        </record>

        <record id="aliexpress_sale_order_tree_view_action" model="ir.actions.act_window.view">
            <field name="view_mode">tree</field>
            <field name="view_id" ref="aliexpress_sale_order_tree_view"/>
            <field name="act_window_id" ref="aliexpress_sale_order_action"/>
        </record>

    </data>
</odoo>
