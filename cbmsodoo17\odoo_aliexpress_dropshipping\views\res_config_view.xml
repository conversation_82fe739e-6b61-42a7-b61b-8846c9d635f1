<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) 2016-Present Webkul Software Pvt. Ltd. (<https://webkul.com/>) -->
<!-- See LICENSE file for full copyright and licensing details. -->
<!-- License URL : https://store.webkul.com/license.html/ -->

<odoo>
    <data>
        <record id="res_config_settings_view_form" model="ir.ui.view">
            <field name="name">aliexpress.drop.shipping.config.setting</field>
            <field name="model">res.config.settings</field>
            <field name="priority" eval="1"/>
            <field name="inherit_id" ref="base.res_config_settings_view_form" />
            <field name="arch" type="xml">
                <xpath expr="//form" position="inside">
                  <app data-string = "AliExpress Dropshipping" string = "AliExpress Dropshipping" name ="odoo_aliexpress_dropshipping" groups="odoo_aliexpress_dropshipping.odoo_aliexpress_dropshipping_group,base.group_system">
                    <!-- <div class="app_settings_block" string="AliExpress Dropshipping" data-string="AliExpress Dropshipping" data-key="odoo_aliexpress_dropshipping" groups="odoo_aliexpress_dropshipping.odoo_aliexpress_dropshipping_group,base.group_system"> -->
                        <h2>Product Settings</h2>
                        <div class="row mt16 o_settings_container" id="ds_instant_settings">
                            <div class="col-12 o_setting_box" title="Auto Import New Product From AliExpress Feed.">
                                <div class="o_setting_left_pane">
                                    <field name="ds_instant_product" help="Auto Import New Product From AliExpress Feed."/>
                                </div>
                                <div class="o_setting_right_pane">
                                    <label for="ds_instant_product" string="Auto Import New Product From AliExpress Feed"/>
                                    <div class="alert-info" role="alert" style="padding: 5px;">
                                        By enabling this option product will get created instantly in odoo from aliexpress feed. But it'll slow the import process from aliexpress.<br/>
                                        <strong>Recommended Way</strong>:- If it's disable then you can activate the CRON jobs for fetching product's information(features, description, combination(s) and packaging) then it'll create the products in odoo. By this way you can import the products from aliexpress smoothly.
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mt16 o_settings_container" id="ds_web_page_settings">
                            <div class="col-12 col-lg-6 o_setting_box" title="Publish Product Instantly After Import.">
                                <div class="o_setting_left_pane">
                                    <field name="ds_auto_published" help="Publish Product Instantly After Import."/>
                                </div>
                                <div class="o_setting_right_pane">
                                    <label for="ds_auto_published" string="Publish Product Instantly After Import"/>
                                    <div class="text-muted">
                                        ⇒ If it is enabled then product will get instantly published on odoo website after importing from aliexpress.
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-lg-6 o_setting_box d-none" title="If it is enabled then product features will be fetched while importing product from aliexpress to odoo store.">
                                <div class="o_setting_left_pane">
                                    <field name="ds_display_shipping_time" class="oe_inline" help="Display aliexpress product's shipping time on odoo website product page."/>
                                </div>
                                <div class="o_setting_right_pane">
                                    <label for="ds_display_shipping_time" string="Display Aliexpress Shipping Time"/>
                                    <div class="text-muted">
                                        ⇒ If it is enabled then aliexpress product's shipping time will be displayed on product page in odoo store.
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mt16 o_settings_container" id="ds_product_fetch_settings">
                            <div class="col-12 col-lg-6 o_setting_box" title="If it is enabled then product features will be fetched while importing product from aliexpress to odoo store.">
                                <div class="o_setting_left_pane">
                                    <field name="ds_fetch_feature" class="oe_inline" help="If it is enabled then product features will be fetched while importing product from aliexpress to odoo store."/>
                                </div>
                                <div class="o_setting_right_pane">
                                    <label for="ds_fetch_feature" string="Fetch Product Features"/>
                                    <div class="text-muted">
                                        ⇒ If it is enabled then product features will be fetched while importing product from aliexpress to odoo store.
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-lg-6 o_setting_box" title="If it is enabled then product description will be fetched while importing product from aliexpress to odoo store.">
                                <div class="o_setting_left_pane">
                                    <field name="ds_fetch_description" class="oe_inline" help="If it is enabled then product description will be fetched while importing product from aliexpress to odoo store."/>
                                </div>
                                <div class="o_setting_right_pane">
                                    <label for="ds_fetch_description" string="Fetch Product Description"/>
                                    <div class="text-muted">
                                        ⇒ If it is enabled then product description will be fetched while importing product from aliexpress to odoo store.
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-lg-6 o_setting_box d-none" title="If it is enabled then product packaging details will be fetched while importing product from aliexpress to odoo store.">
                                <div class="o_setting_left_pane">
                                    <field name="ds_fetch_packaging" class="oe_inline" help="If it is enabled then product packaging details will be fetched while importing product from aliexpress to odoo store."/>
                                </div>
                                <div class="o_setting_right_pane">
                                    <label for="ds_fetch_packaging" string="Fetch Product Packaging Details"/>
                                    <div class="text-muted">
                                        ⇒ If it is enabled then product packaging details will be fetched while importing product from aliexpress to odoo store.
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-lg-6 o_setting_box" title="If it is enabled then product images will be fetched while importing product from aliexpress to odoo store.">
                                <div class="o_setting_left_pane">
                                    <field name="ds_fetch_images" class="oe_inline" help="If it is enabled then product images will be fetched while importing product from aliexpress to odoo store."/>
                                </div>
                                <div class="o_setting_right_pane">
                                    <label for="ds_fetch_images" string="Fetch Product Images"/>
                                    <div class="text-muted">
                                        ⇒ If it is enabled then product images will be fetched while importing product from aliexpress to odoo store.
                                    </div>
                                </div>
                            </div>

                            <div class="col-12 col-lg-6 o_setting_box" title="Set minimum gap between data fetch from the Aliexpress website.">
                            	<div class="o_setting_left_pane"/>
                            	<div class="o_setting_right_pane">
                            		<label for="ds_fetch_time_delay" string="Time Delay"/>
                            		<div class="text-muted">
                            			Minimum gap between data fetch from the Aliexpress website.
                            		</div>
                            		<div class="text-muted">
                            			<field name="ds_fetch_time_delay" class="oe_inline" help="Set minimum gap between data fetch from the Aliexpress website."/> Seconds
                            		</div>
                            	</div>
                            </div>
                            <div class="col-12 col-lg-6 o_setting_box" title="Data will be fetched from AliExpress in Batch.">
                            	<div class="o_setting_left_pane"/>
                            	<div class="o_setting_right_pane">
                            		<label for="ds_fetch_batch_count" string="Batch Count"/>
                            		<div class="text-muted">
                            			Data will be fetched from AliExpress in Batch.
                            		</div>
                            		<div class="text-muted">
                            			<field name="ds_fetch_batch_count" class="oe_inline" help="Data will be fetched from AliExpress in Batch."/>
                            		</div>
                            	</div>
                            </div>


                        </div>
                        <h2>Product Price Settings</h2>
                        <div class="row mt16 o_settings_container" id="ds_product_price_settings">
                            <div class="col-12 col-lg-6 o_setting_box" title="Configure aliexpress product price in odoo store.">
                                <div class="o_setting_left_pane"/>
                                <div class="o_setting_right_pane">
                                    <label for="ds_price_options" string="Price"/>
                                    <div class="text-muted">
                                        Configure aliexpress product price in odoo store.
                                    </div>
                                    <div class="text-muted">
                                        <field name="ds_price_options" class="oe_inline" help="Configure aliexpress product price in odoo store."/>
                                    </div>
                                    <div class="text-muted">
                                        <field name="ds_custom_price" invisible="ds_price_options !='custom'"/>
                                        <field name="ds_price_perc" class="oe_inline" invisible="ds_price_options in ['same','custom']"/>
                                        <span invisible="ds_price_options in ['same','custom']"><b>%</b></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </app>
                </xpath>
            </field>
        </record>

        <record id="aliexpress_drop_shipping_config_settings_action" model="ir.actions.act_window">
            <field name="name">Configure Aliexpress Settings</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">res.config.settings</field>
            <field name="view_id" ref="res_config_settings_view_form"/>
            <field name="view_mode">form</field>
            <field name="target">inline</field>
            <field name="context">{'module' : 'odoo_aliexpress_dropshipping'}</field>
        </record>
    </data>
</odoo>
