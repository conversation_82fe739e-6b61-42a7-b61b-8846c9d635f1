# -*- coding: utf-8 -*-
from odoo import api, fields, models, _
import logging

_logger = logging.getLogger(__name__)


class AppointmentAuditLog(models.Model):
    _name = 'appointment.audit.log'
    _description = 'Appointment Audit Log'
    _order = 'create_date desc'
    _rec_name = 'action_description'

    appointment_id = fields.Many2one('calendar.event', string='Appointment', required=True, ondelete='cascade')
    action_type = fields.Selection([
        ('staff_change', 'Staff Change'),
        ('attendee_change', 'Attendee Change'),
        ('approval_change', 'Approval Status Change'),
        ('checkin_change', 'Check-in Status Change'),
        ('other', 'Other')
    ], string='Action Type', required=True)
    
    action_description = fields.Text('Action Description', required=True)
    
    # Staff change specific fields
    old_user_id = fields.Many2one('res.users', string='Previous Organizer')
    new_user_id = fields.Many2one('res.users', string='New Organizer')
    
    # Attendee change specific fields
    old_attendee_ids = fields.Many2many('res.partner', 'audit_old_attendee_rel', string='Previous Attendees')
    new_attendee_ids = fields.Many2many('res.partner', 'audit_new_attendee_rel', string='New Attendees')
    
    # Status change specific fields
    old_approval_state = fields.Selection([
        ("pending", "Pending"),
        ("approved", "Approved"),
        ("rejected", "Rejected"),
        ("cancelled", "Cancelled"),
    ], string="Previous Approval Status")
    
    new_approval_state = fields.Selection([
        ("pending", "Pending"),
        ("approved", "Approved"),
        ("rejected", "Rejected"),
        ("cancelled", "Cancelled"),
    ], string="New Approval Status")
    
    old_checkin_state = fields.Selection([
        ("waiting", "Waiting for Client"),
        ("in_progress", "In Progress"),
        ("completed", "Completed"),
        ("no_show", "No Show"),
        ("forfeited", "Forfeited"),
    ], string="Previous Check-in Status")
    
    new_checkin_state = fields.Selection([
        ("waiting", "Waiting for Client"),
        ("in_progress", "In Progress"),
        ("completed", "Completed"),
        ("no_show", "No Show"),
        ("forfeited", "Forfeited"),
    ], string="New Check-in Status")
    
    # Audit fields
    changed_by = fields.Many2one('res.users', string='Changed By', required=True, default=lambda self: self.env.user)
    change_reason = fields.Text('Reason for Change')
    ip_address = fields.Char('IP Address')
    user_agent = fields.Text('User Agent')
    
    @api.model
    def log_staff_change(self, appointment_id, old_user_id, new_user_id, reason=None):
        """Log a staff change event"""
        appointment = self.env['calendar.event'].browse(appointment_id)
        old_user = self.env['res.users'].browse(old_user_id) if old_user_id else None
        new_user = self.env['res.users'].browse(new_user_id) if new_user_id else None
        
        description = f"Staff changed from {old_user.name if old_user else 'None'} to {new_user.name if new_user else 'None'}"
        if reason:
            description += f" - Reason: {reason}"
        
        self.create({
            'appointment_id': appointment_id,
            'action_type': 'staff_change',
            'action_description': description,
            'old_user_id': old_user_id,
            'new_user_id': new_user_id,
            'change_reason': reason,
            'ip_address': self._get_client_ip(),
            'user_agent': self._get_user_agent(),
        })
        
        _logger.info(f"Audit log created for staff change in appointment {appointment_id}: {description}")
    
    @api.model
    def log_attendee_change(self, appointment_id, old_attendee_ids, new_attendee_ids, reason=None):
        """Log an attendee change event"""
        old_attendees = self.env['res.partner'].browse(old_attendee_ids) if old_attendee_ids else self.env['res.partner']
        new_attendees = self.env['res.partner'].browse(new_attendee_ids) if new_attendee_ids else self.env['res.partner']
        
        description = f"Attendees changed from [{', '.join(old_attendees.mapped('name'))}] to [{', '.join(new_attendees.mapped('name'))}]"
        if reason:
            description += f" - Reason: {reason}"
        
        self.create({
            'appointment_id': appointment_id,
            'action_type': 'attendee_change',
            'action_description': description,
            'old_attendee_ids': [(6, 0, old_attendee_ids)] if old_attendee_ids else False,
            'new_attendee_ids': [(6, 0, new_attendee_ids)] if new_attendee_ids else False,
            'change_reason': reason,
            'ip_address': self._get_client_ip(),
            'user_agent': self._get_user_agent(),
        })
        
        _logger.info(f"Audit log created for attendee change in appointment {appointment_id}: {description}")
    
    @api.model
    def log_approval_change(self, appointment_id, old_state, new_state, reason=None):
        """Log an approval status change event"""
        description = f"Approval status changed from {old_state} to {new_state}"
        if reason:
            description += f" - Reason: {reason}"
        
        self.create({
            'appointment_id': appointment_id,
            'action_type': 'approval_change',
            'action_description': description,
            'old_approval_state': old_state,
            'new_approval_state': new_state,
            'change_reason': reason,
            'ip_address': self._get_client_ip(),
            'user_agent': self._get_user_agent(),
        })
        
        _logger.info(f"Audit log created for approval change in appointment {appointment_id}: {description}")
    
    def _get_client_ip(self):
        """Get client IP address from request"""
        try:
            if hasattr(self.env, 'request') and self.env.request:
                return self.env.request.httprequest.environ.get('REMOTE_ADDR', 'Unknown')
        except:
            pass
        return 'Unknown'
    
    def _get_user_agent(self):
        """Get user agent from request"""
        try:
            if hasattr(self.env, 'request') and self.env.request:
                return self.env.request.httprequest.environ.get('HTTP_USER_AGENT', 'Unknown')
        except:
            pass
        return 'Unknown'
