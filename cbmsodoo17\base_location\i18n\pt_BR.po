# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * base_location
#
# Translators:
# <AUTHOR> <EMAIL>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-11-22 03:38+0000\n"
"PO-Revision-Date: 2023-10-31 14:38+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Portuguese (Brazil) (https://www.transifex.com/oca/teams/"
"23907/pt_BR/)\n"
"Language: pt_BR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: Weblate 4.17\n"

#. module: base_location
#: model:res.city,name:base_location.demo_brussels_city
msgid "Brussels"
msgstr "Bruxelas"

#. module: base_location
#: model:ir.model.fields,help:base_location.field_res_company__country_enforce_cities
msgid ""
"Check this box to ensure every address created in that country has a 'City' "
"chosen in the list of the country's cities."
msgstr ""
"Marque esta caixa para garantir que todos os endereços criados nesse país "
"tenham uma 'Cidade' escolhida na lista de cidades do país."

#. module: base_location
#: model:ir.actions.act_window,name:base_location.action_res_city_full
#: model:ir.ui.menu,name:base_location.locations_menu_cities
msgid "Cities"
msgstr "Cidades"

#. module: base_location
#: model:ir.model,name:base_location.model_res_city
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__city_id
#: model:ir.model.fields,field_description:base_location.field_res_partner__city
#: model:ir.model.fields,field_description:base_location.field_res_users__city
msgid "City"
msgstr "Cidade"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_company__city_id
#: model:ir.model.fields,field_description:base_location.field_res_partner__city_id
#: model:ir.model.fields,field_description:base_location.field_res_users__city_id
msgid "City ID"
msgstr "ID da Cidade"

#. module: base_location
#: model_terms:ir.ui.view,arch_db:base_location.view_company_form_city
#: model_terms:ir.ui.view,arch_db:base_location.view_partner_form
msgid "City completion"
msgstr "Autocompletar cidades"

#. module: base_location
#: model:ir.model,name:base_location.model_res_city_zip
msgid "City/locations completion object"
msgstr "Objeto autocompletar de Cidades/Locais"

#. module: base_location
#: model:ir.model,name:base_location.model_res_company
msgid "Companies"
msgstr "Empresas"

#. module: base_location
#: model:ir.model,name:base_location.model_res_partner
msgid "Contact"
msgstr "Contato"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__country_id
#: model:ir.model.fields,field_description:base_location.field_res_partner__country_id
#: model:ir.model.fields,field_description:base_location.field_res_users__country_id
#: model_terms:ir.ui.view,arch_db:base_location.view_country_search
msgid "Country"
msgstr "País"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__create_uid
msgid "Created by"
msgstr "Criado por"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__create_date
msgid "Created on"
msgstr "Criado em"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__display_name
msgid "Display Name"
msgstr "Nome a mostrar"

#. module: base_location
#: model_terms:ir.actions.act_window,help:base_location.action_res_city_full
msgid ""
"Display and manage the list of all cities that can be assigned to\n"
"            your partner records. Note that an option can be set on each "
"country\n"
"            separately\n"
"            to enforce any address of it to have a city in this list."
msgstr ""
"Exibir e gerenciar a lista de todas as cidades que podem ser atribuídas\n"
"             aos registros de seu parceiro. Observe que uma opção pode ser "
"definida em cada país\n"
"             separadamente\n"
"             para fazer com que qualquer endereço dele tenha uma cidade "
"nesta lista."

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_company__country_enforce_cities
msgid "Enforce Cities"
msgstr "Impor Cidades"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__id
msgid "ID"
msgstr "ID"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip____last_update
msgid "Last Modified on"
msgstr "Última atualização em"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__write_uid
msgid "Last Updated by"
msgstr "Útima atualização por"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__write_date
msgid "Last Updated on"
msgstr "Útima atualização em"

#. module: base_location
#: model_terms:ir.ui.view,arch_db:base_location.view_partner_form
msgid "Location completion"
msgstr "Auto completar as localizações"

#. module: base_location
#: model:ir.actions.act_window,name:base_location.action_zip_tree
msgid "Locations"
msgstr "Localizações"

#. module: base_location
#: model_terms:ir.ui.view,arch_db:base_location.view_city_zip_filter
msgid "Search zip"
msgstr "Buscar CEP"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__state_id
#: model:ir.model.fields,field_description:base_location.field_res_partner__state_id
#: model:ir.model.fields,field_description:base_location.field_res_users__state_id
msgid "State"
msgstr "Estado"

#. module: base_location
#. odoo-python
#: code:addons/base_location/models/res_partner.py:0
#, python-format
msgid ""
"The city of the partner %(partner)s differs from that in location "
"%(location)s"
msgstr ""
"A cidade do parceiro, %(partner)s, difere da sua localidade %(location)s"

#. module: base_location
#. odoo-python
#: code:addons/base_location/models/res_partner.py:0
#, python-format
msgid ""
"The country of the partner %(partner)s differs from that in location "
"%(location)s"
msgstr "O pais do parceiro, %(partner)s, difere da sua localidade %(location)s"

#. module: base_location
#. odoo-python
#: code:addons/base_location/models/res_partner.py:0
#, python-format
msgid ""
"The state of the partner %(partner)s differs from that in location "
"%(location)s"
msgstr ""
"O estado do parceiro, %(partner)s, difere da sua localidade %(location)s"

#. module: base_location
#. odoo-python
#: code:addons/base_location/models/res_partner.py:0
#, python-format
msgid ""
"The zip of the partner %(partner)s differs from that in location %(location)s"
msgstr "O CEP do parceiro %(partner)s difere daquele do local %(location)s"

#. module: base_location
#: model:ir.model.fields,help:base_location.field_res_company__zip_id
msgid "Use the city name or the zip code to search the location"
msgstr "Use o nome da cidade ou o CEP para pesquisar a localização"

#. module: base_location
#: model:ir.model.constraint,message:base_location.constraint_res_city_name_state_country_uniq
msgid ""
"You already have a city with that name in the same state.The city must have "
"a unique name within it's state and it's country"
msgstr ""
"Você já tem uma cidade com esse nome no mesmo estado. A cidade deve ter um "
"nome exclusivo dentro do estado e do país"

#. module: base_location
#: model:ir.model.constraint,message:base_location.constraint_res_city_zip_name_city_uniq
msgid ""
"You already have a zip with that code in the same city. The zip code must be "
"unique within it's city"
msgstr ""
"Você já tem um CEP com esse código na mesma cidade. O CEP deve ser único "
"dentro da cidade"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__name
msgid "ZIP"
msgstr "CEP"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_company__zip_id
#: model:ir.model.fields,field_description:base_location.field_res_partner__zip_id
#: model:ir.model.fields,field_description:base_location.field_res_users__zip_id
msgid "ZIP Location"
msgstr "Localização do CEP"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_partner__zip
#: model:ir.model.fields,field_description:base_location.field_res_users__zip
#: model_terms:ir.ui.view,arch_db:base_location.city_zip_form
msgid "Zip"
msgstr "Cep"

#. module: base_location
#: model:ir.ui.menu,name:base_location.locations_menu_zips
#: model_terms:ir.ui.view,arch_db:base_location.view_city_form
#: model_terms:ir.ui.view,arch_db:base_location.view_res_country_city_better_zip_form
msgid "Zips"
msgstr "Ceps"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city__zip_ids
msgid "Zips in this city"
msgstr "Ceps desta cidade"

#~ msgid "City of Address"
#~ msgstr "Cidade do Endereço"

#~ msgid ""
#~ "Display and manage the list of all cities that can be assigned to\n"
#~ "            your partner records. Note that an option can be set on each "
#~ "country separately\n"
#~ "            to enforce any address of it to have a city in this list."
#~ msgstr ""
#~ "Exiba e gerencie a lista de todas as cidades que podem ser atribuídas "
#~ "aos\n"
#~ "            seus parceiros. Observe que é possível configurar cada país "
#~ "para fazer\n"
#~ "             com que qualquer endereço dele tenha uma cidade nesta lista."

#~ msgid "Country state"
#~ msgstr "Estado"

#~ msgid "Group By"
#~ msgstr "Agrupar por"

#~ msgid "Latitude"
#~ msgstr "Latitude"

#~ msgid "Longitude"
#~ msgstr "Longitude"

#~ msgid "The official code for the city"
#~ msgstr "O código oficial da cidade"
