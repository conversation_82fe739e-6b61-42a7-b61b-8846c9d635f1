# -*- coding: utf-8 -*-
from odoo.tests.common import TransactionCase
from odoo.exceptions import UserError
from datetime import datetime, timedelta
import logging

_logger = logging.getLogger(__name__)


class TestStaffEditing(TransactionCase):
    
    def setUp(self):
        super().setUp()
        
        # Create test users
        self.admin_user = self.env.ref('base.user_admin')
        self.regular_user = self.env['res.users'].create({
            'name': 'Regular User',
            'login': 'regular_user',
            'email': '<EMAIL>',
            'groups_id': [(6, 0, [self.env.ref('base.group_user').id])]
        })
        self.staff_user_1 = self.env['res.users'].create({
            'name': 'Staff User 1',
            'login': 'staff1',
            'email': '<EMAIL>',
            'groups_id': [(6, 0, [self.env.ref('base.group_user').id])]
        })
        self.staff_user_2 = self.env['res.users'].create({
            'name': 'Staff User 2',
            'login': 'staff2',
            'email': '<EMAIL>',
            'groups_id': [(6, 0, [self.env.ref('base.group_user').id])]
        })
        
        # Create test partner
        self.test_partner = self.env['res.partner'].create({
            'name': 'Test Customer',
            'email': '<EMAIL>',
        })
        
        # Create test appointment
        self.test_appointment = self.env['calendar.event'].create({
            'name': 'Test Appointment',
            'start': datetime.now() + timedelta(days=1),
            'stop': datetime.now() + timedelta(days=1, hours=1),
            'user_id': self.staff_user_1.id,
            'partner_ids': [(6, 0, [self.test_partner.id])],
            'approval_state': 'pending',
        })
    
    def test_admin_can_change_staff_pending_appointment(self):
        """Test that admin can change staff for pending appointments"""
        # Switch to admin user
        self.test_appointment = self.test_appointment.with_user(self.admin_user)
        
        # Change staff
        old_user_id = self.test_appointment.user_id.id
        self.test_appointment.write({'user_id': self.staff_user_2.id})
        
        # Verify change
        self.assertEqual(self.test_appointment.user_id.id, self.staff_user_2.id)
        self.assertEqual(self.test_appointment.staff_changed_by.id, self.admin_user.id)
        self.assertTrue(self.test_appointment.staff_change_date)
        
        # Verify audit log was created
        audit_logs = self.env['appointment.audit.log'].search([
            ('appointment_id', '=', self.test_appointment.id),
            ('action_type', '=', 'staff_change')
        ])
        self.assertEqual(len(audit_logs), 1)
        self.assertEqual(audit_logs.old_user_id.id, old_user_id)
        self.assertEqual(audit_logs.new_user_id.id, self.staff_user_2.id)
    
    def test_regular_user_cannot_change_staff_pending_appointment(self):
        """Test that regular users cannot change staff for pending appointments"""
        # Switch to regular user
        self.test_appointment = self.test_appointment.with_user(self.regular_user)
        
        # Try to change staff - should raise error
        with self.assertRaises(UserError):
            self.test_appointment.write({'user_id': self.staff_user_2.id})
    
    def test_cannot_change_staff_approved_appointment(self):
        """Test that staff cannot be changed for approved appointments"""
        # Approve the appointment first
        self.test_appointment.action_approve()
        
        # Switch to admin user
        self.test_appointment = self.test_appointment.with_user(self.admin_user)
        
        # Try to change staff - should not trigger special validation since it's approved
        # The standard calendar.event validation should apply
        self.test_appointment.write({'user_id': self.staff_user_2.id})
        
        # Verify change went through (no special restriction for approved appointments)
        self.assertEqual(self.test_appointment.user_id.id, self.staff_user_2.id)
    
    def test_staff_availability_validation(self):
        """Test staff availability validation"""
        # Create conflicting appointment
        conflicting_appointment = self.env['calendar.event'].create({
            'name': 'Conflicting Appointment',
            'start': self.test_appointment.start,
            'stop': self.test_appointment.stop,
            'user_id': self.staff_user_2.id,
            'approval_state': 'approved',
        })
        
        # Switch to admin user
        self.test_appointment = self.test_appointment.with_user(self.admin_user)
        
        # Try to assign staff with conflict - should not raise error (just warning logged)
        # This is by design as conflicts might be intentional
        self.test_appointment.write({'user_id': self.staff_user_2.id})
        
        # Verify change went through
        self.assertEqual(self.test_appointment.user_id.id, self.staff_user_2.id)
    
    def test_attendee_synchronization(self):
        """Test that organizer is added to attendees when changed"""
        # Switch to admin user
        self.test_appointment = self.test_appointment.with_user(self.admin_user)
        
        # Change staff to user with partner
        self.test_appointment.write({'user_id': self.staff_user_2.id})
        
        # Verify organizer's partner is in attendees
        if self.staff_user_2.partner_id:
            self.assertIn(self.staff_user_2.partner_id, self.test_appointment.partner_ids)
    
    def test_approval_audit_logging(self):
        """Test that approval changes are logged"""
        # Approve appointment
        self.test_appointment.action_approve()
        
        # Verify audit log was created
        audit_logs = self.env['appointment.audit.log'].search([
            ('appointment_id', '=', self.test_appointment.id),
            ('action_type', '=', 'approval_change')
        ])
        self.assertEqual(len(audit_logs), 1)
        self.assertEqual(audit_logs.old_approval_state, 'pending')
        self.assertEqual(audit_logs.new_approval_state, 'approved')
    
    def test_rejection_audit_logging(self):
        """Test that rejection changes are logged"""
        # Reject appointment
        self.test_appointment.action_reject()
        
        # Verify audit log was created
        audit_logs = self.env['appointment.audit.log'].search([
            ('appointment_id', '=', self.test_appointment.id),
            ('action_type', '=', 'approval_change')
        ])
        self.assertEqual(len(audit_logs), 1)
        self.assertEqual(audit_logs.old_approval_state, 'pending')
        self.assertEqual(audit_logs.new_approval_state, 'rejected')
    
    def test_get_available_staff(self):
        """Test getting available staff for appointment time"""
        available_staff = self.test_appointment.get_available_staff()
        
        # Should include staff users who don't have conflicts
        self.assertIn(self.staff_user_2, available_staff)
        
        # Create conflicting appointment for staff_user_2
        self.env['calendar.event'].create({
            'name': 'Conflicting Appointment',
            'start': self.test_appointment.start,
            'stop': self.test_appointment.stop,
            'user_id': self.staff_user_2.id,
            'approval_state': 'approved',
        })
        
        # Check available staff again
        available_staff = self.test_appointment.get_available_staff()
        
        # staff_user_2 should not be in available staff due to conflict
        self.assertNotIn(self.staff_user_2, available_staff)
    
    def test_action_change_staff_method(self):
        """Test the helper method for changing staff"""
        # Switch to admin user
        self.test_appointment = self.test_appointment.with_user(self.admin_user)
        
        # Use helper method to change staff
        reason = "Original staff is unavailable"
        result = self.test_appointment.action_change_staff(self.staff_user_2.id, reason)
        
        # Verify change
        self.assertTrue(result)
        self.assertEqual(self.test_appointment.user_id.id, self.staff_user_2.id)
        self.assertIn(reason, self.test_appointment.staff_change_reason)
    
    def test_original_user_tracking(self):
        """Test that original user is tracked on creation"""
        # Create new appointment
        new_appointment = self.env['calendar.event'].create({
            'name': 'New Test Appointment',
            'start': datetime.now() + timedelta(days=2),
            'stop': datetime.now() + timedelta(days=2, hours=1),
            'user_id': self.staff_user_1.id,
            'partner_ids': [(6, 0, [self.test_partner.id])],
            'approval_state': 'pending',
        })
        
        # Verify original user is tracked
        self.assertEqual(new_appointment.original_user_id.id, self.staff_user_1.id)
    
    def test_check_staff_edit_permission(self):
        """Test staff edit permission checking"""
        # Admin should have permission for pending appointments
        self.test_appointment = self.test_appointment.with_user(self.admin_user)
        self.assertTrue(self.test_appointment._check_staff_edit_permission())
        
        # Regular user should not have permission
        self.test_appointment = self.test_appointment.with_user(self.regular_user)
        self.assertFalse(self.test_appointment._check_staff_edit_permission())
        
        # No one should have permission for approved appointments
        self.test_appointment.approval_state = 'approved'
        self.test_appointment = self.test_appointment.with_user(self.admin_user)
        self.assertFalse(self.test_appointment._check_staff_edit_permission())
