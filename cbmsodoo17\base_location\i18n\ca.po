# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * base_location
#
# Translators:
# <AUTHOR> <EMAIL>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-11-22 03:38+0000\n"
"PO-Revision-Date: 2024-09-04 17:06+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>."
"github.com>\n"
"Language-Team: Catalan (https://www.transifex.com/oca/teams/23907/ca/)\n"
"Language: ca\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 5.6.2\n"

#. module: base_location
#: model:res.city,name:base_location.demo_brussels_city
msgid "Brussels"
msgstr "Brussel·les"

#. module: base_location
#: model:ir.model.fields,help:base_location.field_res_company__country_enforce_cities
msgid ""
"Check this box to ensure every address created in that country has a 'City' "
"chosen in the list of the country's cities."
msgstr ""
"Marqueu aquesta casella per assegurar-vos que totes les adreces creades en "
"aquest país tinguin una \"Ciutat\" escollida a la llista de ciutats del país."

#. module: base_location
#: model:ir.actions.act_window,name:base_location.action_res_city_full
#: model:ir.ui.menu,name:base_location.locations_menu_cities
msgid "Cities"
msgstr "Ciutats"

#. module: base_location
#: model:ir.model,name:base_location.model_res_city
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__city_id
#: model:ir.model.fields,field_description:base_location.field_res_partner__city
#: model:ir.model.fields,field_description:base_location.field_res_users__city
msgid "City"
msgstr "Ciutat"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_company__city_id
#: model:ir.model.fields,field_description:base_location.field_res_partner__city_id
#: model:ir.model.fields,field_description:base_location.field_res_users__city_id
msgid "City ID"
msgstr "Identificació de la ciutat"

#. module: base_location
#: model_terms:ir.ui.view,arch_db:base_location.view_company_form_city
#: model_terms:ir.ui.view,arch_db:base_location.view_partner_form
msgid "City completion"
msgstr "Autocompletat de la població"

#. module: base_location
#: model:ir.model,name:base_location.model_res_city_zip
msgid "City/locations completion object"
msgstr "Autocompletat d'objectes a partir de ciutats/ubicacions"

#. module: base_location
#: model:ir.model,name:base_location.model_res_company
msgid "Companies"
msgstr "Empreses"

#. module: base_location
#: model:ir.model,name:base_location.model_res_partner
msgid "Contact"
msgstr "Contacte"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__country_id
#: model:ir.model.fields,field_description:base_location.field_res_partner__country_id
#: model:ir.model.fields,field_description:base_location.field_res_users__country_id
#: model_terms:ir.ui.view,arch_db:base_location.view_country_search
msgid "Country"
msgstr "País"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__create_uid
msgid "Created by"
msgstr "Creat per"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__create_date
msgid "Created on"
msgstr "Creat el"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__display_name
msgid "Display Name"
msgstr "Veure el nom"

#. module: base_location
#: model_terms:ir.actions.act_window,help:base_location.action_res_city_full
msgid ""
"Display and manage the list of all cities that can be assigned to\n"
"            your partner records. Note that an option can be set on each "
"country\n"
"            separately\n"
"            to enforce any address of it to have a city in this list."
msgstr ""
"Mostra i gestiona la llista de totes les ciutats a les quals es pot "
"assignar\n"
"             registres de la teva parella. Tingueu en compte que es pot "
"establir una opció a cada país\n"
"             per separat\n"
"             per fer complir qualsevol adreça d'aquesta per tenir una ciutat "
"en aquesta llista."

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_company__country_enforce_cities
msgid "Enforce Cities"
msgstr "Força les ciutats"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__id
msgid "ID"
msgstr "ID"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip____last_update
msgid "Last Modified on"
msgstr "Darrera modificació el"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__write_uid
msgid "Last Updated by"
msgstr "Darrera Actualització per"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__write_date
msgid "Last Updated on"
msgstr "Darrera Actualització el"

#. module: base_location
#: model_terms:ir.ui.view,arch_db:base_location.view_partner_form
msgid "Location completion"
msgstr "Autocompletat de la ubicació"

#. module: base_location
#: model:ir.actions.act_window,name:base_location.action_zip_tree
msgid "Locations"
msgstr "Ubicacions"

#. module: base_location
#: model_terms:ir.ui.view,arch_db:base_location.view_city_zip_filter
msgid "Search zip"
msgstr "Cerca codi postal"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__state_id
#: model:ir.model.fields,field_description:base_location.field_res_partner__state_id
#: model:ir.model.fields,field_description:base_location.field_res_users__state_id
msgid "State"
msgstr "Estat"

#. module: base_location
#. odoo-python
#: code:addons/base_location/models/res_partner.py:0
#, python-format
msgid ""
"The city of the partner %(partner)s differs from that in location "
"%(location)s"
msgstr ""
"La ciutat del soci %(partner)s és diferent de la de la ubicació %(location)s"

#. module: base_location
#. odoo-python
#: code:addons/base_location/models/res_partner.py:0
#, python-format
msgid ""
"The country of the partner %(partner)s differs from that in location "
"%(location)s"
msgstr ""
"El país del soci %(partner)s és diferent de la de la ubicació %(location)s"

#. module: base_location
#. odoo-python
#: code:addons/base_location/models/res_partner.py:0
#, python-format
msgid ""
"The state of the partner %(partner)s differs from that in location "
"%(location)s"
msgstr ""
"L'Estat del soci %(partner)s és diferent de la de la ubicació %(location)s"

#. module: base_location
#. odoo-python
#: code:addons/base_location/models/res_partner.py:0
#, python-format
msgid ""
"The zip of the partner %(partner)s differs from that in location %(location)s"
msgstr ""
"El codi postal del soci %(partner)s és diferent de la de la ubicació "
"%(location)s"

#. module: base_location
#: model:ir.model.fields,help:base_location.field_res_company__zip_id
msgid "Use the city name or the zip code to search the location"
msgstr "Utilitzeu el nom de la ciutat o el codi postal per cercar la ubicació"

#. module: base_location
#: model:ir.model.constraint,message:base_location.constraint_res_city_name_state_country_uniq
msgid ""
"You already have a city with that name in the same state.The city must have "
"a unique name within it's state and it's country"
msgstr ""
"Ja teniu una ciutat amb aquest nom al mateix estat. La ciutat ha de tenir un "
"nom únic dins del seu estat i del seu país"

#. module: base_location
#: model:ir.model.constraint,message:base_location.constraint_res_city_zip_name_city_uniq
msgid ""
"You already have a zip with that code in the same city. The zip code must be "
"unique within it's city"
msgstr ""
"Ja tens un codi postal amb aquest codi a la mateixa ciutat. El codi postal "
"ha de ser únic dins de la seva ciutat"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__name
msgid "ZIP"
msgstr "Codi postal"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_company__zip_id
#: model:ir.model.fields,field_description:base_location.field_res_partner__zip_id
#: model:ir.model.fields,field_description:base_location.field_res_users__zip_id
msgid "ZIP Location"
msgstr "Ubicació del codi postal"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_partner__zip
#: model:ir.model.fields,field_description:base_location.field_res_users__zip
#: model_terms:ir.ui.view,arch_db:base_location.city_zip_form
msgid "Zip"
msgstr "Codi postal"

#. module: base_location
#: model:ir.ui.menu,name:base_location.locations_menu_zips
#: model_terms:ir.ui.view,arch_db:base_location.view_city_form
#: model_terms:ir.ui.view,arch_db:base_location.view_res_country_city_better_zip_form
msgid "Zips"
msgstr "Codis postal"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city__zip_ids
msgid "Zips in this city"
msgstr "Codis postal en aquesta ciutat"

#~ msgid "City of Address"
#~ msgstr "Ciutat d'adreça"

#~ msgid "Group By"
#~ msgstr "Agrupa Per"
