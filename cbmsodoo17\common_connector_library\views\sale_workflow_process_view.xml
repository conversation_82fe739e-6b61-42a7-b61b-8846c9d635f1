<?xml version="1.0" encoding="utf-8"?>

<odoo>
    <!-- VIEW FOR THE OBJECT : sale_workflow_process -->
    <record id="sale_workflow_process_view_form" model="ir.ui.view">
        <field name="name">sale_automatic_workflow.sale_workflow_process.view_form</field>
        <field name="model">sale.workflow.process.ept</field>
        <field eval="16" name="priority"/>
        <field name="type">form</field>
        <field name="arch" type="xml">
            <form string="sale_workflow_process">
                <sheet>
                    <group>
                        <group>
                            <field name="name" required="1"/>
                        </group>
                        <group>
                            <field name="journal_id"
                                   invisible="not register_payment"
                                   required="register_payment"
                                   domain="[('type','in',['bank','cash'])]"/>
                            <field name="inbound_payment_method_id"
                                   invisible="not register_payment"
                                   required="register_payment"
                                   domain=""/>
                            <field name="sale_journal_id" required="1"/>
                        </group>
                    </group>
                    <group>
                        <group string="Workflow Option">
                            <field name="validate_order"/>
                            <field name="create_invoice" invisible="not validate_order"/>
                            <field name="register_payment" invisible="not create_invoice"/>
                            <field name="invoice_date_is_order_date"/>
                        </group>
                        <group string="Order Configuration">
                            <field name="picking_policy" required="1"/>
                        </group>
                        <p colspan="2" class="alert alert-warning" role="alert" invisible="not register_payment">
                            <div style="color:#ff0000; font-size:15px;">
                                <b>Payment Registration :</b>
                            </div>
                            <div class="d-inline-block w-100">
                                <div class="row">
                                    <div class="col-11 p-0">
                                        <ul>
                                            <li>
                                                <b>Enterprise Edition :</b>
                                                If 'Accounting'
                                                module is installed, the invoice will be in "in
                                                payment" state and
                                                to register the payment one must use the Bank
                                                Statement
                                                Reconciliation process.
                                            </li>
                                            <li>
                                                In all other cases it will be registered by default
                                                process.
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </p>
                    </group>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids" widget="mail_followers"
                           groups="base.group_user"/>
                    <field name="message_ids" widget="mail_thread"/>
                    <field name="activity_ids"/>
                </div>
            </form>
        </field>
    </record>

    <record id="sale_workflow_search_view_ept" model="ir.ui.view">
        <field name="name">Sale Workflow Search View</field>
        <field name="model">sale.workflow.process.ept</field>
        <field name="type">search</field>
        <field name="arch" type="xml">
            <search string="Sale Workflow">
                <field name="name"/>
            </search>
        </field>
    </record>

    <record id="sale_workflow_process_view_tree" model="ir.ui.view">
        <field name="name">sale_automatic_workflow.sale_workflow_process.view_tree</field>
        <field name="model">sale.workflow.process.ept</field>
        <field eval="16" name="priority"/>
        <field name="type">tree</field>
        <field name="arch" type="xml">
            <tree string="sale_workflow_process">
                <field name="name"/>
                <field name="validate_order"/>
                <field name="create_invoice"/>
                <field name="register_payment"/>
                <field name="invoice_date_is_order_date"/>
            </tree>
        </field>
    </record>

    <record id="act_sale_workflow_process_form_ept" model="ir.actions.act_window">
        <field name="name">Auto Sales Workflow</field>
        <field name="res_model">sale.workflow.process.ept</field>
        <field name="view_mode">tree,form</field>
    </record>

    <menuitem action="act_sale_workflow_process_form_ept"
              id="menu_act_sale_workflow_process_form_ept" parent="sale.menu_sale_config"
              sequence="100"/>
</odoo>

