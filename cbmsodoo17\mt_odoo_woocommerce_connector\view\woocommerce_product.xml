<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="product_template_tree_view_inherit" model="ir.ui.view">
        <field name="name">product.template.tree.inherit.woocomm</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_tree_view" />
        <field name="arch" type="xml">

            <xpath expr="//tree" position="attributes">
                <attribute name="js_class">woocomm_import_product_button</attribute>
            </xpath>

            <field name="standard_price" position="after">
                <field name="woocomm_instance_id" readonly="1" />
                <field name="wooc_id" readonly="1" />
                <field name="is_exported" readonly="1" />
            </field>
        </field>
    </record>

    <record id="product_template_kanban_view_inherit_woocomm" model="ir.ui.view">
        <field name="name">product.template.kanban.inherit.woocomm</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_kanban_view" />
        <field name="arch" type="xml">

            <xpath expr="//kanban" position="attributes">
                <attribute name="js_class">woocomm_import_product_k_button</attribute>
            </xpath>

        </field>
    </record>    

    <record id="view_product_template_search_inherit_woocomm" model="ir.ui.view">
        <field name="name">woocomm.product.template.search.view.inherit</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_search_view" />
        <field name="arch" type="xml">
            <search>
                <filter string="WooCommerce Synced Products" name="woocomm_imported_products"
                    domain="[('is_exported', '=', True)]" />
                <filter string="New products to Export" name="woocomm_to_export_products"
                    domain="[('is_exported', '=', False)]" />
                <group expand="1" string="Group By">
                    <filter string="Exported" name="export_status" context="{'group_by':'is_exported'}"/>
                    <filter string="Instance" name="instance_group" context="{'group_by':'woocomm_instance_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="product_template_only_form_view_inherit_woocomm" model="ir.ui.view">
        <field name="name">product.template.only.form.view.inherit.woocomm</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_only_form_view" />
        <field name="arch" type="xml">

            <xpath expr="//sheet/div[hasclass('oe_title')]/label" position="before">
                <field name="is_product_active" invisible="1" />
                <field name="woocomm_product_status"
                        invisible = "is_product_active == False"
                    class="bg-success text-white w-auto rounded-3 fs-5 px-3 d-table" readonly="1" />
                <field name="woocomm_product_status"
                        invisible = "is_product_active == True"
                    class="bg-500 text-white w-auto rounded-3 fs-5 px-3 d-table" readonly="1" />
            </xpath>
            <xpath expr="//field[@name='attribute_id']" position="attributes">
                <attribute name="domain">[('woocomm_instance_id','=',parent.woocomm_instance_id),('woocomm_instance_id','!=',False)]</attribute>
                <attribute name="help">Choose Attributes </attribute>
            </xpath> 

            <div name="button_box" position="inside">
                <button name="set_product_status" icon="fa-brands fa-cog" class="oe_stat_button"
                    type="object"
                    string="Enable Product" context="{'status': 'publish'}"
                    invisible = "is_exported == False or is_product_active == True"/>
                <button name="set_product_status" icon="fa-brands fa-cog" class="oe_stat_button"
                    type="object"
                    string="Dsiable Product" context="{'status': 'draft'}"
                    invisible = "is_exported == False or is_product_active == False"/>
            </div>
            <xpath expr="//notebook" position='inside'>
                <page string="WooCommerce Variants">
                    <group string="Variants Info">
                        <field name="woocomm_variant_ids" mode="kanban" nolabel="1" colspan="2"></field>
                    </group>
                </page>
                <page string="WooCommerce Images">
                    <group string="Images Info">
                        <field name="woocomm_image_ids" mode="kanban" nolabel="1" colspan="2"></field>
                    </group>
                </page>                
            </xpath>

            <xpath expr="//div[@name='pricing']" position="after">
                <field string="WooCommerce Sale Price" name="woocomm_sale_price" widget="monetary" />
                <field string="WooCommerce Regular Price" name="woocomm_regular_price" widget="monetary" />
                <field name="woocomm_tag_ids" widget="many2many_tags"
                    options="{'no_create_edit': True}" />
                <field name="is_exported" readonly="1" />
                <field name="wooc_id" string="WooCommerce Id" readonly="1" />
                <field name="woocomm_instance_id" readonly ="is_exported == True"/>
            </xpath>

        </field>
    </record>

    <record id="product_varient_woocomm_normal_form_view" model="ir.ui.view">
        <field name="name">product.varient.woocomm.normal.form.view</field>
        <field name="model">product.product</field>
        <field name="inherit_id" ref="product.product_normal_form_view" />
        <field name="arch" type="xml">

            <xpath expr="//field[@name='detailed_type']" position="before">
                <field name="woocomm_variant_id" string="WooCommerce Id" readonly="1" />
                <field name="woocomm_instance_id" readonly="1" />
                <field name="is_exported" readonly="1" />
            </xpath>

            <xpath expr="//div[@name='pricing']" position="after">
                <field name="woocomm_sale_price" widget="monetary" />
                <field string="WooCommerce Regular Price" name="woocomm_regular_price" widget="monetary" />
            </xpath>

            <xpath expr="//notebook/page[4]" position="after">
                <page string="WooCommerce Details">
                    <separator name="Description" string="Description" />
                    <group>
                        <group>
                            <field name="woocomm_varient_description" string="Description" />
                        </group>
                    </group>

                </page>
            </xpath>
        </field>
    </record>

    <!-- easy form -->
    <record id="product_varient_woocomm_easy_edit_view" model="ir.ui.view">
        <field name="name">product.varient.woocomm.easy.edit.view</field>
        <field name="model">product.product</field>
        <field name="inherit_id" ref="product.product_variant_easy_edit_view" />
        <field name="arch" type="xml">

            <xpath expr="//field[@name='default_code']" position="after">
                <field name="woocomm_variant_id" string="WooCommerce Id" readonly="1" />
                <field name="woocomm_instance_id" readonly="1" />
                <!-- <field name="woocomm_image_id" string="WooCommerce Image Id" readonly="1" /> -->
                <field name="is_exported" readonly="1" />
            </xpath>

            <xpath expr="//group[@name='pricing']" position="inside">
                <field name="woocomm_sale_price" widget="monetary" />
                <field string="WooCommerce Regular Price" name="woocomm_regular_price" widget="monetary" />
            </xpath>

            <xpath expr="//sheet" position="inside">
                <notebook>
                    <page string="WooCommerce Details">
                        <separator name="Description" string="Description" />
                        <group>
                            <group>
                                <field name="woocomm_varient_description" string="Description" />
                            </group>
                        </group>
                    </page>
                </notebook>
            </xpath>
        </field>
    </record>

    <record id="product_product_tree_view_inherit_woocomm" model="ir.ui.view">
        <field name="name">product.product.tree.inherit.woocomm</field>
        <field name="model">product.product</field>
        <field name="inherit_id" ref="product.product_product_tree_view" />
        <field name="arch" type="xml">

            <xpath expr="//tree" position="attributes">
                <attribute name="js_class">woocomm_import_product_button</attribute>
            </xpath>

            <field name="standard_price" position="before">
                <field name="woocomm_sale_price" />
            </field>
            <field name="standard_price" position="after">
                <field name="woocomm_instance_id" readonly="1" />
                <field name="woocomm_variant_id" readonly="1" />
                <field name="is_exported" readonly="1" />
            </field>
        </field>
    </record>

    <record id="action_product_template_tree_view_woocomm" model="ir.actions.act_window">
        <field name="name">Products</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">product.template</field>
        <field name="view_id" ref="product.product_template_tree_view" />
        <field name="context">{'search_default_woocomm_imported_products': 1}</field>
        <field name="view_mode">tree,form</field>
    </record>

    <!-- <record id="action_export_stock_woocomm" model="ir.actions.server">
        <field name="name">Update Stock In WooCommerce</field>
        <field name="model_id" ref="model_product_template"/>
        <field name="binding_model_id" ref="model_product_template"/>
        <field name="binding_view_types">form</field>
        <field name="state">code</field>
        <field name="code">
            if records:
                action = records.action_export_product_stock_to_woocomm()
        </field>
    </record> -->

</odoo>