# -*- coding: utf-8 -*-

import hashlib

from odoo import api, models, _, fields
from odoo.exceptions import UserError


class HrEmployee(models.Model):
    _inherit = 'hr.employee'

    point_of_sale_security = fields.<PERSON><PERSON>an("Point Of Sale Security")
    disable_set_discount = fields.<PERSON><PERSON><PERSON>("Disable Set Discount Button")
    disable_set_price = fields.<PERSON><PERSON><PERSON>("Disable Set Price Button")
    disable_remove_line = fields.<PERSON><PERSON><PERSON>("Disable Remove Line Button")
    disable_plus_minus = fields.<PERSON><PERSON><PERSON>("Disable +/- button")
    disable_set_payment = fields.<PERSON><PERSON>an("Disable Payment Button")
    disable_set_customer = fields.<PERSON><PERSON><PERSON>("Disable Set Customer Button")
    disable_remove_order = fields.<PERSON><PERSON><PERSON>("Disable Remove Order Button")
    disable_return_order = fields.<PERSON><PERSON>an("Disable Return Order")

    @api.onchange("point_of_sale_security")
    def _oc_point_of_sale_security(self):
        if not self.point_of_sale_security:
            self.disable_set_discount = self.disable_set_price = self.disable_remove_line = self.disable_plus_minus = self.disable_set_payment = self.disable_set_customer = self.disable_remove_order = self.disable_return_order = False
