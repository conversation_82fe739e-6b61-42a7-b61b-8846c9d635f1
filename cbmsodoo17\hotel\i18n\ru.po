# Translation of Odoo Server.
# This file contains the translation of the following modules:
#	* hotel
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-05-21 05:08+0000\n"
"PO-Revision-Date: 2020-05-21 05:08+0000\n"
"Last-Translator: <>\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__bom_count
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__bom_count
#: model:ir.model.fields,field_description:hotel.field_hotel_services__bom_count
msgid "# Bill of Material"
msgstr "Спецификация"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__used_in_bom_count
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__used_in_bom_count
#: model:ir.model.fields,field_description:hotel.field_hotel_services__used_in_bom_count
msgid "# BoM Where Used"
msgstr "# Где используется"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__product_variant_count
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__product_variant_count
#: model:ir.model.fields,field_description:hotel.field_hotel_services__product_variant_count
msgid "# Product Variants"
msgstr "Варианты продукции"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__product_count
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__product_count
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__product_count
msgid "# Products"
msgstr "# Продукты"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__description_sale
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__description_sale
#: model:ir.model.fields,help:hotel.field_hotel_services__description_sale
msgid "A description of the Product that you want to communicate to your customers. This description will be copied to every Sales Order, Delivery Order and Customer Invoice/Credit Note"
msgstr "Описание продукта, которое Вы хотели бы предоставлять вашим потребителям.Это описание будет отображаться в каждом заказе покупателя, заказе на доставку и счете покупателю/ кредитном обязательстве"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__type
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__type
#: model:ir.model.fields,help:hotel.field_hotel_services__type
msgid "A storable product is a product for which you manage stock. The Inventory app has to be installed.\n"
"A consumable product is a product for which stock is not managed.\n"
"A service is a non-material product you provide."
msgstr "Товар сохраняется - это товар, которым вы управляете на складе. Необходимо установить приложение Состав. Затратный товар - это товар, для которого состав не руководствуется. Услуга - это нематериальный товар, который вы предоставляете."

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__access_warning
msgid "Access warning"
msgstr "Предупреждение доступа"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio_line__qty_delivered_method
#: model:ir.model.fields,help:hotel.field_hotel_service_line__qty_delivered_method
msgid "According to product configuration, the delivered quantity can be automatically computed by mechanism :\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Analytic From expenses: the quantity is the quantity sum from posted expenses\n"
"  - Timesheet: the quantity is the sum of hours recorded on tasks linked to this sale line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
""
msgstr "Согласно настройки товара, доставлена количество может автоматически вычисляться с помощью механизма: - Вручную количество на строке устанавливается вручную - Аналитика по расходам: количество - сумма от затраченных расходов - Табель: количество - это сумма часов, записанных на задании, связанные с этой строкой продажи - Складские перемещения: количество поступает от подтвержденных комплектувань"

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_room_amenities_type_form
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_room_type_form
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_service_type_form
msgid "Account Properties"
msgstr "Параметры счёта"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__message_needaction
#: model:ir.model.fields,field_description:hotel.field_hotel_room__message_needaction
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__message_needaction
#: model:ir.model.fields,field_description:hotel.field_hotel_services__message_needaction
msgid "Action Needed"
msgstr "Требует внимания"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__active
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__active
#: model:ir.model.fields,field_description:hotel.field_hotel_services__active
msgid "Active"
msgstr "Активно"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__activity_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room__activity_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__activity_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__activity_ids
msgid "Activities"
msgstr "Деятельность"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__activity_state
#: model:ir.model.fields,field_description:hotel.field_hotel_room__activity_state
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__activity_state
#: model:ir.model.fields,field_description:hotel.field_hotel_services__activity_state
msgid "Activity State"
msgstr "Этап действия"

#. module: hotel
#: model:ir.ui.menu,name:hotel.menu_open_hotel_folio1_form_tree_view
msgid "All Folio"
msgstr "Все счета"

#. module: hotel
#: model:ir.ui.menu,name:hotel.menu_action_hotel_room_amenities_view_form
msgid "Amenities"
msgstr "Удобства"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__rcateg_id
msgid "Amenity Catagory"
msgstr "Категории Удобств"

#. module: hotel
#: model:ir.ui.menu,name:hotel.menu_action_hotel_room_amenities_type_view_form
msgid "Amenity Category"
msgstr "Категории Удобств"

#. module: hotel
#: model:ir.ui.menu,name:hotel.menu_parent_amenity
msgid "Amenity Defination"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__amenity_rate
msgid "Amenity Rate"
msgstr "Тариф На Услуги"

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_room_amenities_list
msgid "Amenity rate"
msgstr "Тариф На Услуги"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__amount_undiscounted
msgid "Amount Before Discount"
msgstr "Сумма без скидки"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__analytic_account_id
msgid "Analytic Account"
msgstr "Аналитический счёт"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__analytic_tag_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__analytic_tag_ids
msgid "Analytic Tags"
msgstr "Теги аналитики"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__analytic_line_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__analytic_line_ids
msgid "Analytic lines"
msgstr "Строки аналитики"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__message_attachment_count
#: model:ir.model.fields,field_description:hotel.field_hotel_room__message_attachment_count
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__message_attachment_count
#: model:ir.model.fields,field_description:hotel.field_hotel_services__message_attachment_count
msgid "Attachment Count"
msgstr "Количество вложений"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__attribute_value_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__attribute_value_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__attribute_value_ids
msgid "Attribute Values"
msgstr "Значение атрибута"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__authorized_transaction_ids
msgid "Authorized Transactions"
msgstr "Авторизованные операции"

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "Automatic Declaration"
msgstr "Автоматическое Объявление"

#. module: hotel
#: selection:product.product,state:0
msgid "Available"
msgstr "Доступный"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__available_in_pos
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__available_in_pos
#: model:ir.model.fields,field_description:hotel.field_hotel_services__available_in_pos
msgid "Available in POS"
msgstr "Доступен в ТП"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__produce_delay
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__produce_delay
#: model:ir.model.fields,help:hotel.field_hotel_services__produce_delay
msgid "Average lead time in days to manufacture this product. In the case of multi-level BOM, the manufacturing lead times of the components will be added."
msgstr "Среднее время в днях приведения в производство этого товара. В случае многоуровневой спецификации будет добавлено изготовлении компонентов."

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__variant_bom_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__variant_bom_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__variant_bom_ids
msgid "BOM Product Variants"
msgstr "Варианты спецификации товара"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__barcode
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__barcode
#: model:ir.model.fields,field_description:hotel.field_hotel_services__barcode
msgid "Barcode"
msgstr "Штрих-код"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__image
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__image
#: model:ir.model.fields,field_description:hotel.field_hotel_services__image
msgid "Big-sized image"
msgstr "Изображение большого размера"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__bom_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__bom_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__bom_ids
msgid "Bill of Materials"
msgstr "Ведомость материалов"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__bom_line_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__bom_line_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__bom_line_ids
msgid "BoM Components"
msgstr "Компоненты ВМ"

#. module: hotel
#: selection:product.product,state:0
msgid "Booked"
msgstr "Забронированы"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__product_updatable
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__product_updatable
msgid "Can Edit Product"
msgstr "Может редактировать товар"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__purchase_ok
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__purchase_ok
#: model:ir.model.fields,field_description:hotel.field_hotel_services__purchase_ok
msgid "Can be Purchased"
msgstr "Можно закупать"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__rental
#: model:ir.model.fields,field_description:hotel.field_hotel_services__rental
msgid "Can be Rent"
msgstr "Может быть арендован"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__sale_ok
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__sale_ok
#: model:ir.model.fields,field_description:hotel.field_hotel_services__sale_ok
msgid "Can be Sold"
msgstr "Можно продавать"

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "Cancel Folio"
msgstr "Отменить Счет"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__cat_id
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__ser_id
msgid "Category"
msgstr "Категория"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__route_from_categ_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__route_from_categ_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__route_from_categ_ids
msgid "Category Routes"
msgstr "Категории технологических карт"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__pos_categ_id
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__pos_categ_id
#: model:ir.model.fields,help:hotel.field_hotel_services__pos_categ_id
msgid "Category used in the Point of Sale."
msgstr "Категория используется в точке продажи."

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__checkin_date
msgid "Check In"
msgstr "Заезд"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__checkout_date
msgid "Check Out"
msgstr "Выезд"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__to_weight
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__to_weight
#: model:ir.model.fields,help:hotel.field_hotel_services__to_weight
msgid "Check if the product should be weighted using the hardware scale integration."
msgstr "Проверьте, следует взвешивать продукт с помощью устройства взвешивания."

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__available_in_pos
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__available_in_pos
#: model:ir.model.fields,help:hotel.field_hotel_services__available_in_pos
msgid "Check if you want this product to appear in the Point of Sale."
msgstr "Проверьте, хотите вы, чтобы этот товар появился в точке продажи."

#. module: hotel
#: sql_constraint:hotel_folio.line:0
msgid "Check in Date Should be lesser than the Check Out Date!"
msgstr "Дата заезда должна быть меньше даты выезда!"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__child_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__child_id
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__child_id
msgid "Child Categories"
msgstr "Подчиненные категории"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__color
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__color
#: model:ir.model.fields,field_description:hotel.field_hotel_services__color
msgid "Color Index"
msgstr "Цветовая палитра"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__commitment_date
msgid "Commitment Date"
msgstr "Дата Предоставления"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__company_id
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__company_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room__company_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__company_id
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__company_id
#: model:ir.model.fields,field_description:hotel.field_hotel_services__company_id
msgid "Company"
msgstr "Компания"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__complete_name
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__complete_name
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__complete_name
msgid "Complete Name"
msgstr "Полное название"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__effective_date
msgid "Completion date of the first delivery order."
msgstr "Дата завершения первого заказа на доставку."

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "Compute"
msgstr "Вычислять"

#. module: hotel
#: model:ir.ui.menu,name:hotel.hotel_configuration_menu
msgid "Configuration"
msgstr "Настройка"

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "Confirm Folio"
msgstr "Подтвердить Счет"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__confirmation_date
msgid "Confirmation Date"
msgstr "Дата подтверждения"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__standard_price
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__standard_price
#: model:ir.model.fields,field_description:hotel.field_hotel_services__standard_price
msgid "Cost"
msgstr "Стоимость"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__cost_currency_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__cost_currency_id
#: model:ir.model.fields,field_description:hotel.field_hotel_services__cost_currency_id
msgid "Cost Currency"
msgstr "Валюта"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__cost_method
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__cost_method
#: model:ir.model.fields,field_description:hotel.field_hotel_services__cost_method
msgid "Cost Method"
msgstr "Метод оплаты."

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__standard_price
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__standard_price
#: model:ir.model.fields,help:hotel.field_hotel_services__standard_price
msgid "Cost used for stock valuation in standard price and as a first price to set in average/fifo. Also used as a base price for pricelists. Expressed in the default unit of measure of the product."
msgstr "Стоимость используется для складской оценки стоимости по стандартной цене и как первая цена устанавливается в среднем / FIFO. Также используется как базовая цена для ПРАЙСЛИСТ. Выражается в единице измерения товара по умолчанию."

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__property_cost_method
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__property_cost_method
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__property_cost_method
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__property_cost_method
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__property_cost_method
#: model:ir.model.fields,field_description:hotel.field_hotel_services__property_cost_method
msgid "Costing Method"
msgstr "Метод ценообразования"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_floor__create_uid
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__create_uid
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__create_uid
#: model:ir.model.fields,field_description:hotel.field_hotel_room__create_uid
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__create_uid
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__create_uid
#: model:ir.model.fields,field_description:hotel.field_hotel_room_images__create_uid
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__create_uid
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__create_uid
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__create_uid
#: model:ir.model.fields,field_description:hotel.field_hotel_services__create_uid
msgid "Created by"
msgstr "Создано"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_floor__create_date
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__create_date
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__create_date
#: model:ir.model.fields,field_description:hotel.field_hotel_room__create_date
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__create_date
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__create_date
#: model:ir.model.fields,field_description:hotel.field_hotel_room_images__create_date
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__create_date
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__create_date
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__create_date
#: model:ir.model.fields,field_description:hotel.field_hotel_services__create_date
msgid "Created on"
msgstr "Создан"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__currency_id
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__currency_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room__currency_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__currency_id
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__currency_id
#: model:ir.model.fields,field_description:hotel.field_hotel_services__currency_id
msgid "Currency"
msgstr "Валюта"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__currency_rate
msgid "Currency Rate"
msgstr "Курс валюты"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__qty_available
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__qty_available
#: model:ir.model.fields,help:hotel.field_hotel_services__qty_available
msgid "Current quantity of products.\n"
"In a context with a single Stock Location, this includes goods stored at this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"stored in the Stock Location of the Warehouse of this Shop, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr "Текущее количество продукции.\n"
"В контексте Единого Места хранения, включает в себя товары, хранящиеся в этом месте, или любом из его детей.\n"
"В контексте единого Склада, это включает в себя товары, хранящиеся в Месте хранения этого Склад, или в любом из его детей.\n"
"хранится в Месте Хранения склада этого магазина, или любого из его детей.\n"
"В противном случае, включает в себя товары, хранящиеся в любом Месте Хранения «внутреннего» типа."

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__partner_id
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__order_partner_id
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__order_partner_id
msgid "Customer"
msgstr "Заказчик"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__sale_delay
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__sale_delay
#: model:ir.model.fields,field_description:hotel.field_hotel_services__sale_delay
msgid "Customer Lead Time"
msgstr "Срок поставки заказчика"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__access_url
msgid "Customer Portal URL"
msgstr "Ссылка на портал клиента"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__partner_ref
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__partner_ref
#: model:ir.model.fields,field_description:hotel.field_hotel_services__partner_ref
msgid "Customer Ref"
msgstr "Справка клиента"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__client_order_ref
msgid "Customer Reference"
msgstr "Описание заказчика"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__taxes_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__taxes_id
#: model:ir.model.fields,field_description:hotel.field_hotel_services__taxes_id
msgid "Customer Taxes"
msgstr "Налоги с покупателя"

#. module: hotel
#: model:ir.ui.menu,name:hotel.hotel_dashboard_menu
msgid "Dashboard"
msgstr "Панель управления"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__confirmation_date
msgid "Date on which the sales order is confirmed."
msgstr "Дата подтверждения заказа."

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__supplier_taxes_id
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__supplier_taxes_id
#: model:ir.model.fields,help:hotel.field_hotel_services__supplier_taxes_id
msgid "Default taxes used when buying the product."
msgstr "Типичные налоги применяются при покупке товара."

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__taxes_id
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__taxes_id
#: model:ir.model.fields,help:hotel.field_hotel_services__taxes_id
msgid "Default taxes used when selling the product."
msgstr "Типичные налоги применяются при продаже товара."

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__uom_id
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__uom_id
#: model:ir.model.fields,help:hotel.field_hotel_services__uom_id
msgid "Default unit of measure used for all stock operations."
msgstr "Стандартная единица измерения, используемое для всех складских операций."

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__uom_po_id
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__uom_po_id
#: model:ir.model.fields,help:hotel.field_hotel_services__uom_po_id
msgid "Default unit of measure used for purchase orders. It must be in the same category as the default unit of measure."
msgstr "Единица измерения по умолчанию, используемый для заказов на покупку. Она должна быть в той же категории, что и единица измерения по умолчанию."

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__seller_ids
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__seller_ids
#: model:ir.model.fields,help:hotel.field_hotel_services__seller_ids
msgid "Define vendor pricelists."
msgstr "Определите прайс-лист поставщика."

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__qty_delivered_manual
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__qty_delivered_manual
msgid "Delivered Manually"
msgstr "Доставлено вручную"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__qty_delivered
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__qty_delivered
msgid "Delivered Quantity"
msgstr "Доставленное количество"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__partner_shipping_id
msgid "Delivery Address"
msgstr "Адрес доставки"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__customer_lead
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__customer_lead
msgid "Delivery Lead Time"
msgstr "Время выполнения доставки"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__delivery_count
msgid "Delivery Orders"
msgstr "Заказы на доставку"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__partner_shipping_id
msgid "Delivery address for current sales order."
msgstr "Адрес доставки для текущего заказа."

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__expected_date
msgid "Delivery date you can promise to the customer, computed from product lead times and from the shipping policy of the order."
msgstr "Дату доставки можно обещать клиенту, исчисленную с момента исполнения товара и по политике доставки заказа."

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__sale_delay
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__sale_delay
#: model:ir.model.fields,help:hotel.field_hotel_services__sale_delay
msgid "Delivery lead time, in days. It's the number of days, promised to the customer, between the confirmation of the sales order and the delivery."
msgstr "Срок выполнения доставки в днях. Это количество дней, обещанная заказчику, между подтверждением заказа на продажу и доставкой."

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__route_ids
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__route_ids
#: model:ir.model.fields,help:hotel.field_hotel_services__route_ids
msgid "Depending on the modules installed, this will allow you to define the route of the product: whether it will be bought, manufactured, MTO, etc."
msgstr "В зависимости от установленных модулей, это позволит вам определить маршрут товара: будет ли он покупаться, изготавливаться на заказ и т. Д."

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__name
#: model:ir.model.fields,field_description:hotel.field_hotel_room__description
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__description
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__description
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__name
#: model:ir.model.fields,field_description:hotel.field_hotel_services__description
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_room_type_form
msgid "Description"
msgstr "Описание"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__description_pickingout
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__description_pickingout
#: model:ir.model.fields,field_description:hotel.field_hotel_services__description_pickingout
msgid "Description on Delivery Orders"
msgstr "Описание по заказам на поставку"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__description_picking
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__description_picking
#: model:ir.model.fields,field_description:hotel.field_hotel_services__description_picking
msgid "Description on Picking"
msgstr "Описание по Комлектации"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__description_pickingin
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__description_pickingin
#: model:ir.model.fields,field_description:hotel.field_hotel_services__description_pickingin
msgid "Description on Receptions"
msgstr "Описание на приемах"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__discount
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__discount
msgid "Discount (%)"
msgstr "Скидка (%)"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_floor__display_name
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__display_name
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__display_name
#: model:ir.model.fields,field_description:hotel.field_hotel_room__display_name
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__display_name
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__display_name
#: model:ir.model.fields,field_description:hotel.field_hotel_room_images__display_name
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__display_name
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__display_name
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__display_name
#: model:ir.model.fields,field_description:hotel.field_hotel_services__display_name
msgid "Display Name"
msgstr "Отображаемое Имя"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__display_type
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__display_type
msgid "Display Type"
msgstr "Тип экрана"

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "Do you want to confirm?"
msgstr "Вы хотите это подтвердить?"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio_line__is_downpayment
#: model:ir.model.fields,help:hotel.field_hotel_service_line__is_downpayment
msgid "Down payments are made when creating invoices from a sales order. They are not copied when duplicating a sales order."
msgstr "Авансовые платежи создаются на основании заказа. Они не копируются вместе с заказом при дублировании."

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__effective_date
msgid "Effective Date"
msgstr "Срок эффективности"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__tracking
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__tracking
#: model:ir.model.fields,help:hotel.field_hotel_services__tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr "Обеспечение отслеживания товара, хранящегося на вашем складе."

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__expected_date
msgid "Expected Date"
msgstr "Ожидаемая дата"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__property_account_expense_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__property_account_expense_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__property_account_expense_categ_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__property_account_expense_categ_id
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__property_account_expense_categ_id
#: model:ir.model.fields,field_description:hotel.field_hotel_services__property_account_expense_id
msgid "Expense Account"
msgstr "Счет расходов"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__expense_policy
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__expense_policy
#: model:ir.model.fields,help:hotel.field_hotel_services__expense_policy
msgid "Expenses and vendor bills can be re-invoiced to a customer.With this option, a validated expense can be re-invoice to a customer at its cost or sales price."
msgstr "Расходы и счета поставщиков могут быть повторно выставлены в счете клиенту. При таком варианте проверка расходов может быть повторно выставлена в счете клиенту по себестоимости или цене продажи."

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__fiscal_position_id
msgid "Fiscal Position"
msgstr "Система налогов"

#. module: hotel
#: model:ir.model,name:hotel.model_hotel_floor
#: model:ir.ui.menu,name:hotel.menu_open_hotel_floor_form_tree
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_room_form
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_room_tree
msgid "Floor"
msgstr "Этаж"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_floor__name
msgid "Floor Name"
msgstr "Название Этажа"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__floor_id
msgid "Floor No"
msgstr "Номер Этажа"

#. module: hotel
#: model:ir.actions.act_window,name:hotel.open_hotel_floor_form_tree
msgid "Floor Structure"
msgstr "Перекрытие"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__folio_id
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__folio_id
#: model:ir.ui.menu,name:hotel.menu_open_hotel_folio1_form_tree
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "Folio"
msgstr "Счет"

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "Folio Line"
msgstr "Строка Счета"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__message_follower_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room__message_follower_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__message_follower_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__message_follower_ids
msgid "Followers"
msgstr "Подписчики"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__message_channel_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room__message_channel_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__message_channel_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__message_channel_ids
msgid "Followers (Channels)"
msgstr "Подписчики (Каналы)"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__message_partner_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room__message_partner_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__message_partner_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__message_partner_ids
msgid "Followers (Partners)"
msgstr "Подписчики (Партнеры)"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__removal_strategy_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__removal_strategy_id
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__removal_strategy_id
msgid "Force Removal Strategy"
msgstr "Стратегия принудительного удаления"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__virtual_available
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__virtual_available
#: model:ir.model.fields,field_description:hotel.field_hotel_services__virtual_available
msgid "Forecast Quantity"
msgstr "Прогнозируемое Количество"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__virtual_available
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__virtual_available
#: model:ir.model.fields,help:hotel.field_hotel_services__virtual_available
msgid "Forecast quantity (computed as Quantity On Hand - Outgoing + Incoming)\n"
"In a context with a single Stock Location, this includes goods stored in this location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr "Прогноз количества (рассчитывается как Количество в наличии - исходящее + входящее)\n"
"В контексте с одним Складским Местом хранения, сюда входят товары, хранящиеся в этом месте, или какого-либо из его дочерних.\n"
"В контексте с одного Склада, это включает в себя товары, хранящиеся в Месте хранения Склада, или любого из его дочерних.\n"
"В противном случае, это включает в себя товары, хранящиеся на любых Местах хранения с типом \"внутренний\"."

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__packaging_ids
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__packaging_ids
#: model:ir.model.fields,help:hotel.field_hotel_services__packaging_ids
msgid "Gives the different ways to package the same product."
msgstr "Предоставляет различные варианты упаковки одного и того же продукта."

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__sequence
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__sequence
#: model:ir.model.fields,help:hotel.field_hotel_services__sequence
msgid "Gives the sequence order when displaying a product list"
msgstr "Определяет порядок следования при отображении списка товаров"

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "Guest Name"
msgstr "Имя Гостя"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__hide_expense_policy
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__hide_expense_policy
#: model:ir.model.fields,field_description:hotel.field_hotel_services__hide_expense_policy
msgid "Hide Expense Policy"
msgstr "Скрыть политику расходов"

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "History"
msgstr "История"

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_floor_form
msgid "Hotel Floor"
msgstr "Гостиничный Этаж"

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_floor_tree
msgid "Hotel Floors"
msgstr "Гостиничные Этажи"

#. module: hotel
#: model:ir.actions.act_window,name:hotel.open_hotel_folio1_form_tree
msgid "Hotel Folio"
msgstr "Гостиничный Счет"

#. module: hotel
#: model:ir.model,name:hotel.model_hotel_folio
msgid "Hotel Folio Inherit Adding POS ORDER TABS"
msgstr ""

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_tree_view
msgid "Hotel Folio1"
msgstr "Гостиничный Счет 1"

#. module: hotel
#: model:ir.ui.menu,name:hotel.hotel_management_menu
msgid "Hotel Management"
msgstr "Управление Отелем"

#. module: hotel
#: model:res.groups,name:hotel.group_hotel_user
msgid "Hotel Management / User"
msgstr "Управление Отелем / Пользователь"

#. module: hotel
#: model:res.groups,name:hotel.group_hotel_manager
msgid "Hotel Management/ Manager"
msgstr "Управление Отелем / Менеджер"

#. module: hotel
#: model:ir.actions.act_window,name:hotel.action_hotel_room_form
#: model:ir.model,name:hotel.model_hotel_room
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_room_tree
msgid "Hotel Room"
msgstr "гостиничный номер"

#. module: hotel
#: model:ir.actions.act_window,name:hotel.action_hotel_room_amenities_view_form
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_room_amenities_list
msgid "Hotel Room Amenities"
msgstr "Удобства В Гостиничном Номере"

#. module: hotel
#: model:ir.actions.act_window,name:hotel.action_hotel_room_amenities_type_view_form
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_room_amenities_type_form
msgid "Hotel Room Amenities Type"
msgstr "Тип Удобств В Гостиничном Номере"

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_room_images_form
#: model_terms:ir.ui.view,arch_db:hotel.view_room_images_tree
msgid "Hotel Room Image Gallery"
msgstr "Галерея Изображений Гостиничных Номеров"

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_room_type_form
msgid "Hotel Room Type"
msgstr "Тип Гостиничного Номера"

#. module: hotel
#: model:ir.actions.act_window,name:hotel.action_hotel_services_form
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_services_tree
msgid "Hotel Services"
msgstr "Гостиничные услуги"

#. module: hotel
#: model:ir.model,name:hotel.model_hotel_services
msgid "Hotel Services and its charges"
msgstr "Гостиничные услуги и их стоимость"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_floor__id
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__id
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__id
#: model:ir.model.fields,field_description:hotel.field_hotel_room__id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_images__id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__id
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__id
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__id
#: model:ir.model.fields,field_description:hotel.field_hotel_services__id
msgid "ID"
msgstr "Номер"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__message_unread
#: model:ir.model.fields,help:hotel.field_hotel_room__message_unread
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__message_unread
#: model:ir.model.fields,help:hotel.field_hotel_services__message_unread
msgid "If checked new messages require your attention."
msgstr "Если отмечено, новые сообщения будут требовать вашего внимания."

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__message_needaction
#: model:ir.model.fields,help:hotel.field_hotel_room__message_needaction
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__message_needaction
#: model:ir.model.fields,help:hotel.field_hotel_services__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Если отмечено - новые сообщения требуют Вашего внимания."

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__message_has_error
#: model:ir.model.fields,help:hotel.field_hotel_room__message_has_error
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__message_has_error
#: model:ir.model.fields,help:hotel.field_hotel_services__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Если обозначено, некоторые сообщения имеют ошибку доставки."

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__active
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__active
#: model:ir.model.fields,help:hotel.field_hotel_services__active
msgid "If unchecked, it will allow you to hide the product without removing it."
msgstr "Если не установлено, это позволит вам скрыть продукт, не удаляя его ."

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__picking_policy
msgid "If you deliver all products at once, the delivery order will be scheduled based on the greatest product lead time. Otherwise, it will be based on the shortest."
msgstr "Если вы поставляете все продукты сразу, заказ на поставку будет запланирован на основе наибольшего времени выполнения заказа на поставку продукта. В противном случае он будет основан на самом коротком."

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room_images__img
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__img_ids
msgid "Image"
msgstr "Изображение"

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_room_type_form
msgid "Image Gallery"
msgstr "галерея изображений"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio_line__product_image
#: model:ir.model.fields,help:hotel.field_hotel_room__image
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__image
#: model:ir.model.fields,help:hotel.field_hotel_service_line__product_image
#: model:ir.model.fields,help:hotel.field_hotel_services__image
msgid "Image of the product variant (Big-sized image of product template if false). It is automatically resized as a 1024x1024px image, with aspect ratio preserved."
msgstr "Изображение варианта товара (Изображение образца товара большого размера, если значение \"ошибочно\"). Его размер изменяется автоматически на изображение 1024x1024 пкс, с сохраненным соотношением сторон."

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__image_medium
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__image_medium
#: model:ir.model.fields,help:hotel.field_hotel_services__image_medium
msgid "Image of the product variant (Medium-sized image of product template if false)."
msgstr "Изображение варианта товара (Изображение образца товара среднего размера, если значение \"ошибочно\")."

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__image_small
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__image_small
#: model:ir.model.fields,help:hotel.field_hotel_services__image_small
msgid "Image of the product variant (Small-sized image of product template if false)."
msgstr "Изображение варианта товара (Изображение образца товара маленького размера, если значение ошибочно)."

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__property_account_income_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__property_account_income_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__property_account_income_categ_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__property_account_income_categ_id
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__property_account_income_categ_id
#: model:ir.model.fields,field_description:hotel.field_hotel_services__property_account_income_id
msgid "Income Account"
msgstr "Cчёт доходов и расходов"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__incoming_qty
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__incoming_qty
#: model:ir.model.fields,field_description:hotel.field_hotel_services__incoming_qty
msgid "Incoming"
msgstr "Входящие"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__incoterm
msgid "Incoterms"
msgstr "Инкотермс"

#. module: hotel
#: model:ir.model,name:hotel.model_sale_order_line
msgid "Inherit Order Line"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__default_code
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__default_code
#: model:ir.model.fields,field_description:hotel.field_hotel_services__default_code
msgid "Internal Reference"
msgstr "Внутренний артикул"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__barcode
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__barcode
#: model:ir.model.fields,help:hotel.field_hotel_services__barcode
msgid "International Article Number used for product identification."
msgstr "Международный  номер товара используется для идентификации товара."

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__incoterm
msgid "International Commercial Terms are a series of predefined commercial terms used in international transactions."
msgstr "Международные правила по толкованию наиболее широко используемых торговых терминов в области внешней торговли."

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__property_stock_inventory
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__property_stock_inventory
#: model:ir.model.fields,field_description:hotel.field_hotel_services__property_stock_inventory
msgid "Inventory Location"
msgstr "Место инвентаризации"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__property_valuation
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__property_valuation
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__property_valuation
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__property_valuation
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__property_valuation
#: model:ir.model.fields,field_description:hotel.field_hotel_services__property_valuation
msgid "Inventory Valuation"
msgstr "Оценка запасов"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__partner_invoice_id
msgid "Invoice Address"
msgstr "Адрес выставления счета"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__invoice_count
msgid "Invoice Count"
msgstr "Количество счетов"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__invoice_lines
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__invoice_lines
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "Invoice Lines"
msgstr "Позиции счета"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__invoice_status
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__invoice_status
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__invoice_status
msgid "Invoice Status"
msgstr "Статус счета"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__partner_invoice_id
msgid "Invoice address for current sales order."
msgstr "Адрес счета для текущего заказа."

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__qty_invoiced
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__qty_invoiced
msgid "Invoiced Quantity"
msgstr "Фактурное Количество"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__invoice_ids
msgid "Invoices"
msgstr "Счета"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__invoice_policy
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__invoice_policy
#: model:ir.model.fields,field_description:hotel.field_hotel_services__invoice_policy
msgid "Invoicing Policy"
msgstr "Политика выставления счетов"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__message_is_follower
#: model:ir.model.fields,field_description:hotel.field_hotel_room__message_is_follower
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__message_is_follower
#: model:ir.model.fields,field_description:hotel.field_hotel_services__message_is_follower
msgid "Is Follower"
msgstr "Подписчик"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__is_product_variant
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__is_product_variant
#: model:ir.model.fields,field_description:hotel.field_hotel_services__is_product_variant
msgid "Is Product Variant"
msgstr "Вариант Продукта"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__rental
msgid "Is Rental"
msgstr "Аренда"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__isroom
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__isroom
#: model:ir.model.fields,field_description:hotel.field_hotel_services__isroom
#: model:ir.model.fields,field_description:hotel.field_product_product__isroom
msgid "Is Room"
msgstr "Комната"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__isroomtype
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__isroomtype
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__isroomtype
#: model:ir.model.fields,field_description:hotel.field_product_category__isroomtype
msgid "Is Room Type"
msgstr "Тип комнаты"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_services__isservice
msgid "Is Service"
msgstr "Услуга"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__isservicetype
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__isservicetype
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__isservicetype
#: model:ir.model.fields,field_description:hotel.field_product_category__isservicetype
msgid "Is Service Type"
msgstr "Тип службы"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__isservice
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__isservice
#: model:ir.model.fields,field_description:hotel.field_product_product__isservice
msgid "Is Service id"
msgstr "Служебный идентификатор"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__is_downpayment
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__is_downpayment
msgid "Is a down payment"
msgstr "Аванс"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__isamenitype
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__isamenitype
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__isamenitype
#: model:ir.model.fields,field_description:hotel.field_product_category__isamenitype
msgid "Is amenities Type"
msgstr "Тип услуг"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__iscategid
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__iscategid
#: model:ir.model.fields,field_description:hotel.field_hotel_services__iscategid
#: model:ir.model.fields,field_description:hotel.field_product_product__iscategid
msgid "Is categ id"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__is_expense
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__is_expense
msgid "Is expense"
msgstr "Расход"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__is_expired
msgid "Is expired"
msgstr "Просрочено"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio_line__is_expense
#: model:ir.model.fields,help:hotel.field_hotel_service_line__is_expense
msgid "Is true if the sales order line comes from an expense or a vendor bills"
msgstr "Правильно, если строка заказа на продажу поступает из расходов или счетов поставщиков"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__property_account_income_id
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__property_account_income_id
#: model:ir.model.fields,help:hotel.field_hotel_services__property_account_income_id
msgid "Keep this field empty to use the default value from the product category."
msgstr "Оставьте поле пустым, чтобы использовать значение по умолчанию в категории товара."

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__property_account_expense_id
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__property_account_expense_id
#: model:ir.model.fields,help:hotel.field_hotel_services__property_account_expense_id
msgid "Keep this field empty to use the default value from the product category. If anglo-saxon accounting with automated valuation method is configured, the expense account on the product category will be used."
msgstr "Оставьте это поле пустым, чтобы использовать значение по умолчанию из категории товара. Если настроено англосаксонский учет с автоматизированным методом оценки, будут использованы счет расходов на категорию товара."

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_floor____last_update
#: model:ir.model.fields,field_description:hotel.field_hotel_folio____last_update
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line____last_update
#: model:ir.model.fields,field_description:hotel.field_hotel_room____last_update
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities____last_update
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type____last_update
#: model:ir.model.fields,field_description:hotel.field_hotel_room_images____last_update
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type____last_update
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line____last_update
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type____last_update
#: model:ir.model.fields,field_description:hotel.field_hotel_services____last_update
msgid "Last Modified on"
msgstr "Последнее изменение"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_floor__write_uid
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__write_uid
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__write_uid
#: model:ir.model.fields,field_description:hotel.field_hotel_room__write_uid
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__write_uid
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__write_uid
#: model:ir.model.fields,field_description:hotel.field_hotel_room_images__write_uid
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__write_uid
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__write_uid
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__write_uid
#: model:ir.model.fields,field_description:hotel.field_hotel_services__write_uid
msgid "Last Updated by"
msgstr "Последний раз обновил"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_floor__write_date
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__write_date
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__write_date
#: model:ir.model.fields,field_description:hotel.field_hotel_room__write_date
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__write_date
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__write_date
#: model:ir.model.fields,field_description:hotel.field_hotel_room_images__write_date
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__write_date
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__write_date
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__write_date
#: model:ir.model.fields,field_description:hotel.field_hotel_services__write_date
msgid "Last Updated on"
msgstr "Последнее обновление"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__location_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__location_id
#: model:ir.model.fields,field_description:hotel.field_hotel_services__location_id
msgid "Location"
msgstr "Место"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__message_main_attachment_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room__message_main_attachment_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__message_main_attachment_id
#: model:ir.model.fields,field_description:hotel.field_hotel_services__message_main_attachment_id
msgid "Main Attachment"
msgstr "Основное вложение"

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "Manual Description"
msgstr "Описание Руководства"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__property_valuation
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__property_valuation
#: model:ir.model.fields,help:hotel.field_hotel_services__property_valuation
msgid "Manual: The accounting entries to value the inventory are not posted automatically.\n"
"        Automated: An accounting entry is automatically created to value the inventory when a product enters or leaves the company."
msgstr "Вручную бухгалтерьки записи для оценки состава не публикуются автоматически. Автоматизировано: бухгалтерская запись автоматически создается для оценки состава, когда товар входит или выходит из компании."

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities_type__property_valuation
#: model:ir.model.fields,help:hotel.field_hotel_room_type__property_valuation
#: model:ir.model.fields,help:hotel.field_hotel_service_type__property_valuation
msgid "Manual: The accounting entries to value the inventory are not posted automatically.\n"
"        Automated: An accounting entry is automatically created to value the inventory when a product enters or leaves the company.\n"
"        "
msgstr "Вручную бухгалтерьки записи для оценки состава не публикуются автоматически. Автоматизировано: бухгалтерская запись автоматически создается для оценки состава, когда товар входит или выходит из компании."

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__service_type
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__service_type
#: model:ir.model.fields,help:hotel.field_hotel_services__service_type
msgid "Manually set quantities on order: Invoice based on the manually entered quantity, without creating an analytic account.\n"
"Timesheets on contract: Invoice based on the tracked hours on the related timesheet.\n"
"Create a task and track hours: Create a task on the sales order validation and track the work hours."
msgstr "Задавайте количество в заказе вручную: счет на основе количества, введенного вручную, без создания аналитического счета.\n"
"Табели учета рабочего времени в контракте: счет на основе записанных часов в соответствующем табеле.\n"
"Создавайте задачи и вносите часы: Создайте задачу при подтверждении заказа и вносите в нее рабочие часы."

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__mrp_product_qty
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__mrp_product_qty
#: model:ir.model.fields,field_description:hotel.field_hotel_services__mrp_product_qty
msgid "Manufactured"
msgstr "Произведено"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__produce_delay
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__produce_delay
#: model:ir.model.fields,field_description:hotel.field_hotel_services__produce_delay
msgid "Manufacturing Lead Time"
msgstr "Время производства"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__max_adult
msgid "Max Adult"
msgstr "Максимальное количесвто взрослых"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__max_child
msgid "Max Child"
msgstr "Максимальное количство детей"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__image_medium
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__image_medium
#: model:ir.model.fields,field_description:hotel.field_hotel_services__image_medium
msgid "Medium-sized image"
msgstr "Изображение средних размеров"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__message_has_error
#: model:ir.model.fields,field_description:hotel.field_hotel_room__message_has_error
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__message_has_error
#: model:ir.model.fields,field_description:hotel.field_hotel_services__message_has_error
msgid "Message Delivery error"
msgstr "Ошибка доставки сообщения"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__sale_line_warn_msg
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__sale_line_warn_msg
#: model:ir.model.fields,field_description:hotel.field_hotel_services__sale_line_warn_msg
msgid "Message for Sales Order Line"
msgstr "Сообщение по позиции заказа продаж"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__message_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room__message_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__message_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__message_ids
msgid "Messages"
msgstr "Сообщения"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__qty_delivered_method
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__qty_delivered_method
msgid "Method to update delivered qty"
msgstr "Метод обновления доставленной количества"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__orderpoint_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__orderpoint_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__orderpoint_ids
msgid "Minimum Stock Rules"
msgstr "Правила минимальных запасов"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__name
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__name
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__name
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__name
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__name
#: model:ir.model.fields,field_description:hotel.field_hotel_services__name
msgid "Name"
msgstr "Название"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__signed_by
msgid "Name of the person that signed the SO."
msgstr "Имя лица, подписавшего заказ на продажу."

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__activity_date_deadline
#: model:ir.model.fields,field_description:hotel.field_hotel_room__activity_date_deadline
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__activity_date_deadline
#: model:ir.model.fields,field_description:hotel.field_hotel_services__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Дедлайн следующему шагу"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__activity_summary
#: model:ir.model.fields,field_description:hotel.field_hotel_room__activity_summary
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__activity_summary
#: model:ir.model.fields,field_description:hotel.field_hotel_services__activity_summary
msgid "Next Activity Summary"
msgstr "Резюме следующего мероприятия"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__activity_type_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room__activity_type_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__activity_type_id
#: model:ir.model.fields,field_description:hotel.field_hotel_services__activity_type_id
msgid "Next Activity Type"
msgstr "Тип следующему шагу"

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "Notes"
msgstr "Заметки"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__message_needaction_counter
#: model:ir.model.fields,field_description:hotel.field_hotel_room__message_needaction_counter
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__message_needaction_counter
#: model:ir.model.fields,field_description:hotel.field_hotel_services__message_needaction_counter
msgid "Number of Actions"
msgstr "Количество действий"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio_line__customer_lead
#: model:ir.model.fields,help:hotel.field_hotel_service_line__customer_lead
msgid "Number of days between the order confirmation and the shipping of the products to the customer"
msgstr "Количество дней между подтверждением заказа и отгрузкой продукции заказчику"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__message_has_error_counter
#: model:ir.model.fields,field_description:hotel.field_hotel_room__message_has_error_counter
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__message_has_error_counter
#: model:ir.model.fields,field_description:hotel.field_hotel_services__message_has_error_counter
msgid "Number of error"
msgstr "количество ошибок"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__message_needaction_counter
#: model:ir.model.fields,help:hotel.field_hotel_room__message_needaction_counter
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__message_needaction_counter
#: model:ir.model.fields,help:hotel.field_hotel_services__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Количество сообщений, требующих внимания"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__message_has_error_counter
#: model:ir.model.fields,help:hotel.field_hotel_room__message_has_error_counter
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__message_has_error_counter
#: model:ir.model.fields,help:hotel.field_hotel_services__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Количество сообщений с ложной дставкою"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__message_unread_counter
#: model:ir.model.fields,help:hotel.field_hotel_room__message_unread_counter
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__message_unread_counter
#: model:ir.model.fields,help:hotel.field_hotel_services__message_unread_counter
msgid "Number of unread messages"
msgstr "Количество непрочитанных сообщений"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__require_payment
msgid "Online Payment"
msgstr "Онлайн платеж"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__require_signature
msgid "Online Signature"
msgstr "Онлайн подпись"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__optional_product_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__optional_product_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__optional_product_ids
msgid "Optional Products"
msgstr "Дополнительные товары"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__optional_product_ids
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__optional_product_ids
#: model:ir.model.fields,help:hotel.field_hotel_services__optional_product_ids
msgid "Optional Products are suggested whenever the customer hits *Add to Cart* (cross-sell strategy, e.g. for computers: warranty, software, etc.)."
msgstr "Дополнительные продукты предлагаются всякий раз, когда клиент попадает *Добавить в корзину * (кросс продажи стратегии, например, для компьютеров: гарантия, программное обеспечение и т.д.)."

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__date_order
msgid "Order Date"
msgstr "Дата заказа"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__order_id
msgid "Order Id"
msgstr "Номер заказа"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__order_line
msgid "Order Lines"
msgstr "Позиции заказа"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__name
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__order_id
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__order_id
msgid "Order Reference"
msgstr "Ссылка на заказ"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__order_reserve_invoice_ids
msgid "Order Reservation Invoices"
msgstr "Счета для забронированных заказов"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__state
msgid "Order State"
msgstr "Упорядоченное Состояние"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__state
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__state
msgid "Order Status"
msgstr "Статус заказа"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__invoice_policy
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__invoice_policy
#: model:ir.model.fields,help:hotel.field_hotel_services__invoice_policy
msgid "Ordered Quantity: Invoice quantities ordered by the customer.\n"
"Delivered Quantity: Invoice quantities delivered to the customer."
msgstr "Заказанное количество: количество счетов-фактур заказанных клиентом. Доставлена количество: количество счетов, доставленных клиенту."

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "Other Data"
msgstr "Другие данные"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__outgoing_qty
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__outgoing_qty
#: model:ir.model.fields,field_description:hotel.field_hotel_services__outgoing_qty
msgid "Outgoing"
msgstr "Исходящие"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__product_packaging
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__product_packaging
msgid "Package"
msgstr "Упаковка"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__parent_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__parent_id
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__parent_id
msgid "Parent Category"
msgstr "Категория предка"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__parent_path
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__parent_path
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__parent_path
msgid "Parent Path"
msgstr "Родительский путь"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__reference
msgid "Payment Ref."
msgstr "референс оплаты"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__payment_term_id
msgid "Payment Terms"
msgstr "Условия оплаты"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__picking_ids
msgid "Pickings"
msgstr "Сборки"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__pos_categ_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__pos_categ_id
#: model:ir.model.fields,field_description:hotel.field_hotel_services__pos_categ_id
msgid "Point of Sale Category"
msgstr "Категории для точки продаж"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__access_url
msgid "Portal Access URL"
msgstr "Ссылка для доступа на портал"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__price
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__price
#: model:ir.model.fields,field_description:hotel.field_hotel_services__price
msgid "Price"
msgstr "Цена"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__price_reduce
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__price_reduce
msgid "Price Reduce"
msgstr "Уменьшение цены"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__price_reduce_taxexcl
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__price_reduce_taxexcl
msgid "Price Reduce Tax excl"
msgstr "Цена, уменьшенная на исключенный налог"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__price_reduce_taxinc
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__price_reduce_taxinc
msgid "Price Reduce Tax inc"
msgstr "Цена, уменьшенная на включенный налог"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__list_price
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__list_price
#: model:ir.model.fields,help:hotel.field_hotel_services__list_price
msgid "Price at which the product is sold to customers."
msgstr "Цена, по которой продается товар клиентам."

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__pricelist_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room__pricelist_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__pricelist_id
#: model:ir.model.fields,field_description:hotel.field_hotel_services__pricelist_id
msgid "Pricelist"
msgstr "Прайс-лист"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__pricelist_item_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__pricelist_item_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__pricelist_item_ids
msgid "Pricelist Item"
msgstr "Пункт прайс-листа"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__item_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__item_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__item_ids
msgid "Pricelist Items"
msgstr "Позиции прайс-листа"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__pricelist_id
msgid "Pricelist for current sales order."
msgstr "Прайс-лист для текущего заказа продаж."

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__procurement_group_id
msgid "Procurement Group"
msgstr "Группа поставки"

#. module: hotel
#: model:ir.model,name:hotel.model_product_product
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__product_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room__product_variant_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__product_variant_id
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__product_id
#: model:ir.model.fields,field_description:hotel.field_hotel_services__product_variant_id
msgid "Product"
msgstr "Продукт"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__attribute_line_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__attribute_line_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__attribute_line_ids
msgid "Product Attributes"
msgstr "Атрибуты ТМЦ"

#. module: hotel
#: model:ir.model,name:hotel.model_product_category
#: model:ir.model.fields,field_description:hotel.field_hotel_room__categ_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__categ_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__room_categ_id
#: model:ir.model.fields,field_description:hotel.field_hotel_services__categ_id
msgid "Product Category"
msgstr "Категория продукта"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__product_image
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__product_image
msgid "Product Image"
msgstr "Изображения продукта "

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__packaging_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__packaging_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__packaging_ids
msgid "Product Packages"
msgstr "Варианты упаковки продукта"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__product_tmpl_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__product_tmpl_id
#: model:ir.model.fields,field_description:hotel.field_hotel_services__product_tmpl_id
msgid "Product Template"
msgstr "Шаблон продукта"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__type
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__type
#: model:ir.model.fields,field_description:hotel.field_hotel_services__type
msgid "Product Type"
msgstr "Тип продукта"

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_room_amenities_form
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_room_form
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_services_form
msgid "Product Variant"
msgstr "Вариант продукта"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__product_no_variant_attribute_value_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__product_no_variant_attribute_value_ids
msgid "Product attribute values that do not create variants"
msgstr "Значения атрибутов товара, которые не создают варианты"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__product_id
msgid "Product_id"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__property_stock_production
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__property_stock_production
#: model:ir.model.fields,field_description:hotel.field_hotel_services__property_stock_production
msgid "Production Location"
msgstr "Место производства"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__product_variant_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__product_variant_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__product_variant_ids
msgid "Products"
msgstr "Продукты"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__description_purchase
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__description_purchase
#: model:ir.model.fields,field_description:hotel.field_hotel_services__description_purchase
msgid "Purchase Description"
msgstr "Описание закупки"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__uom_po_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__uom_po_id
#: model:ir.model.fields,field_description:hotel.field_hotel_services__uom_po_id
msgid "Purchase Unit of Measure"
msgstr "Единицы измерения при закупке"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__product_uom_qty
#: model:ir.model.fields,field_description:hotel.field_hotel_room__qty_at_date
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__qty_at_date
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__product_uom_qty
#: model:ir.model.fields,field_description:hotel.field_hotel_services__qty_at_date
#: model:ir.model.fields,field_description:hotel.field_sale_order_line__product_uom_qty
msgid "Quantity"
msgstr "Количество"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__qty_available
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__qty_available
#: model:ir.model.fields,field_description:hotel.field_hotel_services__qty_available
msgid "Quantity On Hand"
msgstr "Количество на руках"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__incoming_qty
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__incoming_qty
#: model:ir.model.fields,help:hotel.field_hotel_services__incoming_qty
msgid "Quantity of planned incoming products.\n"
"In a context with a single Stock Location, this includes goods arriving to this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods arriving to the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods arriving to any Stock Location with 'internal' type."
msgstr "Количество запланированных входящих товаров. В контексте, состоящий из одной позиции на складе, включая товары, поступающие к этому местонахождение, или любых дочерних. В контексте с одним составом это включает товары, поступающие к местонахождению этого состава, или любого дочернего состава. В противном случае это включает товары, прибывающие к любому местонахождение склада с `внутренним` типом."

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__outgoing_qty
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__outgoing_qty
#: model:ir.model.fields,help:hotel.field_hotel_services__outgoing_qty
msgid "Quantity of planned outgoing products.\n"
"In a context with a single Stock Location, this includes goods leaving this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods leaving the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods leaving any Stock Location with 'internal' type."
msgstr "Количество запланированных выходных товаров. В контексте, состоящий из одного места расположения магазина, это включает товары, которые оставляют это местонахождение, или любой из его дочерних складов. В контексте единого состава это включает товары, которые оставляют место расположения этого склада или любого его дочернего. В противном случае это включает в себя товары, оставляют любое местонахождение во `внутреннем` типу."

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__expense_policy
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__expense_policy
#: model:ir.model.fields,field_description:hotel.field_hotel_services__expense_policy
msgid "Re-Invoice Policy"
msgstr "Политика повторного выставления счетов"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__code
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__code
#: model:ir.model.fields,field_description:hotel.field_hotel_services__code
msgid "Reference"
msgstr "Ссылка"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__origin
msgid "Reference of the document that generated this sales order request."
msgstr "Ссылка на документ который создал запрос на этот заказ продаж."

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "Related invoices"
msgstr "Соответствующие счета"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__remaining_validity_days
msgid "Remaining Validity Days"
msgstr "Оставшиеся Дни Действия Договора"

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "Rent"
msgstr "Арендовать"

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "Rent(UOM)"
msgstr "Арендовать"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__reordering_max_qty
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__reordering_max_qty
#: model:ir.model.fields,field_description:hotel.field_hotel_services__reordering_max_qty
msgid "Reordering Max Qty"
msgstr "Максимальное количество пополнений"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__reordering_min_qty
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__reordering_min_qty
#: model:ir.model.fields,field_description:hotel.field_hotel_services__reordering_min_qty
msgid "Reordering Min Qty"
msgstr "Минимальное количество пополнений"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__nbr_reordering_rules
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__nbr_reordering_rules
#: model:ir.model.fields,field_description:hotel.field_hotel_services__nbr_reordering_rules
msgid "Reordering Rules"
msgstr "Правила повторных заказов"

#. module: hotel
#: model:ir.ui.menu,name:hotel.hotel_report_menu
msgid "Reports"
msgstr "Отчёты"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__require_signature
msgid "Request a online signature to the customer in order to confirm orders automatically."
msgstr "Подавайте запрос на онлайн-подпись клиенту, чтобы подтвердить заказ автоматически."

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__require_payment
msgid "Request an online payment to the customer in order to confirm orders automatically."
msgstr "Делайте запрос на онлайн-платеж клиенту в заказе, чтобы подтвердить заказ автоматически."

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "Reserve Order Invoices"
msgstr "Счета Заказа"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__responsible_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__responsible_id
#: model:ir.model.fields,field_description:hotel.field_hotel_services__responsible_id
msgid "Responsible"
msgstr "Ответственный"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__activity_user_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room__activity_user_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__activity_user_id
#: model:ir.model.fields,field_description:hotel.field_hotel_services__activity_user_id
msgid "Responsible User"
msgstr "Ответственный"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__room_amenities
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_room_form
msgid "Room Amenities"
msgstr "Удобства В Номере"

#. module: hotel
#: model:ir.ui.menu,name:hotel.menu_open_hotel_room_type_form_tree
msgid "Room Categories"
msgstr "Категория номеров"

#. module: hotel
#: model:ir.ui.menu,name:hotel.menu_open_hotel_room_type_parent
msgid "Room Definations"
msgstr "Определения Комнат"

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "Room Lines"
msgstr "Линии Комнат"

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "Room No"
msgstr "Номер Комнаты"

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_room_tree
msgid "Room Status"
msgstr "Статус Комнаты"

#. module: hotel
#: model:ir.actions.act_window,name:hotel.open_hotel_room_type_form_tree
#: model:ir.model,name:hotel.model_hotel_room_type
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__categ_id
msgid "Room Type"
msgstr "Тип Комнаты"

#. module: hotel
#: model:ir.model,name:hotel.model_hotel_room_amenities
msgid "Room amenities"
msgstr "Удобства в номере"

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_room_tree
msgid "Room rate"
msgstr "Тариф Комнаты"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__room_lines
#: model:ir.ui.menu,name:hotel.menu_open_hotel_room_form
msgid "Rooms"
msgstr "Комнаты"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__route_id
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__route_id
msgid "Route"
msgstr "Маршрут"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__route_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__route_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__route_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__route_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__route_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__route_ids
msgid "Routes"
msgstr "Маршруты"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__description_sale
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__description_sale
#: model:ir.model.fields,field_description:hotel.field_hotel_services__description_sale
msgid "Sale Description"
msgstr "Описание продажи"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__lst_price
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__lst_price
#: model:ir.model.fields,field_description:hotel.field_hotel_services__lst_price
msgid "Sale Price"
msgstr "Цена продажи"

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio_filter
msgid "Sales Order"
msgstr "Заказ на продажу"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__sale_line_warn
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__sale_line_warn
#: model:ir.model.fields,field_description:hotel.field_hotel_services__sale_line_warn
msgid "Sales Order Line"
msgstr "Строка заказа на продажу"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__list_price
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__list_price
#: model:ir.model.fields,field_description:hotel.field_hotel_services__list_price
msgid "Sales Price"
msgstr "Продажная цена"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__team_id
msgid "Sales Team"
msgstr "Команда продаж"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__user_id
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__salesman_id
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__salesman_id
msgid "Salesperson"
msgstr "Продавец"

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio_filter
msgid "Search Hotel Folio"
msgstr "Поиск гостиничного счета"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__access_token
msgid "Security Token"
msgstr "Токен безопасности"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__categ_id
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__categ_id
#: model:ir.model.fields,help:hotel.field_hotel_services__categ_id
msgid "Select category for the current product"
msgstr "Выберите категорию для продукта"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__sale_line_warn
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__sale_line_warn
#: model:ir.model.fields,help:hotel.field_hotel_services__sale_line_warn
msgid "Selecting the \"Warning\" option will notify user with the message, Selecting \"Blocking Message\" will throw an exception with the message and block the flow. The Message has to be written in the next field."
msgstr "\"Предупреждение\" - сообщить пользователю. \"Блокирующее сообщение\" - исключительная ситуация, сообщить пользователю и заблокировать рабочий процесс. Текст сообщения должен быть записан в следующее поле."

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_floor__sequence
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__sequence
#: model:ir.model.fields,field_description:hotel.field_hotel_room__sequence
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__sequence
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__sequence
#: model:ir.model.fields,field_description:hotel.field_hotel_services__sequence
msgid "Sequence"
msgstr "Нумерация"

#. module: hotel
#: model:ir.ui.menu,name:hotel.menu_open_hotel_service_type_form_tree
msgid "Service Categories"
msgstr "Категории услуг"

#. module: hotel
#: model:ir.ui.menu,name:hotel.menu_open_hotel_service_type_form_tree_parent
msgid "Service Definations"
msgstr "Определение службы"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_services__service_id
msgid "Service Id"
msgstr "Служебный Идентификатор"

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "Service Line"
msgstr "Линия обслуживания"

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "Service Lines"
msgstr "Линии обслуживания"

#. module: hotel
#: model:ir.actions.act_window,name:hotel.open_hotel_service_type_form_tree
#: model:ir.model,name:hotel.model_hotel_service_type
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_service_type_form
msgid "Service Type"
msgstr "Тип службы"

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_services_tree
msgid "Service rate"
msgstr "Скорость обслуживания"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__service_lines
#: model:ir.ui.menu,name:hotel.menu_open_hotel_services_form
msgid "Services"
msgstr "Услуги"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities_type__removal_strategy_id
#: model:ir.model.fields,help:hotel.field_hotel_room_type__removal_strategy_id
#: model:ir.model.fields,help:hotel.field_hotel_service_type__removal_strategy_id
msgid "Set a specific removal strategy that will be used regardless of the source location for this product category"
msgstr "Выберите конкретную стратегию удаления, которая будет использоваться независимо от расположения источника этой категории продукции"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__picking_policy
msgid "Shipping Policy"
msgstr "Политика доставки"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__shop_id
msgid "Shop"
msgstr "Магазин"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__signature
msgid "Signature"
msgstr "Подпись"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__signature
msgid "Signature received through the portal."
msgstr "Подпись получено через портал."

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__signed_by
msgid "Signed by"
msgstr "Подписано"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__image_small
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__image_small
#: model:ir.model.fields,field_description:hotel.field_hotel_services__image_small
msgid "Small-sized image"
msgstr "Маленькое изображение"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__sales_count
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__sales_count
#: model:ir.model.fields,field_description:hotel.field_hotel_services__sales_count
msgid "Sold"
msgstr "Продано"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__origin
msgid "Source Document"
msgstr "Документ-источник"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__property_cost_method
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__property_cost_method
#: model:ir.model.fields,help:hotel.field_hotel_services__property_cost_method
msgid "Standard Price: The products are valued at their standard cost defined on the product.\n"
"        Average Cost (AVCO): The products are valued at weighted average cost.\n"
"        First In First Out (FIFO): The products are valued supposing those that enter the company first will also leave it first."
msgstr "Стандартная цена: товары оцениваются по их стандартной стоимости, определенной на товаре. Средняя цена (AVCO): товары оцениваются по средней стоимости. First In First Out (FIFO): товары оцениваются, считая, что те, кто входит в компанию впервые, также оставят его сначала."

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities_type__property_cost_method
#: model:ir.model.fields,help:hotel.field_hotel_room_type__property_cost_method
#: model:ir.model.fields,help:hotel.field_hotel_service_type__property_cost_method
msgid "Standard Price: The products are valued at their standard cost defined on the product.\n"
"        Average Cost (AVCO): The products are valued at weighted average cost.\n"
"        First In First Out (FIFO): The products are valued supposing those that enter the company first will also leave it first.\n"
"        "
msgstr "Стандартная цена: товары оцениваются по их стандартной стоимости, определенной на товаре. Средняя цена (AVCO): товары оцениваются по средней стоимости. First In First Out (FIFO): товары оцениваются, считая, что те, кто входит в компанию впервые, также оставят его сначала."

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "States"
msgstr "Регионы"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__state
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__state
#: model:ir.model.fields,field_description:hotel.field_hotel_services__state
#: model:ir.model.fields,field_description:hotel.field_product_product__state
msgid "Status"
msgstr "Статус"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__activity_state
#: model:ir.model.fields,help:hotel.field_hotel_room__activity_state
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__activity_state
#: model:ir.model.fields,help:hotel.field_hotel_services__activity_state
msgid "Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr "Этап основан на действиях Просроченный: срок исполнения уже прошел Сегодня: дата действия сегодня Запланировано: будущие действия."

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__stock_fifo_manual_move_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__stock_fifo_manual_move_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__stock_fifo_manual_move_ids
msgid "Stock Fifo Manual Move"
msgstr "Ручное перемещение по Fifo"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__stock_fifo_real_time_aml_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__stock_fifo_real_time_aml_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__stock_fifo_real_time_aml_ids
msgid "Stock Fifo Real Time Aml"
msgstr "Перемещение в реальном времени по Fifo"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__property_stock_account_input
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__property_stock_account_input
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__property_stock_account_input_categ_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__property_stock_account_input_categ_id
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__property_stock_account_input_categ_id
#: model:ir.model.fields,field_description:hotel.field_hotel_services__property_stock_account_input
msgid "Stock Input Account"
msgstr "Счет стоимости входящих ТМЦ"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__property_stock_journal
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__property_stock_journal
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__property_stock_journal
msgid "Stock Journal"
msgstr "Складской журнал"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__stock_move_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__stock_move_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__stock_move_ids
msgid "Stock Move"
msgstr "Перемещение запасов"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__move_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__move_ids
msgid "Stock Moves"
msgstr "Движения запасов"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__property_stock_account_output
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__property_stock_account_output
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__property_stock_account_output_categ_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__property_stock_account_output_categ_id
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__property_stock_account_output_categ_id
#: model:ir.model.fields,field_description:hotel.field_hotel_services__property_stock_account_output
msgid "Stock Output Account"
msgstr "Счет стоимости исходящих ТМЦ"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__stock_quant_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__stock_quant_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__stock_quant_ids
msgid "Stock Quant"
msgstr "Количество на складе"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__property_stock_valuation_account_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__property_stock_valuation_account_id
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__property_stock_valuation_account_id
msgid "Stock Valuation Account"
msgstr "Счёт оценки запасов"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__stock_value_currency_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__stock_value_currency_id
#: model:ir.model.fields,field_description:hotel.field_hotel_services__stock_value_currency_id
msgid "Stock Value Currency"
msgstr "Валюта остатков запаса"

#. module: hotel
#: model:ir.model,name:hotel.model_hotel_room_images
msgid "Store multiple images for each room"
msgstr "Храните несколько изображений для каждой комнаты"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__price_subtotal
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__price_subtotal
msgid "Subtotal"
msgstr "Подытог"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__table_order_invoice_ids
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "Table Order Invoices"
msgstr "Счета с заказов со столиков"

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_form
msgid "Table Reservations"
msgstr "Бронирования Столиков"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__amount_by_group
msgid "Tax amount by group"
msgstr "Сумма налога по группам"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__amount_tax
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__tax_id
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__tax_id
msgid "Taxes"
msgstr "Налоги"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__valid_archived_variant_ids
#: model:ir.model.fields,help:hotel.field_hotel_room__valid_existing_variant_ids
#: model:ir.model.fields,help:hotel.field_hotel_room__valid_product_attribute_ids
#: model:ir.model.fields,help:hotel.field_hotel_room__valid_product_attribute_value_ids
#: model:ir.model.fields,help:hotel.field_hotel_room__valid_product_attribute_value_wnva_ids
#: model:ir.model.fields,help:hotel.field_hotel_room__valid_product_attribute_wnva_ids
#: model:ir.model.fields,help:hotel.field_hotel_room__valid_product_template_attribute_line_ids
#: model:ir.model.fields,help:hotel.field_hotel_room__valid_product_template_attribute_line_wnva_ids
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__valid_archived_variant_ids
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__valid_existing_variant_ids
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__valid_product_attribute_ids
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__valid_product_attribute_value_ids
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__valid_product_attribute_value_wnva_ids
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__valid_product_attribute_wnva_ids
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__valid_product_template_attribute_line_ids
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__valid_product_template_attribute_line_wnva_ids
#: model:ir.model.fields,help:hotel.field_hotel_services__valid_archived_variant_ids
#: model:ir.model.fields,help:hotel.field_hotel_services__valid_existing_variant_ids
#: model:ir.model.fields,help:hotel.field_hotel_services__valid_product_attribute_ids
#: model:ir.model.fields,help:hotel.field_hotel_services__valid_product_attribute_value_ids
#: model:ir.model.fields,help:hotel.field_hotel_services__valid_product_attribute_value_wnva_ids
#: model:ir.model.fields,help:hotel.field_hotel_services__valid_product_attribute_wnva_ids
#: model:ir.model.fields,help:hotel.field_hotel_services__valid_product_template_attribute_line_ids
#: model:ir.model.fields,help:hotel.field_hotel_services__valid_product_template_attribute_line_wnva_ids
msgid "Technical compute"
msgstr "Техническое вычисления"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio_line__display_type
#: model:ir.model.fields,help:hotel.field_hotel_service_line__display_type
msgid "Technical field for UX purpose."
msgstr "Техническое поле для назначения UX."

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__pricelist_id
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__pricelist_id
#: model:ir.model.fields,help:hotel.field_hotel_services__pricelist_id
msgid "Technical field. Used for searching on pricelists, not stored in database."
msgstr "Техническое поле. Используется для поиска по прайс-листам, не хранится в базе данных."

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__stock_move_ids
#: model:ir.model.fields,help:hotel.field_hotel_room__stock_quant_ids
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__stock_move_ids
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__stock_quant_ids
#: model:ir.model.fields,help:hotel.field_hotel_services__stock_move_ids
#: model:ir.model.fields,help:hotel.field_hotel_services__stock_quant_ids
msgid "Technical: used to compute quantities."
msgstr "Технический: используется для вычисления количества."

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__state
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__state
#: model:ir.model.fields,help:hotel.field_hotel_services__state
#: model:ir.model.fields,help:hotel.field_product_product__state
msgid "Tells the user if room is available of booked."
msgstr "Сообщает пользователю, свободен ли забронированный номер."

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__product_template_attribute_value_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__product_template_attribute_value_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__product_template_attribute_value_ids
msgid "Template Attribute Values"
msgstr "Значение шаблона атрибута"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__note
msgid "Terms and conditions"
msgstr "Условия"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__amount_untaxed
msgid "The amount without tax."
msgstr "Сумма без учета налога."

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__analytic_account_id
msgid "The analytic account related to a sales order."
msgstr "Счет аналитики связанный с заказом продаж."

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities_type__property_account_expense_categ_id
#: model:ir.model.fields,help:hotel.field_hotel_room_type__property_account_expense_categ_id
#: model:ir.model.fields,help:hotel.field_hotel_service_type__property_account_expense_categ_id
msgid "The expense is accounted for when a vendor bill is validated, except in anglo-saxon accounting with perpetual inventory valuation in which case the expense (Cost of Goods Sold account) is recognized at the customer invoice validation."
msgstr "Расходы учитываются при проверке счета поставщика, за исключением англосаксонского бухучета с бесконечной оценке запаса, в этом случае расходы (счет стоимости продаваемого товара) признаются подтверждением счета-фактуры клиента."

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities_type__product_count
#: model:ir.model.fields,help:hotel.field_hotel_room_type__product_count
#: model:ir.model.fields,help:hotel.field_hotel_service_type__product_count
msgid "The number of products under this category (Does not consider the children categories)"
msgstr "Количество продуктов этой категории (не учитывает подчиненные категории )"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__reference
msgid "The payment communication of this sale order."
msgstr "Платежное извещение этого заказа на продажу."

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__currency_rate
msgid "The rate of the currency to the currency of rate 1 applicable at the date of the order"
msgstr "Ставка валюты к курсу 1 применяется на дату заказа"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__lst_price
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__lst_price
#: model:ir.model.fields,help:hotel.field_hotel_services__lst_price
msgid "The sale price is managed from the product template. Click on the 'Configure Variants' button to set the extra attribute prices."
msgstr "Цена продажи управляется из шаблона товара. Нажмите кнопку `Настроить варианты`, чтобы установить дополнительные цены атрибутов."

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__amount_tax
msgid "The tax amount."
msgstr "Сумма налога."

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__volume
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__volume
#: model:ir.model.fields,help:hotel.field_hotel_services__volume
msgid "The volume in m3."
msgstr "Объём в метрах куб."

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities_type__property_account_income_categ_id
#: model:ir.model.fields,help:hotel.field_hotel_room_type__property_account_income_categ_id
#: model:ir.model.fields,help:hotel.field_hotel_service_type__property_account_income_categ_id
msgid "This account will be used when validating a customer invoice."
msgstr "Этот счет будет использовано при проверке счета-фактуры клиента."

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room_images__img
msgid "This field holds the image for Room, limited to 1024x1024px"
msgstr "Это поле содержит изображение для комнаты, ограниченное 1024x1024px"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__image_variant
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__image_variant
#: model:ir.model.fields,help:hotel.field_hotel_services__image_variant
msgid "This field holds the image used as image for the product variant, limited to 1024x1024px."
msgstr "Это поле содержит изображение варианта продукта, лимит - 1024x1024px."

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__commitment_date
msgid "This is the delivery date promised to the customer. If set, the delivery order will be scheduled based on this date rather than product lead times."
msgstr "Это дата доставки, обещанная заказчику. Если установлено, заказ на доставку будет запланировано на основе этой даты, а не времени выполнения товара."

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__price_extra
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__price_extra
#: model:ir.model.fields,help:hotel.field_hotel_services__price_extra
msgid "This is the sum of the extra price of all attributes"
msgstr "Это сумма дополнительной цены всех атрибутов"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__property_stock_production
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__property_stock_production
#: model:ir.model.fields,help:hotel.field_hotel_services__property_stock_production
msgid "This stock location will be used, instead of the default one, as the source location for stock moves generated by manufacturing orders."
msgstr "Это место хранения будет использоваться, вместо места хранения по умолчанию, как исходное место хранения для движений созданных производственными заказами."

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__property_stock_inventory
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__property_stock_inventory
#: model:ir.model.fields,help:hotel.field_hotel_services__property_stock_inventory
msgid "This stock location will be used, instead of the default one, as the source location for stock moves generated when you do an inventory."
msgstr "Это место хранения будет использоваться, вместо места хранения по умолчанию, как исходное место хранения для движений созданных при проведении инвентаризации."

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__responsible_id
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__responsible_id
#: model:ir.model.fields,help:hotel.field_hotel_services__responsible_id
msgid "This user will be responsible of the next activities related to logistic operations for this product."
msgstr "Этот пользователь будет отвечать за следующие действия, связанные с логистическими операциями для этого товара."

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room_images__name
msgid "Title"
msgstr "Заголовок"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__qty_to_invoice
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__qty_to_invoice
msgid "To Invoice Quantity"
msgstr "Количество счетов-фактур"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__to_weight
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__to_weight
#: model:ir.model.fields,field_description:hotel.field_hotel_services__to_weight
msgid "To Weigh With Scale"
msgstr "Для взвешивания на весах"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__amount_total
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__price_total
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__price_total
msgid "Total"
msgstr "Всего"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__price_tax
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__price_tax
msgid "Total Tax"
msgstr "сумма налога"

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_folio1_tree_view
msgid "Total amount"
msgstr "Общая сумма"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__total_route_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_type__total_route_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_service_type__total_route_ids
msgid "Total routes"
msgstr "Итого маршрутов"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__service_type
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__service_type
#: model:ir.model.fields,field_description:hotel.field_hotel_services__service_type
msgid "Track Service"
msgstr "Сервис отслеживания"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__tracking
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__tracking
#: model:ir.model.fields,field_description:hotel.field_hotel_services__tracking
msgid "Tracking"
msgstr "Отслеживание"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__transaction_ids
msgid "Transactions"
msgstr "Транзакции"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__type_name
msgid "Type Name"
msgstr "Название типа"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__price_unit
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__price_unit
msgid "Unit Price"
msgstr "Цена за ед."

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__product_uom
#: model:ir.model.fields,field_description:hotel.field_hotel_room__uom_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__uom_id
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__product_uom
#: model:ir.model.fields,field_description:hotel.field_hotel_services__uom_id
msgid "Unit of Measure"
msgstr "Единица измерения"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__uom_name
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__uom_name
#: model:ir.model.fields,field_description:hotel.field_hotel_services__uom_name
msgid "Unit of Measure Name"
msgstr "Название единицы измерения"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__message_unread
#: model:ir.model.fields,field_description:hotel.field_hotel_room__message_unread
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__message_unread
#: model:ir.model.fields,field_description:hotel.field_hotel_services__message_unread
msgid "Unread Messages"
msgstr "Непрочитанные Сообщения"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__message_unread_counter
#: model:ir.model.fields,field_description:hotel.field_hotel_room__message_unread_counter
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__message_unread_counter
#: model:ir.model.fields,field_description:hotel.field_hotel_services__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Счетчик непрочитанных сообщений"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__amount_untaxed
msgid "Untaxed Amount"
msgstr "Сумма без налога"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__untaxed_amount_to_invoice
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__untaxed_amount_to_invoice
msgid "Untaxed Amount To Invoice"
msgstr "Облагаемая налогом сумма для выставления в счете-фактуре"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__untaxed_amount_invoiced
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__untaxed_amount_invoiced
msgid "Untaxed Invoiced Amount"
msgstr "Облагаемая налогом сумма счета-фактуры"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__product_custom_attribute_value_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__product_custom_attribute_value_ids
msgid "User entered custom product attribute values"
msgstr "Пользователь ввел значения атрибутов специального товара"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__valid_archived_variant_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__valid_archived_variant_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__valid_archived_variant_ids
msgid "Valid Archived Variants"
msgstr "Действительные заархивированные варианты"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__valid_existing_variant_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__valid_existing_variant_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__valid_existing_variant_ids
msgid "Valid Existing Variants"
msgstr "Действительные существующие варианты"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__valid_product_template_attribute_line_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__valid_product_template_attribute_line_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__valid_product_template_attribute_line_ids
msgid "Valid Product Attribute Lines"
msgstr "Действительные строки атрибутов товара"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__valid_product_template_attribute_line_wnva_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__valid_product_template_attribute_line_wnva_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__valid_product_template_attribute_line_wnva_ids
msgid "Valid Product Attribute Lines Without No Variant Attributes"
msgstr "Действительные строки атрибутов товара без варианта атрибутов"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__valid_product_attribute_value_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__valid_product_attribute_value_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__valid_product_attribute_value_ids
msgid "Valid Product Attribute Values"
msgstr "Действительные значения атрибута товара"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__valid_product_attribute_value_wnva_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__valid_product_attribute_value_wnva_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__valid_product_attribute_value_wnva_ids
msgid "Valid Product Attribute Values Without No Variant Attributes"
msgstr "Действительные значения атрибута товара без вариантов атрибутов"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__valid_product_attribute_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__valid_product_attribute_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__valid_product_attribute_ids
msgid "Valid Product Attributes"
msgstr "Действительные атрибуты товара"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__valid_product_attribute_wnva_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__valid_product_attribute_wnva_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__valid_product_attribute_wnva_ids
msgid "Valid Product Attributes Without No Variant Attributes"
msgstr "Действительные атрибуты товара без вариантов атрибутов"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__validity_date
msgid "Validity"
msgstr "Период действия"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__validity_date
msgid "Validity date of the quotation, after this date, the customer won't be able to validate the quotation online."
msgstr "Срок действия предложения, после этой даты, клиент не сможет проверить ценовое предложение онлайн."

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__valuation
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__valuation
#: model:ir.model.fields,field_description:hotel.field_hotel_services__valuation
msgid "Valuation"
msgstr "Оценка"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__stock_value
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__stock_value
#: model:ir.model.fields,field_description:hotel.field_hotel_services__stock_value
msgid "Value"
msgstr "Объём"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__image_variant
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__image_variant
#: model:ir.model.fields,field_description:hotel.field_hotel_services__image_variant
msgid "Variant Image"
msgstr "Изображение варианта"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__price_extra
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__price_extra
#: model:ir.model.fields,field_description:hotel.field_hotel_services__price_extra
msgid "Variant Price Extra"
msgstr "Экстра-цена варианта"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__variant_seller_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__variant_seller_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__variant_seller_ids
msgid "Variant Seller"
msgstr "вариант продавца"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__supplier_taxes_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__supplier_taxes_id
#: model:ir.model.fields,field_description:hotel.field_hotel_services__supplier_taxes_id
msgid "Vendor Taxes"
msgstr "Налоги поставщика"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__seller_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__seller_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__seller_ids
msgid "Vendors"
msgstr "Производители"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__volume
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__volume
#: model:ir.model.fields,field_description:hotel.field_hotel_services__volume
msgid "Volume"
msgstr "Объём"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__warehouse_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room__warehouse_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__warehouse_id
#: model:ir.model.fields,field_description:hotel.field_hotel_services__warehouse_id
msgid "Warehouse"
msgstr "Склад"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio__website_message_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room__website_message_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__website_message_ids
#: model:ir.model.fields,field_description:hotel.field_hotel_services__website_message_ids
msgid "Website Messages"
msgstr "Сообщения с сайта"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__website_message_ids
#: model:ir.model.fields,help:hotel.field_hotel_room__website_message_ids
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__website_message_ids
#: model:ir.model.fields,help:hotel.field_hotel_services__website_message_ids
msgid "Website communication history"
msgstr "История общения с сайта"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__weight
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__weight
#: model:ir.model.fields,field_description:hotel.field_hotel_services__weight
msgid "Weight"
msgstr "Вес"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__weight_uom_id
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__weight_uom_id
#: model:ir.model.fields,field_description:hotel.field_hotel_services__weight_uom_id
msgid "Weight Unit of Measure"
msgstr "Единица измерения веса"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__weight
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__weight
#: model:ir.model.fields,help:hotel.field_hotel_services__weight
msgid "Weight of the product, packaging not included. The unit of measure can be changed in the general settings"
msgstr "Вес изделия, упаковка не входит. Единицу можно изменить в общих настройках"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room__weight_uom_name
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities__weight_uom_name
#: model:ir.model.fields,field_description:hotel.field_hotel_services__weight_uom_name
msgid "Weight unit of measure label"
msgstr "Метка единицы измерения веса"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities_type__property_stock_account_input_categ_id
#: model:ir.model.fields,help:hotel.field_hotel_room_type__property_stock_account_input_categ_id
#: model:ir.model.fields,help:hotel.field_hotel_service_type__property_stock_account_input_categ_id
msgid "When doing real-time inventory valuation, counterpart journal items for all incoming stock moves will be posted in this account, unless there is a specific valuation account set on the source location. This is the default value for all products in this category. It can also directly be set on each product"
msgstr "При проведении оценки инвентаризации в реальном времени, корреспондирующие элементы журнала для всех входящих перемещений запасов будут отправлены на этот счёт, если нет конкретного счёта заданного по исходному месту хранения. Его также можно установить непосредственно для каждого продукта."

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__property_stock_account_input
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__property_stock_account_input
#: model:ir.model.fields,help:hotel.field_hotel_services__property_stock_account_input
msgid "When doing real-time inventory valuation, counterpart journal items for all incoming stock moves will be posted in this account, unless there is a specific valuation account set on the source location. When not set on the product, the one from the product category is used."
msgstr "При проведении оценки инвентаризации в реальном времени, корреспондирующие пункты журнала для всех входящих перемещений запасов будут отправлены на этот счёт, если нет конкретного счёта заданного по исходному месту хранения. Его также можно установить непосредственно для каждого товара."

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities_type__property_stock_account_output_categ_id
#: model:ir.model.fields,help:hotel.field_hotel_room_type__property_stock_account_output_categ_id
#: model:ir.model.fields,help:hotel.field_hotel_service_type__property_stock_account_output_categ_id
msgid "When doing real-time inventory valuation, counterpart journal items for all outgoing stock moves will be posted in this account, unless there is a specific valuation account set on the destination location. This is the default value for all products in this category. It can also directly be set on each product"
msgstr "При проведении оценки инвентаризации в реальном времени, корреспондирующие пункты журнала для всех входящих перемещений запасов будут отправлены на этот счёт, если нет конкретного счёта заданного по месту назначения. Его также можно установить непосредственно для каждого продукта."

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room__property_stock_account_output
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities__property_stock_account_output
#: model:ir.model.fields,help:hotel.field_hotel_services__property_stock_account_output
msgid "When doing real-time inventory valuation, counterpart journal items for all outgoing stock moves will be posted in this account, unless there is a specific valuation account set on the destination location. When not set on the product, the one from the product category is used."
msgstr "При проведении оценки инвентаризации в реальном времени, корреспондирующие элементы журнала для всех исходящих перемещений будут помещены на этот счёт, если нет задан счёт в месте назначения. Его также можно установить непосредственно для каждого продукта."

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities_type__property_stock_journal
#: model:ir.model.fields,help:hotel.field_hotel_room_type__property_stock_journal
#: model:ir.model.fields,help:hotel.field_hotel_service_type__property_stock_journal
msgid "When doing real-time inventory valuation, this is the Accounting Journal in which entries will be automatically posted when stock moves are processed."
msgstr "При оценке стоимости запасов в реальном времени, это бухгалтерский журнал в который будут автоматически добавляться записи при обработке перемещений ТМЦ."

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_room_amenities_type__property_stock_valuation_account_id
#: model:ir.model.fields,help:hotel.field_hotel_room_type__property_stock_valuation_account_id
#: model:ir.model.fields,help:hotel.field_hotel_service_type__property_stock_valuation_account_id
msgid "When real-time inventory valuation is enabled on a product, this account will hold the current value of the products."
msgstr "При оценке запасов в реальном времени, этот счет будет содержать текущую оценку стоимости продуктов."

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__partner_id
#: model:ir.model.fields,help:hotel.field_hotel_folio_line__order_partner_id
#: model:ir.model.fields,help:hotel.field_hotel_service_line__order_partner_id
msgid "You can find a customer by its Name, TIN, Email or Internal Reference."
msgstr "Вы можете найти клиента по его имени, ИНН, электронной почте или внутренним ссылке."

#. module: hotel
#: model:ir.model,name:hotel.model_hotel_room_amenities_type
msgid "amenities Type"
msgstr "Тип удобств"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room_amenities_type__cat_id
msgid "category"
msgstr "Категория"

#. module: hotel
#: model:ir.model,name:hotel.model_hotel_service_line
msgid "hotel Service line"
msgstr "Линия гостиничного обслуживания"

#. module: hotel
#: model:ir.model,name:hotel.model_hotel_folio_line
msgid "hotel folio1 room line"
msgstr ""

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_room_images__room_images_id
msgid "img_ids"
msgstr ""

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_room_form
msgid "max adult"
msgstr "Максимальное количество взрослых"

#. module: hotel
#: model_terms:ir.ui.view,arch_db:hotel.view_hotel_room_form
msgid "max child"
msgstr "Максимальное количество детей"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_folio_line__order_line_id
msgid "order_line_id"
msgstr "Номер строки заказа"

#. module: hotel
#: model:ir.model.fields,field_description:hotel.field_hotel_service_line__service_line_id
msgid "service_line_id"
msgstr "Номер сервисной линии"

#. module: hotel
#: model:ir.model.fields,help:hotel.field_hotel_folio__amount_by_group
msgid "type: [(name, amount, base, formated amount, formated base)]"
msgstr "тип: [(Наименования, Количества, базы, сформированной суммы, сформированной базы)]"

