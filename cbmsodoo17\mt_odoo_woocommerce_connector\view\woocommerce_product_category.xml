<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="woocomm_product_category_list_view_inherit" model="ir.ui.view">
        <field name="name">woocomm.product.category.tree.inherit</field>
        <field name="model">product.category</field>
        <field name="inherit_id" ref="product.product_category_list_view"/>
        <field name="arch" type="xml">
            <xpath expr="//tree" position="attributes">
                <attribute name="js_class">woocomm_import_product_category_button</attribute>
            </xpath>
            <field name="display_name" position="after">
                <field name="wooc_id" readonly="1"/>
                <field name="woocomm_instance_id" readonly="1"/>
                <field name="is_exported" readonly="1"/>
                <field name="wooc_cat_slug"/>
            </field>
        </field>
    </record>

    <record id="woocomm_product_category_form_view_inherit" model="ir.ui.view">
        <field name="name">woocomm.product.category.form.inherit</field>
        <field name="model">product.category</field>
        <field name="inherit_id" ref="product.product_category_form_view"/>
        <field name="arch" type="xml">
            <xpath expr="//group[@name='first']" position="inside">
                <field name="wooc_cat_slug" class="oe_inline"/>
                <field name="wooc_id" readonly="1"/>
                <field name="woocomm_instance_id" readonly="1"/>
                <field name="is_exported" readonly="1"/>
                <field name="wooc_cat_description" string="Description"/>
            </xpath>
        </field>
    </record>

    <record id="woocomm_product_category_search_view_inherit" model="ir.ui.view">
        <field name="name">woocomm.product.category.search.view.inherit</field>
        <field name="model">product.category</field>
        <field name="inherit_id" ref="product.product_category_search_view"/>
        <field name="arch" type="xml">
            <search>
                <filter string="WooCommerce Synced Categories" name="wooc_imported_categories"
                        domain="[('is_exported', '=', True)]"/>
            </search>
        </field>
    </record>

    <record id="action_product_category_woocomm" model="ir.actions.act_window">
        <field name="name">Product Category</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">product.category</field>
        <field name="view_id" ref="product.product_category_list_view"/>
        <field name="context">{'search_default_wooc_imported_categories': 1}</field>
        <field name="view_mode">tree,form</field>
    </record>

</odoo>