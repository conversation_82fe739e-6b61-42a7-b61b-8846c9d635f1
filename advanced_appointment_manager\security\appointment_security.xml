<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- System administrators can see all appointments -->
        <record id="appointment_see_all" model="ir.rule">
            <field name="name">Appointment: See All</field>
            <field name="model_id" ref="calendar.model_calendar_event"/>
            <field name="groups" eval="[(4, ref('base.group_system'))]"/>
            <field name="domain_force">[(1, '=', 1)]</field>
        </record>

        <!-- Regular users can see their own appointments and those they're assigned to -->
        <record id="appointment_see_own_and_assigned" model="ir.rule">
            <field name="name">Appointment: See Own and Assigned</field>
            <field name="model_id" ref="calendar.model_calendar_event"/>
            <field name="groups" eval="[(4, ref('base.group_user'))]"/>
            <field name="domain_force">
                ['|', ('user_id', '=', user.id), ('partner_ids', 'in', [user.partner_id.id])]
            </field>
        </record>

        <!-- Portal users can only see appointments where they are attendees -->
        <record id="appointment_portal_access" model="ir.rule">
            <field name="name">Appointment: Portal Access</field>
            <field name="model_id" ref="calendar.model_calendar_event"/>
            <field name="groups" eval="[(4, ref('base.group_portal'))]"/>
            <field name="domain_force">
                [('partner_ids', 'in', [user.partner_id.id])]
            </field>
        </record>

        <!-- Additional security: Only system admins can modify pending appointments' organizer -->
        <record id="appointment_modify_pending_organizer" model="ir.rule">
            <field name="name">Appointment: Modify Pending Organizer (Admin Only)</field>
            <field name="model_id" ref="calendar.model_calendar_event"/>
            <field name="groups" eval="[(4, ref('base.group_system'))]"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="perm_read" eval="False"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>
    </data>
</odoo>