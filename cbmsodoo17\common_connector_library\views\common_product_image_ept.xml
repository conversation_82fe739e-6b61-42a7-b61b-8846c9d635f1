<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="view_common_product_image_ept_form" model="ir.ui.view">
        <field name="name">common.product.image.ept.form</field>
        <field name="model">common.product.image.ept</field>
        <field name="arch" type="xml">
            <form string="Product Images">
                <field name="sequence" invisible="1"/>
                <div class="row o_website_sale_image_modal">
                    <div class="col-md-6 col-xl-5">
                        <label for="name" string="Image Name"/>
                        <h2>
                            <field name="name" placeholder="Image Name"/>
                        </h2>
                        <label for="url"/>
                        <br/>
                        <field name="url" required="not image"/>
                        <br/>
                    </div>

                    <div class="col-md-6 col-xl-7 text-center o_website_sale_image_modal_container">
                        <div class="row">
                            <div class="col">
                                <field name="image" widget="image" required="url == ''"/>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 col-xl-5"
                         invisible="context.get('default_product_id',False)">
                        <field name="template_id" invisible="1"/>
                        <label for="product_id" string="Product variant"/>
                        <field name="product_id" domain="[('product_tmpl_id','=',template_id)]"/>
                    </div>
                </div>
            </form>
        </field>
    </record>
    <record id="view_common_product_image_ept_kanban" model="ir.ui.view">
        <field name="name">common.product.image.ept.kanban</field>
        <field name="model">common.product.image.ept</field>
        <field name="arch" type="xml">
            <kanban string="Product Images" default_order="sequence">
                <field name="id"/>
                <field name="name"/>
                <field name="image"/>
                <field name="sequence" widget="handle"/>
                <templates>
                    <t t-name="kanban-box">
<!--                        TODO: We are not getting proper image with size in the kanban view-->
                        <div class="oe_kanban_global_click">
                            <div class="o_kanban_image">
                                <img alt="Product" class="o_image_64_contain"
                                     t-att-src="kanban_image('common.product.image.ept', 'image', record.id.raw_value)"/>
                            </div>
                            <div class="oe_kanban_details">
                                <strong class="o_kanban_record_title">
                                    <field name="name"/>
                                </strong>
                                <br/>
                                <t t-set="size_status" t-value="'badge-success'"/>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>
</odoo>
