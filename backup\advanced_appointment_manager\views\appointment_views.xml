<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="view_calendar_event_form_advanced_approval" model="ir.ui.view">
            <field name="name">calendar.event.form.advanced.approval</field>
            <field name="model">calendar.event</field>
            <field name="inherit_id" ref="calendar.view_calendar_event_form"/>
            <field name="arch" type="xml">
                <xpath expr="//sheet" position="before">
                    <header>
                        <button name="action_approve" type="object" string="Approve" class="oe_highlight"
                                invisible="approval_state != 'pending'"/>
                        <button name="action_reject" type="object" string="Reject" class="btn-secondary"
                                invisible="approval_state != 'pending'"/>
                        <button name="action_check_in" type="object" string="Check-In" class="oe_highlight"
                                invisible="checkin_state != 'waiting' or approval_state != 'approved'"/>
                        <button name="action_mark_completed" type="object" string="Mark as Completed" class="oe_highlight"
                                invisible="checkin_state != 'in_progress' or approval_state != 'approved'"/>
                        <button name="action_mark_no_show" type="object" string="Mark as No Show" class="btn-secondary"
                                invisible="(checkin_state not in ('waiting', 'in_progress')) or (approval_state != 'approved')"/>
                        <button name="action_mark_forfeited" type="object" string="Mark as Forfeited" class="btn-secondary"
                                invisible="(checkin_state not in ('waiting', 'in_progress')) or (approval_state != 'approved')"/>

                        <field name="approval_state" widget="statusbar" statusbar_visible="pending,approved,rejected,cancelled"/>
                        <field name="checkin_state" widget="statusbar" invisible="approval_state != 'approved'"/>
                    </header>
                </xpath>
            </field>
        </record>

        <record id="view_calendar_event_tree_advanced_approval" model="ir.ui.view">
            <field name="name">calendar.event.tree.advanced.approval</field>
            <field name="model">calendar.event</field>
            <field name="inherit_id" ref="appointment.calendar_event_view_tree_booking"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='name']" position="after">
                    <field name="approval_state" widget="badge"
                           decoration-success="approval_state == 'approved'"
                           decoration-warning="approval_state == 'pending'"
                           decoration-danger="approval_state == 'rejected'"
                           decoration-muted="approval_state == 'cancelled'"/>
                    <field name="checkin_state" widget="badge" optional="show"
                           decoration-success="checkin_state == 'completed' or checkin_state == 'in_progress'"
                           decoration-info="checkin_state == 'waiting'"
                           decoration-danger="checkin_state == 'no_show' or checkin_state == 'forfeited'"
                           decoration-muted="checkin_state == 'forfeited'"/>
                </xpath>
            </field>
        </record>
    </data>
</odoo>