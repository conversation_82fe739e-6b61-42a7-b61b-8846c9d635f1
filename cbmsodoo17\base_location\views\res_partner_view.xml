<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <record id="view_partner_form" model="ir.ui.view">
        <field name="name">res.partner.zip_id.2</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_partner_form" />
        <field name="arch" type="xml">
            <field name="city" position="before">
                <field
                    name="zip_id"
                    options="{'create_name_field': 'city', 'no_open': True, 'no_create': True}"
                    placeholder="Location completion"
                    readonly="type == 'contact' and parent_id != False"
                />
            </field>
            <xpath
                expr="//field[@name='child_ids']/form//field[@name='city']"
                position="before"
            >
                <field
                    name="zip_id"
                    options="{'create_name_field': 'city', 'no_open': True, 'no_create': True}"
                    placeholder="City completion"
                />
            </xpath>
        </field>
    </record>
</odoo>
