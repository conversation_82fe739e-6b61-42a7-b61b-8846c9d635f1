<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="inherited_stock_return_picking_form" model="ir.ui.view">
        <field name="name">stock.return.picking.inherited.form</field>
        <field name="model">stock.return.picking</field>
        <field name="inherit_id" ref="stock.view_stock_return_picking_form"/>
        <field name="arch" type="xml">
            <field name="picking_id" position="after">
                <field name="sale_order_ept_id" invisible="1"/>
            </field>
            <button name="create_returns" position="attributes">
                <attribute name="invisible">sale_order_ept_id</attribute>
            </button>
            <button name="create_returns" position="after">
                <button name="create_returns_ept" string="Return" type="object" class="btn-primary"
                        invisible="picking_id"/>
            </button>
        </field>
    </record>
</odoo>
