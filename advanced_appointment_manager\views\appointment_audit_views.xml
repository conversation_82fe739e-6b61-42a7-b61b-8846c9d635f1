<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Audit Log Tree View -->
        <record id="view_appointment_audit_log_tree" model="ir.ui.view">
            <field name="name">appointment.audit.log.tree</field>
            <field name="model">appointment.audit.log</field>
            <field name="arch" type="xml">
                <tree string="Appointment Audit Log" create="false" edit="false" delete="false">
                    <field name="create_date" string="Date/Time"/>
                    <field name="appointment_id"/>
                    <field name="action_type"/>
                    <field name="action_description"/>
                    <field name="changed_by"/>
                    <field name="old_user_id" optional="hide"/>
                    <field name="new_user_id" optional="hide"/>
                    <field name="old_approval_state" optional="hide"/>
                    <field name="new_approval_state" optional="hide"/>
                </tree>
            </field>
        </record>

        <!-- Audit Log Form View -->
        <record id="view_appointment_audit_log_form" model="ir.ui.view">
            <field name="name">appointment.audit.log.form</field>
            <field name="model">appointment.audit.log</field>
            <field name="arch" type="xml">
                <form string="Appointment Audit Log" create="false" edit="false" delete="false">
                    <sheet>
                        <group>
                            <group>
                                <field name="appointment_id"/>
                                <field name="action_type"/>
                                <field name="changed_by"/>
                                <field name="create_date" string="Date/Time"/>
                            </group>
                            <group>
                                <field name="ip_address"/>
                                <field name="user_agent"/>
                            </group>
                        </group>
                        
                        <group string="Action Details">
                            <field name="action_description" colspan="2"/>
                            <field name="change_reason" colspan="2"/>
                        </group>
                        
                        <notebook>
                            <page string="Staff Changes" invisible="action_type != 'staff_change'">
                                <group>
                                    <field name="old_user_id"/>
                                    <field name="new_user_id"/>
                                </group>
                            </page>
                            
                            <page string="Attendee Changes" invisible="action_type != 'attendee_change'">
                                <group>
                                    <field name="old_attendee_ids" widget="many2many_tags"/>
                                    <field name="new_attendee_ids" widget="many2many_tags"/>
                                </group>
                            </page>
                            
                            <page string="Status Changes" invisible="action_type not in ['approval_change', 'checkin_change']">
                                <group>
                                    <field name="old_approval_state" invisible="action_type != 'approval_change'"/>
                                    <field name="new_approval_state" invisible="action_type != 'approval_change'"/>
                                    <field name="old_checkin_state" invisible="action_type != 'checkin_change'"/>
                                    <field name="new_checkin_state" invisible="action_type != 'checkin_change'"/>
                                </group>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Audit Log Search View -->
        <record id="view_appointment_audit_log_search" model="ir.ui.view">
            <field name="name">appointment.audit.log.search</field>
            <field name="model">appointment.audit.log</field>
            <field name="arch" type="xml">
                <search string="Appointment Audit Log">
                    <field name="appointment_id"/>
                    <field name="action_type"/>
                    <field name="changed_by"/>
                    <field name="action_description"/>
                    
                    <filter string="Staff Changes" name="staff_changes" domain="[('action_type', '=', 'staff_change')]"/>
                    <filter string="Approval Changes" name="approval_changes" domain="[('action_type', '=', 'approval_change')]"/>
                    <filter string="Attendee Changes" name="attendee_changes" domain="[('action_type', '=', 'attendee_change')]"/>
                    
                    <separator/>
                    <filter string="Today" name="today" domain="[('create_date', '>=', datetime.datetime.combine(context_today(), datetime.time(0,0,0)))]"/>
                    <filter string="This Week" name="this_week" domain="[('create_date', '>=', (context_today() - datetime.timedelta(days=7)).strftime('%Y-%m-%d'))]"/>
                    <filter string="This Month" name="this_month" domain="[('create_date', '>=', (context_today().replace(day=1)).strftime('%Y-%m-%d'))]"/>
                    
                    <group expand="0" string="Group By">
                        <filter string="Action Type" name="group_action_type" context="{'group_by': 'action_type'}"/>
                        <filter string="Changed By" name="group_changed_by" context="{'group_by': 'changed_by'}"/>
                        <filter string="Date" name="group_date" context="{'group_by': 'create_date:day'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- Audit Log Action -->
        <record id="action_appointment_audit_log" model="ir.actions.act_window">
            <field name="name">Appointment Audit Log</field>
            <field name="res_model">appointment.audit.log</field>
            <field name="view_mode">tree,form</field>
            <field name="search_view_id" ref="view_appointment_audit_log_search"/>
            <field name="context">{'search_default_this_week': 1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No audit logs found!
                </p>
                <p>
                    This view shows all changes made to appointments, including staff assignments,
                    approval status changes, and attendee modifications.
                </p>
            </field>
        </record>

        <!-- Menu Item for Audit Log (only visible to system admins) -->
        <menuitem id="menu_appointment_audit_log"
                  name="Audit Log"
                  parent="calendar.mail_menu_calendar"
                  action="action_appointment_audit_log"
                  groups="base.group_system"
                  sequence="50"/>

        <!-- Add Audit Log to Appointment Form View -->
        <record id="view_calendar_event_form_audit_log" model="ir.ui.view">
            <field name="name">calendar.event.form.audit.log</field>
            <field name="model">calendar.event</field>
            <field name="inherit_id" ref="view_calendar_event_form_advanced_approval"/>
            <field name="arch" type="xml">
                <xpath expr="//notebook" position="inside">
                    <page string="Audit Log" groups="base.group_system">
                        <field name="audit_log_ids" readonly="1">
                            <tree create="false" edit="false" delete="false">
                                <field name="create_date" string="Date/Time"/>
                                <field name="action_type"/>
                                <field name="action_description"/>
                                <field name="changed_by"/>
                            </tree>
                        </field>
                    </page>
                </xpath>
            </field>
        </record>
    </data>
</odoo>
