<?xml version="1.0" encoding="utf-8"?>
<odoo>
    
    <record id="view_woocomm_product_image_form" model="ir.ui.view">
        <field name="name">woocommerce.product.image.form</field>
        <field name="model">woocommerce.product.image</field>
        <field name="arch" type="xml">
            <form string="WooCommerce Images Details">
                <sheet>
                    <group>
                        <group>
                            <field name="wooc_id" readonly="1" />
                        </group>
                        <group>
                            <field name="name" />
                            <field name="is_main_image"
                            readonly = "is_main_image != False or wooc_id == False" force_save="1" />
                        </group>
                    </group>
                    <group>
                        <field name="wooc_url" readonly="1" invisible = "wooc_url != False"/>
                    </group>                    
                    <group>
                        <field name="product_template_id" readonly="1"/>
                        <!-- <field name="product_variant_id" /> -->
                        <field name="product_image_variant_ids" widget="many2many_tags" options="{'no_create': True}" />
                        <field name="wooc_image"  filename="name"/>
                        <field name="wooc_image"  widget="image" readonly="1"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="view_woocomm_product_image_kanban" model="ir.ui.view">
        <field name="name">woocommerce.product.image.kanban</field>
        <field name="model">woocommerce.product.image</field>
        <field name="arch" type="xml">
            <kanban string="Product WooCommerce Images">
                <field name="name" />
                <field name="wooc_id" />
                <field name="wooc_image" />
                <field name="product_template_id" />
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="oe_kanban_global_click">
                            <div class="o_kanban_image">
                                <t t-if="record.wooc_image.value">
                                    <img alt="Product Image"
                                        t-att-src="kanban_image('woocommerce.product.image', 'wooc_image',record.id.raw_value)" />
                                </t>

                            </div>
                            <div class="oe_kanban_details">
                                <strong class="o_kanban_record_title">
                                    <field name="name" />
                                </strong>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

</odoo>