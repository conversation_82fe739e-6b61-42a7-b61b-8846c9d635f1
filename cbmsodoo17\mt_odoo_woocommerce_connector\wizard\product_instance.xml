<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="view_woocomm_export_product_instance_form" model="ir.ui.view">
        <field name="name">woocomm.product.instance.form.view</field>
        <field name="model">woocomm.product.instance.exp</field>
        <field name="arch" type="xml">
            <form string="Select Instance">
                <sheet>

                    <div>
                        <p colspan="2" class="alert alert-warning" role="alert">
                            <u>
                                <h3 style="font-weight:bold;color:#7d5a29">Note :</h3>
                            </u>
                            <b>
                                Are you sure to export new products to this instance?
                            </b>
                        </p>
                    </div>

                    <group>
                        <field name="woocomm_instance_id" required="1" options="{'no_create':True,'no_create_edit':True}"/>
                    </group>
                    <group>
                        <field name="force_update_product"/>
                        <field name="force_update_image" invisible = "force_update_product != True"/>
                    </group>
                    <footer>
                        <button name="product_instance_selected_for_exp" string="Export Products" type="object"
                                class="oe_highlight"/>
                        <button string="Cancel" class="oe_highlight" special="cancel"/>
                    </footer>

                </sheet>
            </form>
        </field>
    </record>

    <record id="view_woocomm_import_product_instance_form" model="ir.ui.view">
        <field name="name">woocomm.product.instance.import.form.view</field>
        <field name="model">woocomm.product.instance.imp</field>
        <field name="arch" type="xml">
            <form string="Select Instance">
                <sheet>
                    <div>
                        <p colspan="2" class="alert alert-warning" role="alert">
                            <u>
                                <h3 style="font-weight:bold;color:#7d5a29">Note :</h3>
                            </u>
                            <b>
                                Are you sure to import products from this instance?
                            </b>
                        </p>
                    </div>

                    <group>
                        <field name="woocomm_instance_id" required="1" options="{'no_create':True,'no_create_edit':True}"/>
                    </group>
                    <group>
                        <field name="is_force_update"/>
                    </group>
                    <footer>
                        <button name="product_instance_selected_for_imp" string="Import Products" type="object"
                                class="oe_highlight"/>
                        <button string="Cancel" class="oe_highlight" special="cancel"/>
                    </footer>

                </sheet>
            </form>
        </field>
    </record>

    <record id="action_wizard_woocomm_product_instance" model="ir.actions.act_window">
        <field name="name">WooCommerce - Export Products</field>
        <field name="res_model">woocomm.product.instance.exp</field>
        <field name="binding_model_id" ref="model_product_template"/>
        <field name="binding_view_types">form,list</field>
        <field name="target">new</field>
        <field name="view_id" ref="mt_odoo_woocommerce_connector.view_woocomm_export_product_instance_form"/>
    </record>

    <record id="action_wizard_woocomm_import_product_instance" model="ir.actions.act_window">
        <field name="name">WooCommerce - Import Products</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">woocomm.product.instance.imp</field>
        <field name="view_id" ref="view_woocomm_import_product_instance_form"/>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>

</odoo>
