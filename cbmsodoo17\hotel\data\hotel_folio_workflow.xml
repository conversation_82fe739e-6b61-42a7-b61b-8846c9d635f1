<?xml version="1.0" encoding="utf-8"?>
<odoo>
	<data>
		<record model="workflow" id="wkf_hotel">
			<field name="name">hotel.folio.basic</field>
			<field name="osv">hotel.folio</field>
			<field name="on_create">True</field>
		</record>
	
	#----------------------------------------------
	# Activity
	#----------------------------------------------
	
		<record model="workflow.activity" id="act_draft">
			<field name="wkf_id" ref="wkf_hotel"/>
			<field name="flow_start">True</field>
			<field name="name">draft</field>
		</record>
						
		<record model="workflow.activity" id="act_router">
			<field name="wkf_id" ref="wkf_hotel"/>
			<field name="name">router</field>
			<field name="kind">function</field>
			<field name="action">action_wait()</field>
			<field name="split_mode">OR</field>
		</record>
		<record model="workflow.activity" id="act_wait_invoice">
			<field name="wkf_id" ref="wkf_hotel"/>
			<field name="name">wait_invoice</field>
		</record>

		<record model="workflow.activity" id="act_done">
			<field name="wkf_id" ref="wkf_hotel"/>
			<field name="name">done</field>
			<field name="flow_stop">True</field>
			<field name="kind">function</field>
			<field name="action">write({'state':'done'})</field>
			<field name="join_mode">AND</field>
		</record>
		<record model="workflow.activity" id="act_cancel">
			<field name="wkf_id" ref="wkf_hotel"/>
			<field name="name">cancel</field>
			<field name="flow_stop">True</field>
			<field name="kind">stopall</field>
			<field name="action">action_cancel()</field>
		</record>
		<record model="workflow.activity" id="act_cancel2">
			<field name="wkf_id" ref="wkf_hotel"/>
			<field name="name">cancel2</field>
			<field name="flow_stop">True</field>
			<field name="kind">stopall</field>
			<field name="action">action_cancel()</field>
		</record>
		<record model="workflow.activity" id="act_cancel3">
			<field name="wkf_id" ref="wkf_hotel"/>
			<field name="name">cancel3</field>
			<field name="flow_stop">True</field>
			<field name="kind">stopall</field>
			<field name="action">action_cancel()</field>
		</record>

		<record model="workflow.activity" id="act_invoice">
			<field name="wkf_id" ref="wkf_hotel"/>
			<field name="name">invoice</field>
			<field name="kind">subflow</field>
			<field name="subflow_id" search="[('name','=','account.invoice.basic')]"/>
			<field name="action">action_invoice_create()</field>
		</record>
		<record model="workflow.activity" id="act_invoice_except">
			<field name="wkf_id" ref="wkf_hotel"/>
			<field name="name">invoice_except</field>
			<field name="kind">function</field>
			<field name="action">action_invoice_cancel()</field>
		</record>
		<record model="workflow.activity" id="act_invoice_end">
			<field name="wkf_id" ref="wkf_hotel"/>
			<field name="name">invoice_end</field>
			<field name="kind">dummy</field>
		</record>
		<record model="workflow.activity" id="act_invoice_cancel">
			<field name="wkf_id" ref="wkf_hotel"/>
			<field name="name">invoice_cancel</field>
			<field name="flow_stop">True</field>
			<field name="kind">stopall</field>
			<field name="action">action_cancel()</field>
		</record>

	#----------------------------------------------
	# Transistion
	#----------------------------------------------
	
		<record model="workflow.transition" id="trans_invoice_end_done">
			<field name="act_from" ref="act_invoice_end"/>
			<field name="act_to" ref="act_done"/>
		</record>
		<record model="workflow.transition" id="trans_draft_router">
			<field name="act_from" ref="act_draft"/>
			<field name="act_to" ref="act_router"/>
			<field name="signal">order_confirm</field>
		</record>
		<record model="workflow.transition" id="trans_draft_cancel">
			<field name="act_from" ref="act_draft"/>
			<field name="act_to" ref="act_cancel"/>
			<field name="signal">cancel</field>
		</record>
		<record model="workflow.transition" id="trans_router_wait_invoice_shipping">
			<field name="act_from" ref="act_wait_invoice"/>
			<field name="act_to" ref="act_invoice_end"/>
		</record>


		<record model="workflow.transition" id="trans_router_wait_invoice">
			<field name="act_from" ref="act_router"/>
			<field name="act_to" ref="act_wait_invoice"/>
		</record>

		<record model="workflow.transition" id="trans_wait_invoice_cancel2">
			<field name="act_from" ref="act_wait_invoice"/>
			<field name="act_to" ref="act_cancel2"/>
			<field name="signal">cancel</field>
		</record>
		
		<record model="workflow.transition" id="trans_wait_invoice_invoice">
			<field name="act_from" ref="act_wait_invoice"/>
			<field name="act_to" ref="act_invoice"/>
			<field name="condition">(shipped)</field>
		</record>
		
		<record model="workflow.transition" id="trans_wait_invoice_invoice_manual">
			<field name="act_from" ref="act_wait_invoice"/>
			<field name="act_to" ref="act_invoice"/>
			<field name="signal">manual_invoice</field>
		</record>

		<record model="workflow.transition" id="trans_invoice_invoice_end">
			<field name="act_from" ref="act_invoice"/>
			<field name="act_to" ref="act_invoice_end"/>
			<field name="signal">subflow.paid</field>
		</record>
		<record model="workflow.transition" id="trans_invoice_invoice_except">
			<field name="act_from" ref="act_invoice"/>
			<field name="act_to" ref="act_invoice_except"/>
			<field name="signal">subflow.cancel</field>
		</record>
		<record model="workflow.transition" id="trans_invoice_except_invoice">
			<field name="act_from" ref="act_invoice_except"/>
			<field name="act_to" ref="act_invoice"/>
			<field name="signal">invoice_recreate</field>
		</record>
		<record model="workflow.transition" id="trans_invoice_except_invoice_end">
			<field name="act_from" ref="act_invoice_except"/>
			<field name="act_to" ref="act_invoice_end"/>
			<field name="signal">invoice_corrected</field>
		</record>
		<record model="workflow.transition" id="trans_invoice_except_invoice_cancel">
			<field name="act_from" ref="act_invoice_except"/>
			<field name="act_to" ref="act_invoice_cancel"/>
			<field name="signal">invoice_cancel</field>
		</record>
		
	</data>
</odoo>