<?xml version="1.0"?>
<odoo>
    <data>
        <menuitem id="hotel_management_menu" name="Hotel Management"
                  web_icon="hotel,static/description/hotel_management_icon.png"/>
        <menuitem id="hotel_configuration_menu" name="Configuration"
                  sequence="7" parent="hotel_management_menu"/>
        <menuitem id="hotel_report_menu" name="Reports" sequence="6"
                  parent="hotel_management_menu"/>
        <!--==================================================== Floor ==================================================== -->
        <record model="ir.ui.view" id="view_hotel_floor_form">
            <field name="name">hotel.floor.form</field>
            <field name="model">hotel.floor</field>
            <field name="arch" type="xml">
                <form string=" Hotel Floor">
                    <sheet>
                        <group col="4">
                            <field name="name"/>
                            <field name="sequence"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>
        <record model="ir.ui.view" id="view_hotel_floor_tree">
            <field name="name">hotel.floor.tree</field>
            <field name="model">hotel.floor</field>
            <field name="arch" type="xml">
                <tree string=" Hotel Floors" editable="bottom">
                    <field name="name" colspan="1"/>
                    <field name="sequence"/>
                </tree>
            </field>
        </record>
        <record id="view_hotel_floor_kanban" model="ir.ui.view">
            <field name="name">hotel.floor.kanban</field>
            <field name="model">hotel.floor</field>
            <field name="arch" type="xml">
                <kanban>
                    <field name="name"/>
                    <field name="sequence"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div t-attf-class="oe_kanban_global_click">
                                <div class="oe_kanban_details">
                                    <ul>
                                        <li>
                                            <strong class="o_kanban_record_title">
                                                <field name="name"/>
                                            </strong>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>
        <record model="ir.actions.act_window" id="open_hotel_floor_form_tree">
            <field name="name">Floor Structure</field>
            <field name="res_model">hotel.floor</field>
            <!--<field name="view_type">form</field>-->
            <field name="view_mode">tree,kanban</field>
        </record>
        <menuitem name="Floor" id="menu_open_hotel_floor_form_tree"
                  action="hotel.open_hotel_floor_form_tree" sequence="1"
                  parent="hotel_configuration_menu"/>
        <!--====================================================================
			Amenities Type ==================================================================== -->
        <record model="ir.ui.view" id="view_hotel_room_amenities_type_form">
            <field name="name">hotel.room_amenities_type_form</field>
            <field name="model">hotel.room_amenities_type</field>
            <field name="arch" type="xml">
                <form string="Hotel Room Amenities Type">
                    <sheet>
                        <div class="oe_title">
                            <label for="name" class="oe_edit_only"/>
                            <h1>
                                <field name="name"/>
                            </h1>
                        </div>
                        <group>
                            <group name="parent" colspan="4">
                                <field name="parent_id" domain="[('isamenitype','=',True)]"/>
                                <!-- <field name="type"/> -->
                                <field name="isamenitype"/>
                                <field name="company_id"/>
                            </group>
                            <group name="account_property" string="Account Properties"
                                   colspan="2">
                                <field name="property_account_income_categ_id" domain="[('deprecated','=',False)]"/>
                                <field name="property_account_expense_categ_id" domain="[('deprecated','=',False)]"/>
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="view_amenities_type_kanban" model="ir.ui.view">
            <field name="name">hotel.room_amenities_type.kanban</field>
            <field name="model">hotel.room_amenities_type</field>
            <field name="arch" type="xml">
                <kanban>
                    <field name="name"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div t-attf-class="oe_kanban_global_click">
                                <div class="oe_kanban_details">
                                    <ul>
                                        <li>
                                            <strong class="o_kanban_record_title">
                                                <field name="name"/>
                                            </strong>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <record model="ir.actions.act_window" id="action_hotel_room_amenities_type_view_form">
            <field name="name">Hotel Room Amenities Type</field>
            <field name="res_model">hotel.room_amenities_type</field>
            <!--<field name="view_type">form</field>-->
            <field name="view_mode">tree,form,kanban</field>
            <field name="domain">[('company_id', 'in', context.get('allowed_company_ids'))]</field>
        </record>
        <menuitem name="Amenity Defination" id="menu_parent_amenity"
                  sequence="3" parent="hotel_configuration_menu"/>
        <menuitem name="Amenity Category"
                  id="menu_action_hotel_room_amenities_type_view_form"
                  action="hotel.action_hotel_room_amenities_type_view_form"
                  sequence="3" parent="menu_parent_amenity"/>
        <!--=============================================================== Room
			Amenities =============================================================== -->
        <record model="ir.ui.view" id="view_hotel_room_amenities_list">
            <field name="name">hotel.room_amenities_list</field>
            <field name="model">hotel.room_amenities</field>
            <field name="arch" type="xml">
                <tree string="Hotel Room Amenities">
                    <field name="name"/>
                    <field name="categ_id"/>
                    <field name="list_price" string="Amenity rate"/>
                </tree>
            </field>
        </record>
        <record model="ir.actions.act_window" id="action_hotel_room_amenities_view_form">
            <field name="name">Hotel Room Amenities</field>
            <field name="res_model">hotel.room_amenities</field>
            <!--<field name="view_type">form</field>-->
            <field name="view_mode">tree,form,kanban</field>
            <field name="domain">[('company_id', 'in', context.get('allowed_company_ids'))]</field>
            <field name="view_id" ref="hotel.view_hotel_room_amenities_list"/>
        </record>

        <record id="view_amenities_kanban" model="ir.ui.view">
            <field name="name">hotel.room_amenities.kanban</field>
            <field name="model">hotel.room_amenities</field>
            <field name="arch" type="xml">
                <kanban>
                    <field name="image_1920" widget="image" options="{'size': 'medium'}"/>
                    <field name="name"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div t-attf-class="oe_kanban_global_click">
                                <div class="oe_kanban_details">
                                    <ul>
                                        <div class="o_kanban_image">
                                            <field name="image_1920" widget="image"/>
                                        </div>
                                        <div class="o_kanban_record_title">
                                            <field name="name"/>
                                        </div>
                                    </ul>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <menuitem name="Amenities" id="menu_action_hotel_room_amenities_view_form"
                  action="hotel.action_hotel_room_amenities_view_form" sequence="2"
                  parent="menu_parent_amenity"/>
        <!--======================================================== Room Type
			======================================================== -->
        <record model="ir.ui.view" id="view_hotel_room_type_tree">
            <field name="name">hotel.room_type.tree</field>
            <field name="model">hotel.room_type</field>
            <field name="arch" type="xml">
                <tree string="Room Type">
                    <field name="name"/>
                    <field name="company_id" invisible="1"/>
                </tree>
            </field>
        </record>
        <record model="ir.ui.view" id="view_hotel_room_type_form">
            <field name="name">hotel.room_type.form</field>
            <field name="model">hotel.room_type</field>
            <field name="arch" type="xml">
                <form string="Hotel Room Type">
                    <sheet>
                        <div class="oe_title">
                            <label for="name" class="oe_edit_only"/>
                            <h1>
                                <field name="name"/>
                            </h1>
                        </div>
                        <group>
                            <group name="parent" colspan="4">
                                <field name="parent_id" domain="[('isroomtype','=',True)]"/>
                                <!-- <field name="type"/> -->
                                <field name="isroomtype"/>
                                <field name="company_id"/>
                            </group>
                            <group name="account_property" string="Account Properties"
                                   colspan="2">
                                <field name="property_account_income_categ_id" domain="[('deprecated','=',False)]"/>
                                <field name="property_account_expense_categ_id" domain="[('deprecated','=',False)]"/>
                            </group>
                        </group>
                        <notebook>
                            <page string="Description">
                                <field name="description"/>
                            </page>
                            <page string="Image Gallery">
                                <field name="img_ids"/>
                            </page>
                            <page string="Room Amenities">
                                <separator string=" Room Amenities"/>
                                <field name="room_type_amenities"/>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="view_room_type_kanban" model="ir.ui.view">
            <field name="name">hotel.room_type.kanban</field>
            <field name="model">hotel.room_type</field>
            <field name="arch" type="xml">
                <kanban>
                    <field name="name"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div t-attf-class="oe_kanban_global_click">
                                <div class="oe_kanban_details">
                                    <ul>
                                        <strong class="o_kanban_record_title">
                                            <field name="name"/>
                                        </strong>
                                    </ul>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <record model="ir.actions.act_window" id="open_hotel_room_type_form_tree">
            <field name="name">Room Type</field>
            <field name="res_model">hotel.room_type</field>
            <!--<field name="view_type">form</field>-->
            <field name="view_mode">tree,form,kanban</field>
            <field name="domain">[('company_id', 'in', context.get('allowed_company_ids'))]</field>
        </record>
        <menuitem name="Room Definations" id="menu_open_hotel_room_type_parent"
                  sequence="6" parent="hotel_configuration_menu"/>
        <menuitem name="Room Categories" id="menu_open_hotel_room_type_form_tree"
                  action="hotel.open_hotel_room_type_form_tree" sequence="7"
                  parent="menu_open_hotel_room_type_parent"/>
        <!-- =============================Room Type Images=========================================== -->
        <record model="ir.ui.view" id="view_hotel_room_images_form">
            <field name="name">hotel.room.images.form</field>
            <field name="model">hotel.room.images</field>
            <field name="arch" type="xml">
                <form string="Hotel Room Image Gallery" version="7.0">
                    <sheet>
                        <div class="oe_title">
                            <label for="name" class="oe_edit_only"/>
                            <h1>
                                <field name="name"/>
                            </h1>
                        </div>
                        <group>
                            <field name="img" widget='image' class="oe_avatar"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>
        <record model="ir.ui.view" id="view_room_images_tree">
            <field name="name">hotel.room.images.tree</field>
            <field name="model">hotel.room.images</field>
            <field name="arch" type="xml">
                <tree string="Hotel Room Image Gallery">
                    <field name="name"/>
                </tree>
            </field>
        </record>
        <!-- ======================================================================================== -->
        <!--=================================================== Rooms =================================================== -->
        <record id="view_hotel_room_form" model="ir.ui.view">
            <field name="name">hotel.room_form</field>
            <field name="model">hotel.room</field>
            <field name="mode">primary</field>
            <field eval="110" name="priority"/>
            <field name="inherit_id" ref="product.product_template_form_view"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='categ_id']" position="after">
                    <field name="floor_id" string="Floor"/>
                    <field name="max_adult" string="max adult"/>
                    <field name="max_child" string="max child"/>
                </xpath>
                <xpath expr="//field[@name='categ_id']" position="attributes">
                    <attribute name="domain">[('isroomtype','=',True)]</attribute>
                </xpath>
                <xpath expr="//page[@name='sales']" position="after">
                    <page name="room_amenities" string="Room Amenities">
                        <separator string=" Room Amenities"/>
                        <field name="room_amenities" colspan="4" nolabel="1"/>
                    </page>
                </xpath>
            </field>
        </record>
        <record model="ir.ui.view" id="view_hotel_room_tree">
            <field name="name">hotel.room.tree</field>
            <field name="model">hotel.room</field>
            <field name="arch" type="xml">
                <tree string="Hotel Room">
                    <field name="name"/>
                    <field name="categ_id"/>
                    <field name="floor_id" string="Floor"/>
                    <field name="list_price" string="Room rate"/>
                    <field name="state" string="Room Status" invisible="1"/>
                </tree>
            </field>
        </record>

        <record id="view_room_kanban" model="ir.ui.view">
            <field name="name">hotel.room.kanban</field>
            <field name="model">hotel.room</field>
            <field name="arch" type="xml">
                <kanban>
                    <field name="image_1920" widget="image" options="{'size': 'medium'}"/>
                    <field name="name"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div t-attf-class="oe_kanban_global_click">
                                <div class="oe_kanban_details">
                                    <ul>
                                        <div class="o_kanban_image">
                                            <field name="image_1920" widget="image"/>
                                        </div>
                                        <div class="o_kanban_record_title">
                                            <field name="name"/>
                                        </div>
                                    </ul>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <record model="ir.actions.act_window" id="action_hotel_room_form">
            <field name="name">Hotel Room</field>
            <field name="res_model">hotel.room</field>
            <!--<field name="view_type">form</field>-->
            <field name="view_mode">tree,form,kanban</field>
            <field name="domain">[('company_id', 'in', context.get('allowed_company_ids'))]</field>
        </record>
        <menuitem name="Rooms" id="menu_open_hotel_room_form" action="hotel.action_hotel_room_form"
                  sequence="5" parent="menu_open_hotel_room_type_parent"/>
        <!-- Services -->
        <record model="ir.ui.view" id="view_hotel_service_type_form">
            <field name="name">hotel.service_type.form</field>
            <field name="model">hotel.service_type</field>
            <field name="arch" type="xml">
                <form string="Service Type" version="7.0">
                    <sheet>
                        <div class="oe_title">
                            <label for="name" class="oe_edit_only"/>
                            <h1>
                                <field name="name"/>
                            </h1>
                        </div>
                        <group>
                            <group name="parent" colspan="4">
                                <field name="parent_id" domain="[('isservicetype','=',True)]"/>
                                <field name="isservicetype"/>
                                <field name="company_id"/>
                            </group>
                            <group name="account_property" string="Account Properties"
                                   colspan="2">
                                <field name="property_account_income_categ_id" domain="[('deprecated','=',False)]"/>
                                <field name="property_account_expense_categ_id" domain="[('deprecated','=',False)]"/>
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="view_service_type_kanban" model="ir.ui.view">
            <field name="name">hotel.service_type.kanban</field>
            <field name="model">hotel.service_type</field>
            <field name="arch" type="xml">
                <kanban>
                    <field name="name"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div t-attf-class="oe_kanban_global_click">
                                <div class="oe_kanban_details">
                                    <ul>
                                        <strong class="o_kanban_record_title">
                                            <field name="name"/>
                                        </strong>
                                    </ul>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <record model="ir.actions.act_window" id="open_hotel_service_type_form_tree">
            <field name="name">Service Type</field>
            <field name="res_model">hotel.service_type</field>
            <!--<field name="view_type">form</field>-->
            <field name="view_mode">tree,form,kanban</field>
            <field name="domain">[('company_id', 'in', context.get('allowed_company_ids'))]</field>
        </record>
        <menuitem name="Service Definations"
                  id="menu_open_hotel_service_type_form_tree_parent" sequence="9"
                  parent="hotel_configuration_menu"/>
        <menuitem name="Service Categories" id="menu_open_hotel_service_type_form_tree"
                  action="hotel.open_hotel_service_type_form_tree" sequence="10"
                  parent="menu_open_hotel_service_type_form_tree_parent"/>

        <record id="view_hotel_services_form" model="ir.ui.view">
            <field name="name">hotel.services_form</field>
            <field name="model">hotel.services</field>
            <field name="mode">primary</field>
            <field eval="7" name="priority"/>
            <field name="inherit_id" ref="product.product_template_form_view"/>
            <field name="arch" type="xml">
                <form position="attributes">
                    <attribute name="string">Product Variant</attribute>
                </form>
                <xpath expr="//field[@name='categ_id']" position="attributes">
                    <attribute name="domain">[('isservicetype','=',True)]</attribute>
                </xpath>
            </field>
        </record>

        <record model="ir.ui.view" id="view_hotel_services_tree">
            <field name="name">hotel.services.tree</field>
            <field name="model">hotel.services</field>
            <field name="arch" type="xml">
                <tree string="Hotel Services">
                    <field name="name"/>
                    <field name="categ_id"/>
                    <field name="list_price" string="Service rate"/>
                </tree>
            </field>
        </record>

        <record id="view_services_kanban" model="ir.ui.view">
            <field name="name">hotel.services.kanban</field>
            <field name="model">hotel.services</field>
            <field name="arch" type="xml">
                <kanban>
                    <field name="image_1920" widget="image" options="{'size': 'medium'}"/>
                    <field name="name"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div t-attf-class="oe_kanban_global_click">
                                <div class="oe_kanban_details">
                                    <ul>
                                        <div class="o_kanban_image">
                                            <field name="image_1920" widget="image"/>
                                        </div>
                                        <div class="o_kanban_record_title">
                                            <field name="name"/>
                                        </div>
                                    </ul>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <record model="ir.actions.act_window" id="action_hotel_services_form">
            <field name="name">Hotel Services</field>
            <field name="res_model">hotel.services</field>
            <!--<field name="view_type">form</field>-->
            <field name="view_mode">tree,form,kanban</field>
            <field name="domain">[('company_id', 'in', context.get('allowed_company_ids'))]</field>
        </record>
        <menuitem name="Services" id="menu_open_hotel_services_form"
                  action="hotel.action_hotel_services_form" sequence="8"
                  parent="menu_open_hotel_service_type_form_tree_parent"/>
        <!--======================================================== Hotel Folio
			======================================================== -->
        <record model="ir.ui.view" id="view_hotel_folio1_form">
            <field name="name">hotel.folio.form</field>
            <field name="model">hotel.folio</field>
            <field name="arch" type="xml">
                <form name="Folio">
                    <header>
                        <button name="action_confirm" invisible="state != 'draft'" string="Confirm Folio"
                                confirm="Do you want to confirm?" type="object" class="oe_highlight"
                                groups="base.group_user"/>
                        <button name="action_cancel" string="Cancel Folio" invisible="state != 'draft'" type="object"
                                class="oe_highlight"/>
                        <field name="state"/>
                    </header>
                    <sheet>
                        <div name="button_box" class="oe_button_box">
                            <button name="action_view_invoice"
                                    type="object"
                                    class="oe_stat_button"
                                    icon="fa-pencil-square-o">
                                <field name="invoice_count" widget="statinfo" string="Invoices"/>
                            </button>
                        </div>
                        <div class="oe_title">
                            <h1>
                                <field name="name" readonly="1"/>
                            </h1>
                        </div>
                        <notebook>
                            <page name="Folio" string="Folio">
                                <group colspan="4" col="4">
                                    <field name="company_id" invisible="1"/>
                                    <field name="partner_id" required="1" string="Guest Name"/>
                                    <field name="partner_invoice_id"/>
                                    <field name="pricelist_id"/>
                                    <field name="partner_shipping_id"/>
                                </group>
                                <group>
                                    <field name="note"/>
                                </group>
                                <separator string="Room Lines"/>
                                <field name="room_lines" readonly="state in ['sale']" colspan="4" string="Room Lines"
                                       nolabel="1">
                                    <form name="Room Line">
                                        <notebook>
                                            <page name="Folio Line" string="Folio Line">
                                                <group colspan="4" col="4">
                                                    <field name="checkin_date"/>
                                                    <field name="checkout_date"/>
                                                </group>
                                                <separator string="Automatic Declaration" colspan="4"/>
                                                <group colspan="4" col="6">
                                                    <field name="categ_id" domain="[('isroomtype','=',True)]"/>
                                                    <field name="product_id"
                                                           domain="[('isroom','=',True),('categ_id','=',categ_id)]"/>
                                                    <field name="product_uom_category_id" invisible="1"/>
                                                    <field name="product_uom"/>
                                                    <field name="product_uom_qty"/>
                                                </group>
                                                <separator string="Manual Description" colspan="4"/>
                                                <group colspan="4" col="4">
                                                    <field name="name" colspan="4"/>
                                                    <field name="price_unit" string="Rent"/>
                                                    <field name="discount"/>
                                                    <field name="tax_id" widget="many2many_tags"/>
                                                </group>
                                                <newline/>

                                                <separator string="States" colspan="4"/>
                                                <field name="state"/>
                                            </page>
                                            <page name="History" string="History">
                                                <separator string="Invoice Lines" colspan="4"/>
                                                <field name="invoice_lines" colspan="4" nolabel="1"/>
                                            </page>
                                        </notebook>
                                    </form>
                                    <tree name="Room Line">
                                        <field name="name"/>
                                        <field name="state" invisible="1"/>
                                        <field name="checkin_date"/>
                                        <field name="checkout_date"/>
                                        <field name="product_id" string="Room No"/>
                                        <field name="product_uom_category_id" invisible="1"/>
                                        <field name="product_uom_qty"/>
                                        <field name="product_uom" string="Rent(UOM)"/>
                                        <field name="price_unit" string="Rent"/>
                                        <field name="price_subtotal"/>
                                    </tree>
                                </field>
                                <separator string="Service Lines"/>
                                <field name="service_lines" readonly="state in ['sale']" colspan="4" string="Service Line"
                                       nolabel="1">
                                    <form name="Service Line">
                                        <notebook>
                                            <page name="service_line" string="Service Line">
                                                <separator string="Automatic Declaration" colspan="4"/>
                                                <group colspan="4" col="4">
                                                    <field name="product_id"
                                                           domain="[('isservice','=',True)]"/>
                                                    <field name="product_uom_category_id" invisible="1"/>
                                                </group>
                                                <group colspan="4" col="4">
                                                    <field name="product_uom_qty"/>
                                                    <field name="product_uom"/>
                                                </group>
                                                <separator string="Manual Description" colspan="4"/>
                                                <group colspan="4" col="4">
                                                    <field name="name" colspan="4"/>
                                                    <field name="price_unit"/>
                                                    <field name="discount"/>
                                                    <field name="tax_id" widget="many2many_tags"/>
                                                    <newline/>

                                                    <separator string="States" colspan="4"/>
                                                    <field name="state"/>
                                                </group>
                                            </page>
                                            <!-- <page name="history" string="History">
												<separator string="Invoice Lines" colspan="4"/>
												<field name="invoice_lines" colspan="4" nolabel="1"/>
											</page> -->
                                        </notebook>
                                    </form>
                                    <tree name="Service Line">
                                        <field name="name"/>
                                        <field name="state" invisible="1"/>
                                        <field name="product_id"/>
                                        <field name="product_uom_category_id" invisible="1"/>
                                        <field name="product_uom_qty"/>
                                        <field name="price_unit"/>
                                        <field name="price_subtotal"/>
                                    </tree>
                                </field>
                                <newline/>
                                <group class="oe_subtotal_footer oe_right" colspan="2"
                                       name="sale_total">
                                    <!-- <field name="tax_totals" widget="account-tax-totals-field" nolabel="1" colspan="2" readonly="1"/> -->
                                    <field name="currency_id" invisible="1"/>
                                    <field name="amount_untaxed" widget='monetary'
                                           options="{'currency_field': 'currency_id'}"/>
                                    <field name="amount_tax" widget='monetary'
                                           options="{'currency_field': 'currency_id'}"/>
                                    <!-- <div class="oe_subtotal_footer_separator oe_inline"> -->

                                    <!-- </div> -->
                                    <label for="amount_total"/>
                                    <field name="amount_total" nolabel="1"
                                           widget='monetary'
                                           options="{'currency_field': 'currency_id'}"/>
                                    <button name="button_dummy11" string="Compute" invisible="state != 'draft'"
                                            type="object" class="oe_highlight"/>
                                </group>
                            </page>
                            <page name="other_data" string="Other Data">
                                <group colspan="4" col="4">
                                     <field name="shop_id" groups="base.group_no_one" widget="selection"/>
                                    <field name="fiscal_position_id" widget="selection"/>
                                    <field name="user_id"/>
                                    <field name="origin"/>
                                    <field name="client_order_ref"/>
                                </group>
                                <separator string="Notes" colspan="4"/>
                                <field name="note" colspan="4" nolabel="1"/>
                            </page>
                            <page name="History" string="History">
                                <separator string="Related invoices" colspan="4"/>
                                <field name="invoice_ids" colspan="4" nolabel="1"/>
                            </page>
                            <page name="table_reservations" string="Table Reservations">
                                <separator string="Reserve Order Invoices" colspan="4"/>
                                <field name="order_reserve_invoice_ids" colspan="4"
                                       nolabel="1"/>
                                <separator string="Table Order Invoices" colspan="4"/>
                                <field name="table_order_invoice_ids" colspan="4" nolabel="1"/>
                            </page>
                        </notebook>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids" widget="mail_followers"/>
                        <field name="activity_ids" widget="mail_activity"/>
                        <field name="message_ids" widget="mail_thread"/>
                    </div>
                </form>
            </field>
        </record>
        <record model="ir.ui.view" id="view_hotel_folio1_tree_view">
            <field name="name">hotel.folio.tree</field>
            <field name="model">hotel.folio</field>
            <field name="arch" type="xml">
                <tree string="Hotel Folio1" default_order="id desc">
                    <field name="name"/>
                    <field name="partner_id"/>
                    <field name="date_order"/>
                    <field name="amount_total" sum="Total amount"/>
                    <field name="state"/>
                </tree>
            </field>
        </record>
        <record id="view_hotel_folio_filter" model="ir.ui.view">
            <field name="name">hotel.folio.select</field>
            <field name="model">hotel.folio</field>
            <field name="arch" type="xml">
                <search string="Search Hotel Folio">
                    <field name="order_id" string="Sales Order"
                           filter_domain="['|',('name','ilike',self),('partner_id', 'child_of', self)]"/>
                    <field name="partner_id" filter_domain="[('partner_id', 'child_of', self)]"/>
                </search>
            </field>
        </record>
        <record model="ir.actions.act_window" id="open_hotel_folio1_form_tree">
            <field name="name">Hotel Folio</field>
            <field name="res_model">hotel.folio</field>
            <!--<field name="view_type">form</field>-->
            <field name="view_mode">tree,form</field>
            <field name="search_view_id" ref="hotel.view_hotel_folio_filter"/>
        </record>
        <record id="product_category_form_view_inherit_hotel_manageement" model="ir.ui.view">
            <field name="name">product.category.form</field>
            <field name="model">product.category</field>
            <field name="inherit_id" ref="product.product_category_form_view"/>
            <field name="arch" type="xml">
                <field name="parent_id" position="after">
                    <field name="company_id"/>
                </field>
            </field>
        </record>
        <menuitem name="All Folio" id="menu_open_hotel_folio1_form_tree_view" sequence="65"
                  parent="hotel_management_menu"/>
        <menuitem name="Folio" id="menu_open_hotel_folio1_form_tree"
                  action="hotel.open_hotel_folio1_form_tree" parent="menu_open_hotel_folio1_form_tree_view"/>
    </data>
</odoo>
