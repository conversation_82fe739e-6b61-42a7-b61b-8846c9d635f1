<?xml version="1.0" encoding="utf-8"?>
<openerp>
<data noupdate="1">
	<record id="group_banquet_manager" model="res.groups">
        <field name="name">Hotel Management/ Banquet Manager</field>
    </record>
    <record id="group_banquet_user" model="res.groups">
        <field name="name">Hotel Management / Banquet User</field>
    </record>
	
	 <!-- Multi - Company Rules -->
	
	<record model="ir.rule" id="deposit_payment_policy_comp_rule">
        <field name="name">Deposit Payment Policy multi-company</field>
        <field name="model_id" ref="model_deposit_payment_policy"/>
        <field name="global" eval="True"/>
        <field name="domain_force">['|',('company_id','=',False),('company_id','in',company_ids)]</field>
    </record>

    <record model="ir.rule" id="banquet_quotation_comp_rule">
        <field name="name">Banquet Quotataion multi-company</field>
        <field name="model_id" ref="model_banquet_quotation"/>
        <field name="global" eval="True"/>
        <field name="domain_force">['|',('company_id','=',False),('company_id','in',company_ids)]</field>
    </record>
	
	<record model="ir.rule" id="crm_lead_rel_comp_rule">
        <field name="name">Lead</field>
        <field name="model_id" ref="model_crm_lead"/>
        <field name="domain_force">['|',('company_id','=',False),('company_id','in',company_ids)]</field>
    </record>
	
</data>
</openerp>