# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * base_location
#
# Translators:
# <AUTHOR> <EMAIL>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-11-22 03:38+0000\n"
"PO-Revision-Date: 2023-06-06 13:09+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Croatian (https://www.transifex.com/oca/teams/23907/hr/)\n"
"Language: hr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n"
"%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"
"X-Generator: Weblate 4.17\n"

#. module: base_location
#: model:res.city,name:base_location.demo_brussels_city
msgid "Brussels"
msgstr "Brisel"

#. module: base_location
#: model:ir.model.fields,help:base_location.field_res_company__country_enforce_cities
msgid ""
"Check this box to ensure every address created in that country has a 'City' "
"chosen in the list of the country's cities."
msgstr ""
"Označite ovdje da osigurate da svaka adresa kreirana u toj držai ima odabran "
"'Grad' iz liste gradova u državi."

#. module: base_location
#: model:ir.actions.act_window,name:base_location.action_res_city_full
#: model:ir.ui.menu,name:base_location.locations_menu_cities
msgid "Cities"
msgstr "Gradovi"

#. module: base_location
#: model:ir.model,name:base_location.model_res_city
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__city_id
#: model:ir.model.fields,field_description:base_location.field_res_partner__city
#: model:ir.model.fields,field_description:base_location.field_res_users__city
msgid "City"
msgstr "Grad"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_company__city_id
#: model:ir.model.fields,field_description:base_location.field_res_partner__city_id
#: model:ir.model.fields,field_description:base_location.field_res_users__city_id
msgid "City ID"
msgstr "ID Grada"

#. module: base_location
#: model_terms:ir.ui.view,arch_db:base_location.view_company_form_city
#: model_terms:ir.ui.view,arch_db:base_location.view_partner_form
msgid "City completion"
msgstr "Popunjavanje gradova"

#. module: base_location
#: model:ir.model,name:base_location.model_res_city_zip
msgid "City/locations completion object"
msgstr "Objekt popunjavanja za Gradove/Lokacije"

#. module: base_location
#: model:ir.model,name:base_location.model_res_company
msgid "Companies"
msgstr "Tvrtke"

#. module: base_location
#: model:ir.model,name:base_location.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__country_id
#: model:ir.model.fields,field_description:base_location.field_res_partner__country_id
#: model:ir.model.fields,field_description:base_location.field_res_users__country_id
#: model_terms:ir.ui.view,arch_db:base_location.view_country_search
msgid "Country"
msgstr "Država"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__display_name
msgid "Display Name"
msgstr "Naziv za prikaz"

#. module: base_location
#: model_terms:ir.actions.act_window,help:base_location.action_res_city_full
msgid ""
"Display and manage the list of all cities that can be assigned to\n"
"            your partner records. Note that an option can be set on each "
"country\n"
"            separately\n"
"            to enforce any address of it to have a city in this list."
msgstr ""
"Prikaži u upravljaj listom gradova koji mogu biti dodijeljeni\n"
"                           vašim partnerima. Napomena ova opcija može biti "
"postavljena\n"
"                           pojedinačno na svakoj državi\n"
"                           radi forsiranja adrese sa odabirom grada."

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_company__country_enforce_cities
msgid "Enforce Cities"
msgstr "Obavezni gradovi"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__id
msgid "ID"
msgstr "ID"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip____last_update
msgid "Last Modified on"
msgstr "Zadnje modificirano"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__write_uid
msgid "Last Updated by"
msgstr "Zadnji ažurirao"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__write_date
msgid "Last Updated on"
msgstr "Zadnje ažuriranje"

#. module: base_location
#: model_terms:ir.ui.view,arch_db:base_location.view_partner_form
msgid "Location completion"
msgstr "Popunjavanje lokacija"

#. module: base_location
#: model:ir.actions.act_window,name:base_location.action_zip_tree
msgid "Locations"
msgstr "Lokacije"

#. module: base_location
#: model_terms:ir.ui.view,arch_db:base_location.view_city_zip_filter
msgid "Search zip"
msgstr "Pretraži poštanski broj"

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__state_id
#: model:ir.model.fields,field_description:base_location.field_res_partner__state_id
#: model:ir.model.fields,field_description:base_location.field_res_users__state_id
msgid "State"
msgstr "Županija"

#. module: base_location
#. odoo-python
#: code:addons/base_location/models/res_partner.py:0
#, python-format
msgid ""
"The city of the partner %(partner)s differs from that in location "
"%(location)s"
msgstr ""

#. module: base_location
#. odoo-python
#: code:addons/base_location/models/res_partner.py:0
#, python-format
msgid ""
"The country of the partner %(partner)s differs from that in location "
"%(location)s"
msgstr ""
"Država partnera %(partner)s razlikuje se od one na lokaciji %(location)s"

#. module: base_location
#. odoo-python
#: code:addons/base_location/models/res_partner.py:0
#, python-format
msgid ""
"The state of the partner %(partner)s differs from that in location "
"%(location)s"
msgstr ""
"Regija partnera %(partner)s razlikuje se od regije na lokaciji %(location)s"

#. module: base_location
#. odoo-python
#: code:addons/base_location/models/res_partner.py:0
#, python-format
msgid ""
"The zip of the partner %(partner)s differs from that in location %(location)s"
msgstr ""
"Poštanski broj %(partner)s razlikuje se od onog na lokaciji %(location)s"

#. module: base_location
#: model:ir.model.fields,help:base_location.field_res_company__zip_id
msgid "Use the city name or the zip code to search the location"
msgstr "Koristite naziv ili šifru grada za pretraživanje"

#. module: base_location
#: model:ir.model.constraint,message:base_location.constraint_res_city_name_state_country_uniq
msgid ""
"You already have a city with that name in the same state.The city must have "
"a unique name within it's state and it's country"
msgstr ""
"Već postoji grad ovog imena u istoj regiji. Grad mora imati jedinstveni "
"naziv unutar regije ili države"

#. module: base_location
#: model:ir.model.constraint,message:base_location.constraint_res_city_zip_name_city_uniq
msgid ""
"You already have a zip with that code in the same city. The zip code must be "
"unique within it's city"
msgstr ""

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city_zip__name
msgid "ZIP"
msgstr ""

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_company__zip_id
#: model:ir.model.fields,field_description:base_location.field_res_partner__zip_id
#: model:ir.model.fields,field_description:base_location.field_res_users__zip_id
msgid "ZIP Location"
msgstr ""

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_partner__zip
#: model:ir.model.fields,field_description:base_location.field_res_users__zip
#: model_terms:ir.ui.view,arch_db:base_location.city_zip_form
msgid "Zip"
msgstr ""

#. module: base_location
#: model:ir.ui.menu,name:base_location.locations_menu_zips
#: model_terms:ir.ui.view,arch_db:base_location.view_city_form
#: model_terms:ir.ui.view,arch_db:base_location.view_res_country_city_better_zip_form
msgid "Zips"
msgstr ""

#. module: base_location
#: model:ir.model.fields,field_description:base_location.field_res_city__zip_ids
msgid "Zips in this city"
msgstr "Poštanski brojevi u ovom gradu"

#~ msgid "Group By"
#~ msgstr "Grupiraj po"
