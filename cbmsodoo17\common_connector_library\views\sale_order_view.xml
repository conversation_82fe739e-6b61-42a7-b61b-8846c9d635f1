<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record model="ir.ui.view" id="sale_order_form_view_ept">
        <field name="name">sale.order.form.view.ept</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
            <field name="analytic_account_id" options="{'no_create': True}" position="before">
                <field name="auto_workflow_process_id"/>
            </field>
            <xpath expr="//button[@name='action_view_invoice']" position="before">
                <button type="object" name="action_view_stock_move_ept" class="oe_stat_button"
                        icon="fa-truck"
                        invisible="moves_count == 0"
                        groups="base.group_user">
                    <field name="moves_count" widget="statinfo" string="Stock Move"/>
                </button>
            </xpath>
            <button name="action_cancel" position="before">
                <button type="action" name="%(stock.act_stock_return_picking)d"
                        context="{'default_sale_order_ept_id':id}"
                        string="Return" invisible="moves_count == 0" groups="base.group_user">
                </button>
            </button>
            <xpath expr="//page[@name='order_lines']/field[@name='order_line']/tree/field[@name='price_subtotal']"
                   position="after">
                <field name="warehouse_id_ept" string="Warehouse" column_invisible="True"/>
            </xpath>
        </field>
    </record>
</odoo>
