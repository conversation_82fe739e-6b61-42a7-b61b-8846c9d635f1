<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="connector_digest_digest_default" model="digest.digest">
            <field name="name">Your Connector Periodic Digest</field>
            <field name="periodicity">daily</field>
            <field name="user_ids" eval="[(4, ref('base.user_admin'))]"/>
            <field name="next_run_date" eval="DateTime.now().strftime('%Y-%m-%d')"/>
            <field name="state">deactivated</field>
            <field name="is_connector_digest">True</field>
            <field name="kpi_orders">True</field>
            <field name="kpi_shipped_orders">True</field>
            <field name="kpi_cancel_orders">True</field>
            <field name="kpi_pending_shipment_on_date">True</field>
            <field name="kpi_refund_orders">True</field>
            <field name="kpi_avg_order_value">True</field>
            <field name="kpi_late_deliveries">True</field>
            <field name="kpi_on_shipping_orders">True</field>
            <field name="kpi_account_total_revenue">True</field>
            <field name="kpi_res_users_connected">False</field>
            <field name="kpi_mail_message_total">False</field>
            <field name="kpi_all_sale_total">False</field>
<!--            <field name="kpi_account_bank_cash">False</field>-->
        </record>

        <!-- Email layout: encapsulation when sending (not used in backend display) -->
        <template id="connector_digest_mail_layout">
            &lt;!DOCTYPE html&gt;
            <html xmlns="http://www.w3.org/1999/xhtml">
                <head>
                    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
                    <meta name="format-detection" content="telephone=no"/>
                    <meta name="viewport"
                          content="width=device-width; initial-scale=1.0; maximum-scale=1.0; user-scalable=no;"/>
                    <meta http-equiv="X-UA-Compatible" content="IE=9; IE=8; IE=7; IE=EDGE"/>

                    <style type="text/css">
                        body {
                        margin: 0;
                        padding: 0;
                        font-family: Arial, Helvetica, Verdana, sans-serif;
                        }
                        #header_background {
                        background-color: #875a7b;
                        }
                        .global_layout {
                        max-width: 588px;
                        margin: 0 auto;
                        border: 1px solid #eeeeee;
                        border-top: 0;
                        background-color: #ffffff;
                        }
                        .company_name {
                        display: inline;
                        vertical-align: middle;
                        font-weight: bold;
                        color: #8f8f8f;
                        }
                        .title_subtitle {
                        font-weight: bold;
                        color: #282f33;
                        word-break: break-all;
                        }
                        .button {
                        float: right;
                        background-color: #875a7b;
                        color: #ffffff;
                        border-radius: 5px;
                        }
                        #button_connect {
                        text-align: center;
                        }
                        .date {
                        color: #8f8f8f;
                        }
                        .tip_title {
                        margin-top: 0;
                        font-weight: bold;
                        }
                        .tip_content {
                        margin: 0 auto;
                        color:#333333;
                        text-align: justify;
                        text-justify: inter-word;
                        }
                        .tip_button {
                        background-color: #875a7b;
                        border-radius: 5px;
                        margin: 14px 16px 14px 0px;
                        padding: 10px;
                        text-decoration: none;
                        }
                        .tip_button_text {
                        color: #ffffff;
                        }
                        .illustration_border {
                        width: 100%;
                        border: 1px solid grey;
                        }
                        .kpi_row_footer {
                        padding-bottom: 20px;
                        }
                        .kpi_header {
                        overflow: auto;
                        padding-bottom: 10px;
                        border-bottom: 1px solid #eeeeee;
                        font-size: 15px;
                        font-weight: bold;
                        color: #282f33;
                        }
                        #button_open_report {
                        padding: 5px 10px;
                        font-size: 12px;
                        font-weight: normal;
                        }
                        .kpi_cell {
                        float: left;
                        width: 25%;
                        text-align: center;
                        padding-top: 2px;
                        }
                        .kpi_cell_center {
                        border-top: 2px solid #875A7B;
                        }
                        .kpi_cell_border {
                        border-top: 2px solid #00A09D;
                        }
                        .kpi_value {
                        color: #282f33;
                        font-weight: bold;
                        text-decoration: none;
                        }
                        .kpi_center_col {
                        color: #875A7B;
                        }
                        .kpi_border_col {
                        color: #00A09D;
                        }
                        .kpi_value_label {
                        display: inline-block;
                        margin-bottom: 10px;
                        color: #888888;
                        font-size: 12px;
                        text-transform: uppercase;
                        }
                        .kpi_margin {
                        width: 75px;
                        padding: 5px 10px;
                        text-decoration: none;
                        border-radius: 5px;
                        }
                        .positive_kpi_margin {
                        border: 1px solid #c4ecd7;
                        background-color: #d5f1e2;
                        color: #17613a;
                        }
                        .negative_kpi_margin {
                        border: 1px solid #f4cfce;
                        background-color: #f7dddc;
                        color: #712b29;
                        }
                        .kpi_trick {
                        clear: both;
                        }
                        .download_app {
                        height: 40px;
                        margin-bottom: 5px;
                        display: inline-block;
                        }
                        .preference_div {
                        clear: both;
                        background-color: #eeeeee;
                        text-align: center;
                        }
                        .preference {
                        margin-bottom: 15px;
                        color: #6b6d70;
                        }
                        .preference a {
                        text-decoration: none;
                        }
                        .by_odoo {
                        color: #8f8f8f;
                        }
                        .odoo_link {
                        text-decoration: none;
                        }
                        .odoo_link_text {
                        font-weight: bold;
                        color: #875a7b;
                        }
                        .run_business {
                        color: #2d2a26;
                        }
                        #footer {
                        background-color: #eeeeee;
                        color: #8f8f8f;
                        text-align: center;
                        }

                        @media only screen and (max-width: 650px) {
                        .d-block {
                        display: block !important;
                        }
                        #header_background {
                        padding-top: 0px;
                        }
                        #header {
                        padding: 15px 20px;
                        border: 1px solid #eeeeee;
                        }
                        .global_layout {
                        padding: 20px;
                        }
                        .company_name {
                        font-size: 15px;
                        }
                        .title_subtitle {
                        margin: 5px auto;
                        }
                        #button_connect {
                        padding: 4px 10px;
                        font-size: 12px;
                        }
                        .date {
                        margin-top: 10px;
                        font-size: 10px;
                        }
                        .tip_title {
                        font-size: 14px;
                        }
                        .tip_content {
                        margin: 10px auto 0 auto;
                        font-size: 12px;
                        line-height: 20px;
                        }
                        .illustration_border {
                        margin-top: 15px;
                        }
                        .kpi_value {
                        font-size: 20px;
                        }
                        .kpi_margin {
                        font-size: 10px;
                        }
                        .kpi_margin_margin {
                        margin-bottom: 5px;
                        }
                        .preference_div {
                        padding: 15px;
                        }
                        .preference {
                        font-size: 12px;
                        }
                        .by_odoo {
                        font-size: 10px;
                        }
                        .run_business {
                        margin: 20px auto;
                        font-size: 12px;
                        }
                        #footer {
                        font-size: 15px;
                        }
                        #powered {
                        margin-top: 15px;
                        }
                        .tip_twocol {
                        overflow: auto;
                        margin-top: 15px;
                        }
                        .tip_twocol_left {
                        text-align: left;
                        }
                        .tip_twocol_img {
                        width: 90%;
                        }
                        }

                        @media only screen and (min-width: 651px) {
                        #header_background {
                        padding-top: 70px;
                        }
                        #header {
                        padding: 20px 30px 25px 30px;
                        border-left: 1px solid #875a7b;
                        border-right: 1px solid #875a7b;
                        }
                        .global_layout {
                        padding: 25px 30px 30px 30px;
                        }
                        .company_name {
                        font-size: 25px;
                        }
                        .title_subtitle {
                        margin: 10px auto;
                        }
                        .button {
                        padding: 5px 10px;
                        }
                        #button_connect {
                        padding: 6px 10px;
                        font-size: 15px;
                        }
                        .date {
                        margin-top: 15px;
                        font-size: 12px;
                        }
                        .tip_title {
                        font-size: 20px;
                        }
                        .tip_content {
                        margin: 15px auto 0 auto;
                        font-size: 15px;
                        line-height: 25px;
                        }
                        .illustration_border {
                        margin-top: 20px;
                        }
                        .kpi_value {
                        font-size: 30px;
                        }
                        .kpi_margin {
                        font-size: 12px;
                        }
                        .kpi_margin_margin {
                        margin-bottom: 10px;
                        }
                        .preference_div {
                        padding: 20px;
                        }
                        .preference {
                        font-size: 15px;
                        }
                        .by_odoo {
                        font-size: 12px;
                        }
                        .run_business {
                        margin: 15px auto;
                        font-size: 18px;
                        }
                        #footer {
                        font-size: 20px;
                        }
                        #stock_tip {
                        overflow: auto;
                        margin-top: 20px;
                        }
                        #stock_div_img {
                        text-align: center;
                        }
                        #stock_img {
                        width: 70%;
                        }
                        }
                    </style>
                </head>
                <body>
                    <t t-out="body"/>
                </body>
            </html>
        </template>
        <!-- CONNECTOR DIGEST MAIN TEMPLATE -->
        <template id="connector_digest_mail_main">
            <div id="header_background">
                <div class="global_layout" id="header">
                    <div style="overflow: auto;">
                        <p t-field="company.name" class="company_name"/>
                        <a t-att-href="top_button_url" target="_blank">
                            <span t-esc="top_button_label" class="button" id="button_connect"/>
                        </a>
                    </div>
                    <div class="title_subtitle">
                        <p t-esc="title"/>
                        <p t-if="sub_title" t-esc="sub_title"/>
                    </div>
                    <div t-esc="formatted_date" class="date"/>
                </div>
            </div>

            <div style="background-color: #eeeeee;">

                <div t-if="kpi_data" class="global_layout" style="padding-bottom: 0;">
                    <t t-set="kpi_color" t-value="'kpi_border_col'"/>
                    <div t-foreach="kpi_data" t-as="kpi_info" class="kpi_row_footer">
                        <div t-if="kpi_info.get('kpi_col1') or kpi_info.get('kpi_col2') or kpi_info.get('kpi_col3') or kpi_info.get('kpi_col4')"
                             t-att-data-field="kpi_info['kpi_name']">
                            <div class="kpi_header">
                                <span t-esc="kpi_info['kpi_fullname']" style="vertical-align: middle;"/>
                                <a t-if="kpi_info['kpi_action']" t-att-href="'/web#action=%s' % kpi_info['kpi_action']">
                                    <span class="button" id="button_open_report">Open Report</span>
                                </a>
                            </div>
                            <div t-if="kpi_info.get('kpi_name') == 'kpi_late_deliveries'">
                                <style>
                                    .custom_kpi_cell {
                                    width: 33%;
                                    float: left;
                                    padding-top: 2px;
                                    text-align: center;
                                    }
                                    .custom_kpi_value {
                                    font-size:30px;
                                    color:#00A09D;
                                    font-weight: bold;
                                    }
                                </style>
                                <div class="custom_kpi_cell kpi_cell_border">
                                    <span class="custom_kpi_value">1 - 3</span>
                                </div>
                                <div class="custom_kpi_cell kpi_cell_center">
                                    <span class="custom_kpi_value">4 - 7</span>
                                </div>
                                <div class="custom_kpi_cell kpi_cell_border">
                                    <span class="custom_kpi_value">7+</span>
                                </div>
                                <div t-if="kpi_info.get('kpi_col1')" class="custom_kpi_cell kpi_cell_border">
                                    <div t-call="digest.digest_tool_kpi">
                                        <t t-set="kpi_value" t-value="kpi_info['kpi_col1']['value']"/>
                                    </div>
                                </div>
                                <div t-if="kpi_info.get('kpi_col2')" class="custom_kpi_cell kpi_cell_center">
                                    <div t-call="digest.digest_tool_kpi">
                                        <t t-set="kpi_value" t-value="kpi_info['kpi_col2']['value']"/>
                                    </div>
                                </div>
                                <div t-if="kpi_info.get('kpi_col3')" class="custom_kpi_cell kpi_cell_border">
                                    <div t-call="digest.digest_tool_kpi">
                                        <t t-set="kpi_value" t-value="kpi_info['kpi_col3']['value']"/>
                                    </div>
                                </div>
                            </div>
                            <div t-else="">
                                <div t-if="kpi_info.get('kpi_col1')" class="kpi_cell kpi_cell_border">
                                    <div t-call="digest.digest_tool_kpi">
                                        <t t-set="kpi_value" t-value="kpi_info['kpi_col1']['value']"/>
                                        <t t-set="kpi_subtitle" t-value="kpi_info['kpi_col1']['col_subtitle']"/>
                                    </div>
                                </div>
                                <div t-if="kpi_info.get('kpi_col2')" class="kpi_cell kpi_cell_center">
                                    <div t-call="digest.digest_tool_kpi">
                                        <t t-set="kpi_value" t-value="kpi_info['kpi_col2']['value']"/>
                                        <t t-set="kpi_subtitle" t-value="kpi_info['kpi_col2']['col_subtitle']"/>
                                    </div>
                                </div>
                                <div t-if="kpi_info.get('kpi_col3')" class="kpi_cell kpi_cell_border">
                                    <div t-call="digest.digest_tool_kpi">
                                        <t t-set="kpi_value" t-value="kpi_info['kpi_col3']['value']"/>
                                        <t t-set="kpi_subtitle" t-value="kpi_info['kpi_col3']['col_subtitle']"/>
                                    </div>
                                </div>
                                <div t-if="kpi_info.get('kpi_col4')" class="kpi_cell kpi_cell_center">
                                    <div t-call="digest.digest_tool_kpi">
                                        <t t-set="kpi_value" t-value="kpi_info['kpi_col4']['value']"/>
                                        <t t-set="kpi_subtitle" t-value="kpi_info['kpi_col4']['col_subtitle']"/>
                                    </div>
                                </div>
                            </div>
                            <div class="kpi_trick"/>
                        </div>
                    </div>
                </div>

                <t t-if="body" t-out="body"/>

                <div class="global_layout" id="footer">
                    <p style="font-weight: bold;" t-esc="company.name"/>
                    <p class="by_odoo" id="powered">
                        Powered by
                        <a href="https://www.odoo.com" target="_blank" class="odoo_link">
                            <span class="odoo_link_text">Odoo</span>
                        </a>
                    </p>
                </div>
            </div>
        </template>
    </data>
</odoo>
