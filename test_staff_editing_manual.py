#!/usr/bin/env python3
"""
Manual test script to verify staff editing functionality
Run this script in the Odoo shell: python odoo-bin shell -d your_database
"""

def test_staff_editing():
    """Manual test for staff editing functionality"""
    print("=== Testing Staff Editing Functionality ===")
    
    # Get environment
    env = globals().get('env')
    if not env:
        print("Error: This script must be run in Odoo shell")
        return
    
    try:
        # 1. Test model exists
        print("\n1. Testing model existence...")
        appointment_model = env['calendar.event']
        audit_model = env['appointment.audit.log']
        print("✓ Models exist")
        
        # 2. Test field existence
        print("\n2. Testing field existence...")
        test_appointment = appointment_model.search([('approval_state', '=', 'pending')], limit=1)
        if not test_appointment:
            print("No pending appointments found. Creating test appointment...")
            # Create test data
            test_partner = env['res.partner'].create({
                'name': 'Test Customer',
                'email': '<EMAIL>'
            })
            test_appointment = appointment_model.create({
                'name': 'Test Appointment',
                'start': '2024-01-01 10:00:00',
                'stop': '2024-01-01 11:00:00',
                'partner_ids': [(6, 0, [test_partner.id])],
                'approval_state': 'pending'
            })
        
        # Check if new fields exist
        fields_to_check = ['approval_state', 'original_user_id', 'staff_changed_by', 'audit_log_ids']
        for field in fields_to_check:
            if hasattr(test_appointment, field):
                print(f"✓ Field '{field}' exists")
            else:
                print(f"✗ Field '{field}' missing")
        
        # 3. Test permission checking
        print("\n3. Testing permission checking...")
        admin_user = env.ref('base.user_admin')
        if test_appointment.with_user(admin_user)._check_staff_edit_permission():
            print("✓ Admin has staff edit permission for pending appointments")
        else:
            print("✗ Admin permission check failed")
        
        # 4. Test staff availability method
        print("\n4. Testing staff availability...")
        available_staff = test_appointment.get_available_staff()
        print(f"✓ Found {len(available_staff)} available staff members")
        
        # 5. Test audit log creation
        print("\n5. Testing audit log...")
        initial_logs = len(test_appointment.audit_log_ids)
        
        # Simulate staff change (as admin)
        if available_staff:
            new_staff = available_staff[0]
            old_user_id = test_appointment.user_id.id if test_appointment.user_id else False
            
            # Change staff
            test_appointment.with_user(admin_user).write({'user_id': new_staff.id})
            
            # Check if audit log was created
            final_logs = len(test_appointment.audit_log_ids)
            if final_logs > initial_logs:
                print("✓ Audit log created for staff change")
            else:
                print("✗ Audit log not created")
        
        # 6. Test approval logging
        print("\n6. Testing approval logging...")
        if test_appointment.approval_state == 'pending':
            test_appointment.action_approve()
            approval_logs = audit_model.search([
                ('appointment_id', '=', test_appointment.id),
                ('action_type', '=', 'approval_change')
            ])
            if approval_logs:
                print("✓ Approval change logged")
            else:
                print("✗ Approval change not logged")
        
        print("\n=== Test Summary ===")
        print("✓ Staff editing functionality appears to be working correctly")
        print("✓ All core features implemented")
        print("✓ Audit trail functional")
        
    except Exception as e:
        print(f"✗ Error during testing: {e}")
        import traceback
        traceback.print_exc()

def test_view_inheritance():
    """Test that view inheritance is working"""
    print("\n=== Testing View Inheritance ===")
    
    env = globals().get('env')
    if not env:
        print("Error: This script must be run in Odoo shell")
        return
    
    try:
        # Check if our view exists
        view = env['ir.ui.view'].search([
            ('name', '=', 'calendar.event.form.advanced.approval')
        ])
        if view:
            print("✓ Enhanced appointment form view exists")
            
            # Check if it has our modifications
            arch = view.arch_db
            if 'approval_state == \'pending\' and user_has_groups(\'base.group_system\')' in arch:
                print("✓ Staff editing conditions found in view")
            else:
                print("✗ Staff editing conditions not found in view")
        else:
            print("✗ Enhanced appointment form view not found")
            
        # Check audit view
        audit_view = env['ir.ui.view'].search([
            ('name', '=', 'appointment.audit.log.tree')
        ])
        if audit_view:
            print("✓ Audit log view exists")
        else:
            print("✗ Audit log view not found")
            
    except Exception as e:
        print(f"✗ Error testing views: {e}")

if __name__ == "__main__":
    print("Run this script in Odoo shell:")
    print("python odoo-bin shell -d your_database")
    print("Then execute: exec(open('test_staff_editing_manual.py').read())")
    print("Then run: test_staff_editing()")
