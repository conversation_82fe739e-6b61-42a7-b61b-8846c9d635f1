# -*- coding: utf-8 -*-
from odoo import api, fields, models, _
from odoo.exceptions import UserError
from datetime import timedelta
import logging

_logger = logging.getLogger(__name__)

class WebsiteAppointmentAppointment(models.Model):
    _inherit = 'calendar.event'

    approval_state = fields.Selection([
        ("pending", "Pending"),
        ("approved", "Approved"),
        ("rejected", "Rejected"),
        ("cancelled", "Cancelled"),
    ], string="Approval Status", default="pending", copy=False, tracking=True)

    checkin_state = fields.Selection([
        ("waiting", "Waiting for Client"),
        ("in_progress", "In Progress"),
        ("completed", "Completed"),
        ("no_show", "No Show"),
        ("forfeited", "Forfeited"),
    ], string="Check-in Status", default="waiting", copy=False, tracking=True)

    @api.model_create_multi
    def create(self, vals_list):
        """Override create to handle portal user creation for website appointments"""
        events = super().create(vals_list)

        # Process each created event
        for event in events:
            # Check if this is a website appointment (has approval_state pending)
            if hasattr(event, 'approval_state') and event.approval_state == 'pending' and event.partner_id:
                _logger.info(f"Processing website appointment {event.id} for partner {event.partner_id.name}")

                # Grant portal access to the customer if they don't have it
                try:
                    self._grant_portal_access(event.partner_id)
                    _logger.info(f"Portal access granted/verified for partner {event.partner_id.name}")
                except Exception as e:
                    _logger.error(f"Failed to grant portal access for partner {event.partner_id.name}: {e}")

        return events

    def _grant_portal_access(self, partner):
        """Grant portal access to a partner and send invitation email if needed"""
        if not partner or not partner.email:
            _logger.warning(f"Cannot grant portal access: partner {partner} has no email")
            return False

        email_login = partner.email.lower()

        # Check if user already exists (including inactive users)
        existing_user = self.env['res.users'].with_context(active_test=False).search([('login', '=', email_login)], limit=1)
        if existing_user:
            _logger.info(f"User with login '{email_login}' already exists. Checking access...")

            # Get important groups
            portal_group = self.env.ref('base.group_portal', raise_if_not_found=False)
            internal_user_group = self.env.ref('base.group_user', raise_if_not_found=False)

            if not portal_group:
                _logger.error("Portal group not found")
                return False

            # Check if user already has portal access
            if existing_user.active and portal_group in existing_user.groups_id:
                _logger.info(f"User {existing_user.login} already has portal access")
                return True

            # Check if user is an internal user (has more privileges than portal)
            if existing_user.active and internal_user_group and internal_user_group in existing_user.groups_id:
                _logger.info(f"User {existing_user.login} is an internal user - no need for portal access")
                return True

            # If user is inactive, reactivate and give portal access
            if not existing_user.active:
                existing_user.write({
                    'active': True,
                    'groups_id': [(6, 0, [portal_group.id])]  # Replace groups with only portal group
                })
                _logger.info(f"Reactivated user {existing_user.login} with portal access")
                return True

            # If user is active but has no portal access and is not internal user
            if portal_group not in existing_user.groups_id:
                # Replace user groups with portal group to avoid conflicts
                existing_user.write({
                    'groups_id': [(6, 0, [portal_group.id])]  # Replace all groups with portal group
                })
                _logger.info(f"Granted portal access to existing user {existing_user.login}")
                return True

            return True

        # Create new portal user only if one doesn't exist
        try:
            _logger.info(f"Creating new portal user for {partner.name} ({email_login})")

            # Use the portal wizard to create the user properly
            portal_wizard = self.env['portal.wizard'].create({
                'user_ids': [(0, 0, {
                    'partner_id': partner.id,
                    'email': partner.email,
                    'in_portal': True,
                })]
            })
            portal_wizard.action_apply()
            _logger.info(f"Created portal user and sent invitation email to {partner.email}")

        except Exception as e:
            _logger.error(f"Could not create portal user for {partner.name}: {e}")
            return False

        return True

    def action_approve(self):
        """Approve appointment and send notification"""
        for appt in self:
            if appt.approval_state != 'pending':
                _logger.warning(f"Appointment {appt.id} is not in pending state, current state: {appt.approval_state}")
                continue

            _logger.info(f"Approving appointment {appt.id} for partner {appt.partner_id.name}")

            # Update appointment status
            appt.write({'approval_state': 'approved', 'checkin_state': 'waiting'})

            # Grant portal access to customer
            if appt.partner_id:
                portal_granted = appt._grant_portal_access(appt.partner_id)
                if portal_granted:
                    _logger.info(f"Portal access granted for appointment {appt.id}")
                else:
                    _logger.warning(f"Failed to grant portal access for appointment {appt.id}")

            # Send approval notification email
            template = self.env.ref('advanced_appointment_manager.email_template_appointment_approved_aam', raise_if_not_found=False)
            if template:
                try:
                    template.send_mail(appt.id, force_send=True)
                    _logger.info(f"Sent approval email for appointment {appt.id}")
                except Exception as e:
                    _logger.error(f"Failed to send approval email for appointment {appt.id}: {e}")
            else:
                _logger.error("Approval email template not found")

        return True

    def action_reject(self):
        """Reject appointment and send notification"""
        for appt in self:
            if appt.approval_state != 'pending':
                _logger.warning(f"Appointment {appt.id} is not in pending state, current state: {appt.approval_state}")
                continue

            _logger.info(f"Rejecting appointment {appt.id} for partner {appt.partner_id.name}")

            # Update appointment status
            appt.write({'approval_state': 'rejected'})

            # Send rejection notification email
            template = self.env.ref('advanced_appointment_manager.email_template_appointment_rejected_aam', raise_if_not_found=False)
            if template:
                try:
                    template.send_mail(appt.id, force_send=True)
                    _logger.info(f"Sent rejection email for appointment {appt.id}")
                except Exception as e:
                    _logger.error(f"Failed to send rejection email for appointment {appt.id}: {e}")
            else:
                _logger.error("Rejection email template not found")

        return True

    def action_check_in(self):
        self.ensure_one()
        self.checkin_state = "in_progress"

    def action_mark_completed(self):
        self.ensure_one()
        self.checkin_state = "completed"

    def action_mark_no_show(self):
        self.ensure_one()
        self.checkin_state = "no_show"

    def action_mark_forfeited(self):
        self.ensure_one()
        if self.checkin_state == 'forfeited': return
        self.checkin_state = 'forfeited'
        template = self.env.ref('advanced_appointment_manager.email_template_appointment_forfeited_aam', raise_if_not_found=False)
        if template:
            # Changed to False to add email to queue for inspection.
            template.send_mail(self.id, force_send=False)

    def action_cancel(self):
        if not self.filtered(lambda appt: appt.approval_state in ['pending', 'approved']): return False
        return self.write({'approval_state': 'cancelled'})

    @api.model
    def _cron_update_appointment_status(self):
        company = self.env.company
        lateness_policy = company.lateness_policy
        if lateness_policy == 'none': return
        now = fields.Datetime.now()
        search_window_past = now - timedelta(days=2)
        search_window_future = now + timedelta(days=2)
        waiting_appointments = self.search([
            ('start', '>=', search_window_past), ('start', '<=', search_window_future),
            ('approval_state', '=', 'approved'), ('checkin_state', '=', 'waiting')
        ])
        if not waiting_appointments: return
        if lateness_policy == 'no_show':
            no_show_delay_minutes = company.no_show_delay_minutes
            for appt in waiting_appointments:
                if appt.start < now - timedelta(minutes=no_show_delay_minutes):
                    _logger.info(f"Marking appointment {appt.id} as No Show based on policy.")
                    appt.action_mark_no_show()
        elif lateness_policy == 'forfeit':
            forfeit_minutes_before = company.forfeit_minutes_before
            for appt in waiting_appointments:
                deadline = appt.start - timedelta(minutes=forfeit_minutes_before)
                if deadline <= now < appt.start:
                    _logger.info(f"Marking appointment {appt.id} as Forfeited based on policy.")
                    appt.action_mark_forfeited()

    def test_email_templates(self):
        """Test method to verify email templates are working"""
        self.ensure_one()
        _logger.info("Testing email templates for advanced appointment manager")

        # Test approval template
        approval_template = self.env.ref('advanced_appointment_manager.email_template_appointment_approved_aam', raise_if_not_found=False)
        if approval_template:
            _logger.info(f"✅ Approval email template found: {approval_template.name}")
        else:
            _logger.error("❌ Approval email template not found")

        # Test rejection template
        rejection_template = self.env.ref('advanced_appointment_manager.email_template_appointment_rejected_aam', raise_if_not_found=False)
        if rejection_template:
            _logger.info(f"✅ Rejection email template found: {rejection_template.name}")
        else:
            _logger.error("❌ Rejection email template not found")

        # Test forfeited template
        forfeited_template = self.env.ref('advanced_appointment_manager.email_template_appointment_forfeited_aam', raise_if_not_found=False)
        if forfeited_template:
            _logger.info(f"✅ Forfeited email template found: {forfeited_template.name}")
        else:
            _logger.error("❌ Forfeited email template not found")

        # Show user notification
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Email Templates Test',
                'message': 'Email templates test completed. Check server logs for details.',
                'type': 'success',
            }
        }

    def debug_appointment_flow(self):
        """Debug method to check appointment flow"""
        self.ensure_one()
        _logger.info("=== DEBUGGING APPOINTMENT FLOW ===")
        _logger.info(f"Current appointment ID: {self.id}")
        _logger.info(f"Approval state: {self.approval_state}")
        _logger.info(f"Check-in state: {self.checkin_state}")
        _logger.info(f"Partner: {self.partner_id.name if self.partner_id else 'None'}")
        _logger.info(f"Partner email: {self.partner_id.email if self.partner_id else 'None'}")

        # Check if partner has portal access
        portal_status = "No partner"
        if self.partner_id and self.partner_id.email:
            existing_user = self.env['res.users'].search([('login', '=', self.partner_id.email.lower())], limit=1)
            if existing_user:
                _logger.info(f"Partner has user account: {existing_user.login} (Active: {existing_user.active})")
                _logger.info(f"User groups: {[g.name for g in existing_user.groups_id]}")
                portal_status = f"User exists: {existing_user.login} (Active: {existing_user.active})"
            else:
                _logger.info("Partner does not have user account")
                portal_status = "No user account"

        _logger.info("=== END DEBUG ===")

        # Show user notification with debug info
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Debug Information',
                'message': f'Appointment {self.id}: {self.approval_state} | Portal: {portal_status}. Check server logs for full details.',
                'type': 'info',
            }
        }