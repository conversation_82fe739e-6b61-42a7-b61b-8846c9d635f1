# -*- coding: utf-8 -*-
from odoo import http
from odoo.http import request
from odoo.exceptions import AccessError, MissingError
from odoo.addons.portal.controllers.portal import CustomerPortal, pager as portal_pager
from odoo.fields import Datetime
import logging

_logger = logging.getLogger(__name__)

class AppointmentCustomerPortal(CustomerPortal):

    def _prepare_home_portal_values(self, counters):
        values = super()._prepare_home_portal_values(counters)
        partner = request.env.user.partner_id
        appointment_count = request.env['calendar.event'].search_count([
            ('partner_ids', 'child_of', partner.id),
            ('approval_state', 'in', ['pending', 'approved'])
        ])
        values['appointment_count'] = appointment_count
        return values

    @http.route(['/my/appointments', '/my/appointments/page/<int:page>'], type='http', auth="user", website=True)
    def portal_my_appointments(self, page=1, **kw):
        partner = request.env.user.partner_id
        domain = [('partner_ids', 'child_of', partner.id)]
        appointment_count = request.env['calendar.event'].search_count(domain)
        pager = portal_pager(url="/my/appointments", total=appointment_count, page=page, step=self._items_per_page)
        appointments = request.env['calendar.event'].search(domain, limit=self._items_per_page, offset=pager['offset'], order='start desc')
        values = {'appointments': appointments, 'page_name': 'appointments', 'pager': pager, 'default_url': '/my/appointments'}
        return request.render("advanced_appointment_manager.portal_my_appointments", values)

    @http.route(['/my/appointments/<int:appointment_id>'], type='http', auth="user", website=True)
    def portal_appointment_detail(self, appointment_id, **kw):
        try:
            appointment = self._document_check_access('calendar.event', appointment_id)
        except (AccessError, MissingError):
            return request.redirect('/my')
        
        values = {
            'appointment': appointment,
            'user': request.env.user,
            'now': Datetime.now(),
        }
        return request.render("advanced_appointment_manager.portal_appointment_detail", values)

    @http.route(['/my/appointments/<int:appointment_id>/cancel'], type='http', auth="user", website=True, methods=['POST'])
    def portal_appointment_cancel(self, appointment_id, **kw):
        try:
            appointment = self._document_check_access('calendar.event', appointment_id)
        except (AccessError, MissingError):
            return request.redirect('/my')
        if appointment.approval_state in ['approved', 'pending']:
            appointment.action_cancel()
        return request.redirect('/my/appointments')