<?xml version='1.0' encoding='UTF-8' ?>
<odoo>
    <data>
        <template id="booking_show" name="room booking">
            <t t-call="website.layout">
                <div id="wrap" class="pattern_bg">
                    <div class="oe_structure"/>
                    <div class="container">
                        <div class="row">
                            <div class="book_fullcol booking_info1">
                                <div class="col-md-12">
                                    <div class="bookroom_headingbg">
                                        <h4 style="margin-left:10px;">Review Your Booking</h4>
                                        <div class="dashed_line"/>
                                        <br/>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <form action="/product_remove/" type="post">
                                        <input type="hidden" name="room_order_id" id="room_id" t-att-value="room_id"/>
                                        <t t-if="room_data">
                                            <t t-foreach="room_data" t-as="a">
                                                <div class="card mb-12" style="max-width: 100%;margin-left:100px;margin-right:100px;margin-bottom: 25px;box-shadow: 0px 0px 9px 2px rgb(156, 144, 144);">
                                                    <div class="row g-0">
                                                        <div class="col-md-6">
                                                        <t t-if="website.get_image(a)">
                                                        <img t-att-src="website.image_url_new(a['image'], 'img', None if product_image_big else '512x512')[0]" alt="Plants: image 1 0f 4 thumb" class="card-img-top" style="height:288px; width:400px;"/>
                                                        </t>
                                                        </div>
                                                        <div class="col-md-6">
                                                        <div class="card-body" style="margin-left:50px;">
                                                            <input type="hidden" name="len" t-att-value="{'data':room_data}"/>
                                                            <input type="hidden" name="chkin" t-att-value="a['chkin']"/>
                                                            <input type="hidden" name="chkout" t-att-value="a['chkout']"/>
                                                            <input type="hidden" name="rm_type" t-att-value="a['chkout']"/>
                                                            <h5 class="card-title">Room Type : <span t-esc="a['rm_name']"></span></h5>
                                                            <p class="card-text">Arrival Date : <span t-esc="a['chkin']"></span></p>
                                                            <p class="card-text">Departure Date : <span t-esc="a['chkout']"></span></p>
                                                            <p class="card-text">No. of nights : <span t-esc="a['nights']"></span></p>
                                                            <p class="card-text">Adult : <span t-esc="a['adult']"></span></p>
                                                            <p class="card-text">Children : <span t-esc="a['child']"></span></p>
                                                            <p class="card-text">SubTotal : <span t-esc="a['price']"></span><span t-esc="a['img']"></span></p>
                                                        </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </t>
                                                <!-- <div class="col-md-2">
                                                    <t t-if="website.get_image(a)">
                                                        <a class="example-image-link" data-lightbox="example-set" title="Room-1" href="website.image_url_new(a['image'], 'img', None if product_image_big else '512x512')[0]">
                                                            <img itemprop="image" class="img img-responsive img-border" t-att-src="website.image_url_new(a['image'], 'img', None if product_image_big else '512x512')[0]" alt="Plants: image 1 0f 4 thumb" width="141" height="104"/>
                                                        </a>
                                                    </t>
                                                </div>
                                                <div class="col-md-12" style="margin-left: 1em;">
                                                    <div class="col-md-8 row">
                                                        <input type="hidden" name="len" t-att-value="{'data':room_data}"/>
                                                        <input type="hidden" name="chkin" t-att-value="a['chkin']"/>
                                                        <input type="hidden" name="chkout" t-att-value="a['chkout']"/>
                                                        <input type="hidden" name="rm_type" t-att-value="a['chkout']"/>
                                                        <form>
                                                            <div class="book_containt book_containt_form" style="margin-left:10px;">
                                                                <label>Room Type :-</label>
                                                                <input type="text" name="room_type" t-att-value="a['rm_name']" style="margin-left:35px;" readonly="readonly"/>
                                                                <strong style="margin-left:35px;">SubTotal :</strong>
                                                                <span t-esc="a['price']"/>
                                                                <span t-esc="a['img']"/>
                                                            </div>
                                                            <div class="book_containt book_containt_form" style="margin-left:10px;">
                                                                <label>Arrival Date :-</label>
                                                                <input type="text" name="chkin" t-att-value="a['chkin']" style="margin-left:33px;" readonly="readonly"/>
                                                            </div>
                                                            <div class="book_containt book_containt_form" style="margin-left:10px;">
                                                                <label>Departure Date :-</label>
                                                                <input type="text" name="chkout" t-att-value="a['chkout']" style="margin-left:11px;" readonly="readonly"/>
                                                            </div>
                                                            <div class="book_containt book_containt_form" style="margin-left:10px;">
                                                                <label>No. of nights :-</label>
                                                                <input type="text" name="nights" t-att-value="a['nights']" style="margin-left:25px;" readonly="readonly"/>
                                                            </div>
                                                            <div class="book_containt book_containt_form" style="margin-left:10px;">
                                                                <label>Adult :-</label>
                                                                <input type="text" name="adult" t-att-value="a['adult']" style="margin-left:76px;" readonly="readonly"/>
                                                            </div>
                                                            <div class="book_containt book_containt_form" style="margin-left:10px;">
                                                                <label>Children :-</label>
                                                                <input type="text" name="child" t-att-value="a['child']" style="margin-left:55px;" readonly="readonly"/>
                                                            </div>
                                                        </form>
                                                    </div>
                                                </div> -->
                                                <!--												<div class="col-md-5">-->
                                                <!--													<div style="margin-top:100px; float:right; padding-right:0px;">-->
                                                <!--														<strong>Total :</strong>-->
                                                <!--														<span t-esc="a['price']" />-->
                                                <!--														<span t-esc="a['img']" />-->
                                                <!--													</div>-->
                                                <!--												</div>-->
                                                <div class="clearfix"/>
                                                <br/>
                                            <!-- </t> -->
                                        </t>
                                    </form>
                                </div>
                                <div class="dashed_line"/>
                                <div class="col-md-12 bookroom_footer row" style="margin-left: inherit;">
                                    <div class="col-md-7"/>
                                    <div class="col-md-5" style="text-align:right;">
                                        <form>
                                            <strong>Total Amount:</strong>
                                            <input id="total" type="text" size="15" t-att-value="tot" style="padding:0 10px; font-weight:bold; color:#666;" readonly="readonly"/>
                                            <br/>
                                            <br/>
                                            <strong>Taxes:</strong>
                                            <input id="tax" type="text" size="15" t-att-value="tax" style="padding:0 10px; font-weight:bold;color:#666;" onchange="onchange_tax(this)" readonly="readonly"/>
                                            <br/>
                                            <br/>
                                            <strong>Grand Total:</strong>
                                            <input id="grand_total" type="text" size="15" t-att-value="tot_tax" style="padding:0 10px; font-weight:bold;color:#666;" readonly="readonly"/>
                                            <br/>
                                            <br/>
                                        </form>
                                    </div>
                                </div>
                                <div class="col-md-12 bookroom_footer1">
                                    <a class="btn btn-primary" href="/product_screen/" style="clear:right; margin-top:10px;margin-left:1em;">
                                        <span class="fa fa-long-arrow-left"/>
                                        Back to Booking
                                    </a>
                                    <a class="btn btn-primary reservation_payment" href="/partner/checkout" style="clear:right; margin-top:10px;margin-left:57em;">
                                        Proceed to Payment
                                        <span class="fa fa-long-arrow-right"/>
                                    </a>
                                </div>
                                <!-- <div class="col-md-12 bookroom_footer1">
                                    <div class="col-md-6" style="margin-left:66em;">
                                        <a class="btn btn-primary" href="/product_screen/" style="clear:right; margin-top:10px;">
                                            <span class="fa fa-long-arrow-left" />
											Back to Booking
                                        </a>
                                    </div>
                                    <div class="col-md-6" style="margin-left:66em;">
                                        <a class="btn btn-primary reservation_payment" href="/partner/checkout" style="clear:right; margin-top:10px;">
											Proceed to Payment
                                            <span class="fa fa-long-arrow-right" />
                                        </a>
                                    </div>
                                </div> -->
                            </div>
                        </div>
                        <div class="oe_structure"/>
                    </div>
                </div>
            </t>
        </template>
        <!-- New Template added -->
        <template id="res_partner_show" name="room booking">
    <t t-call="website.layout">
        <div id="wrap">
            <div class="container oe_website_sale">
                <h1>Your Address</h1>
                <form action="/partner_add/" method="post" class="checkout_autoformat">
                    <!-- <form action="/shop/confirm_order/" method="post"> -->
                    <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                    <div class="row">
                        <div class="col-md-8 oe_cart">
                            <h3 class="page-header mt16">
                                Billing Information
                                <small groups="base.group_public">
                                    or
                                    <a class='btn btn-primary' t-if="not partner" t-attf-href="/web?redirect=#{ request.httprequest.url }">Sign in</a>
                                </small>
                            </h3>
                            <div class="row">
                                <div class="col-md-12">
                                    <div t-if="error_message" class="alert alert-danger">
                                        <t t-foreach="error_message" t-as="err">
                                            <t t-esc="err"/>
                                            <br/>
                                        </t>
                                    </div>
                                </div>
                                <div t-attf-class="form-group #{error.get('name') and 'has-error' or ''} col-lg-6">
                                    <label class="control-label" for="contact_name">Your Name</label>
                                    <t t-if="checkout.get('name') == 'Public user'">
                                        <input type="text" name="name" class="form-control"/>
                                    </t>
                                    <t t-else="">
                                        <input type="text" name="name" class="form-control" t-att-value="checkout.get('name')"/>
                                    </t>
                                </div>
                                <div t-if="has_check_vat" class="clearfix"/>
                                <div t-attf-class="form-group #{error.get('street2') and 'has-error' or ''} col-lg-6">
                                    <label class="control-label label-optional" for="street2">Company Name</label>
                                    <input type="text" name="street2" class="form-control" t-att-value="checkout.get('street2')"/>
                                </div>
                                <!-- <div t-if="has_check_vat" t-attf-class="form-group #{error.get('vat') and 'has-error' or ''} col-lg-6">
                                    <label class="control-label label-optional" for="vat">VAT Number</label>
                                    <input type="text" name="vat" class="form-control" t-att-value="checkout.get('vat')" />
                                </div> -->
                                <div t-attf-class="form-group #{error.get('email') and 'has-error' or ''} col-lg-6">
                                    <label class="control-label" for="contact_name">Email</label>
                                    <input type="email" name="email" class="form-control" t-att-value="checkout.get('email')"/>
                                </div>
                                <div t-attf-class="form-group #{error.get('phone') and 'has-error' or ''} col-lg-6">
                                    <label class="control-label" for="phone">Phone</label>
                                    <input type="tel" name="phone" class="form-control" t-att-value="checkout.get('phone')"/>
                                </div>
                                <div t-attf-class="form-group #{error.get('street') and 'has-error' or ''} col-lg-6">
                                    <label class="control-label" for="street">Street</label>
                                    <input type="text" name="street" class="form-control" t-att-value="checkout.get('street')"/>
                                </div>
                                <!-- <div class="clearfix" /> -->
                                <div t-attf-class="form-group #{error.get('city') and 'has-error' or ''} col-lg-6">
                                    <label class="control-label" for="city">City</label>
                                    <input type="text" name="city" class="form-control" t-att-value="checkout.get('city')"/>
                                </div>
                                <div t-attf-class="form-group #{error.get('zip') and 'has-error' or ''} col-lg-6">
                                    <label class="control-label label-optional" for="zip">Zip / Postal Code</label>
                                    <input type="text" name="zip" class="form-control" t-att-value="checkout.get('zip')"/>
                                </div>
                                <div t-attf-class="form-group #{error.get('country_id') and 'has-error' or ''} col-md-6 div_country">
                                    <label class="control-label" for="country_id">Country</label>
                                    <select id="country_id" name="country_id" class="form-control" mode="shipping">
                                        <option value="">Country...</option>
                                        <t t-foreach="countries" t-as="c">
                                            <option t-att-value="c.id" t-att-selected="c.id == checkout.get('country_id')">
                                                <t t-esc="c.name"/>
                                            </option>
                                        </t>
                                    </select>
                                </div>
                                <div t-attf-class="form-group #{error.get('state_id') and 'has-error' or ''} col-md-6 div_state" t-att-style="(not country or not country.state_ids) and 'display: none'">
                                    <label class="control-label" for="state_id">State / Province</label>
                                    <select name="state_id" class="form-control" data-init="1">
                                        <option value="">State / Province...</option>
                                        <t t-foreach="country and country.state_ids or []" t-as="s">
                                            <option t-att-value="s.id" t-att-selected="s.id == ('state_id' in checkout and country and checkout['state_id'] != '' and int(checkout['state_id']))">
                                                <t t-esc="s.name"/>
                                            </option>
                                        </t>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <input type="submit" value="Confirm" class="confirm_button"/>
                    <br/>
                </form>
            </div>
        </div>
    </t>
</template>

        <!-- New temp.end -->
        <template id="total123">
            <div id="cart_total" align="right" t-if="website_sale_order">
                <div class="row" id="order_total_untaxed">
                    <!-- <span class="col-xs-6 text-right text-muted">Subtotal:</span> -->
                    <span class="col-xs-6 text-right-not-xs text-left-xs text-muted">
                        <span>Subtotal :</span>
                        <span t-field="website_sale_order.untaxed_amt" style="white-space: nowrap;" t-options="{'widget': 'monetary','from_currency': website_sale_order.pricelist_id.currency_id,'display_currency': website.currency_id}"/>
                    </span>
                </div>
                <div class="row" id="order_total_taxes">
                    <!-- <span class="col-xs-6 text-right text-muted" title="Taxes may be updated after providing shipping address"> Taxes:</span> -->
                    <span class="col-xs-6 text-right-not-xs text-left-xs text-muted">
                        <span>Taxes :</span>
                        <span t-field="website_sale_order.total_tax" style="white-space: nowrap;" t-options="{'widget': 'monetary', 'from_currency': website_sale_order.pricelist_id.currency_id, 'display_currency': website.currency_id}"/>
                    </span>
                </div>
                <div class="row" id="order_total">
                    <hr class="mt8 mb0"/>
                    <!-- <span class="col-xs-6 text-right h4 mt0">Total:</span> -->
                    <span class="col-xs-6 text-right-not-xs text-left-xs h4 mt0" style="white-space: nowrap;">
                        <span>Total :</span>
                        <span t-field="website_sale_order.total_cost1" style="white-space: nowrap;" t-options="{'widget': 'monetary','from_currency': website_sale_order.pricelist_id.currency_id,'display_currency': website.currency_id}"/>
                    </span>
                </div>
            </div>
        </template>
        

<template id="payment123" name="Payment">
    <t t-call="website.layout">
        <t t-set="show_wizard_checkout" t-value="False"/>
        <t t-set="additional_title">Shop - Select Payment Method</t>
        <t t-set='redirect' t-valuef="/shop/payment"/>
        <t t-set="oe_structure">
            <!-- This is the drag-and-drop area for website building blocs at the end of each
                checkout page. This is append at the of the page in `checkout_layout`. The
                templates created in the database to store blocs are hooked using XPath on the
                `oe_structure` element ID. Therefore, we can't use dynamic IDs (like with
                t-att-id) and each template needs to define a div element. -->
            <div class="oe_structure" id="oe_structure_website_sale_payment_2"/>
        </t>
        <div class="col-12" t-if="errors">
            <t t-set="hide_payment_button" t-value="True"/>
            <t t-foreach="errors" t-as="error">
                <div class="alert alert-danger" t-if="error" role="alert">
                    <h4>
                        <t t-esc="error[0]" />
                    </h4>
                    <t t-esc="error[1]" />
                </div>
            </t>
        </div>
        <h3 class="mb-4">Confirm order</h3>
        <div class="col-12 col-xl order-xl-1 oe_cart">
            <table class="table table-striped table-condensed" id="cart_products"
                t-if="website_sale_order and website_sale_order.reservation_line">
                <thead>
                    <tr>
                        <th class='td-img'>Rooms</th>
                        <th></th>
                        <th class='td-qty'>No of Days</th>
                        <th class='text-center td-price'>Price</th>
                    </tr>
                </thead>
                <tbody>
                    <tr t-foreach="website_sale_order.reservation_line" t-as="line">
                        <td class='' colspan="2"
                                    t-if="not line.room_number.product_tmpl_id"></td>
                        <td class='td-img' t-if="line.room_number.product_tmpl_id">
                            <a
                                        t-attf-href="/shop/product/#{ slug(line.room_number.product_tmpl_id) }">
                                <span t-field="line.room_number.image_128"
                                            t-options="{'widget': 'image', 'class': 'img-rounded'}" />
                            </a>
                        </td>
                        <td class='td-product_name' t-if="line.room_number.product_tmpl_id">
                            <div>
                                <strong
                                            t-field="line.room_number.with_context(display_default_code=False).display_name" />
                            </div>
                            <div class="text-muted hidden-xs small">
                                <t t-foreach="line.room_number.name.splitlines()[1:]"
                                            t-as="name_line">
                                    <span>
                                        <t t-esc="name_line" />
                                    </span>
                                    <br />
                                </t>
                            </div>
                        </td>
                        <td class='td-qty'>
                            <div t-esc="line.number_of_days" />
                        </td>
                        <td class="text-center td-price">
                            <span t-field="line.sub_total1" style="white-space: nowrap;"
                                        t-options="{'widget': 'monetary','from_currency': website_sale_order.pricelist_id.currency_id,'display_currency': website.currency_id}" />
                        </td>
                    </tr>
                </tbody>
            </table>
            <div class="oe_structure clearfix mt-3" id="oe_structure_website_sale_payment_1"/>
            <div class="col-md-12">
                <t t-call="hotel_online.total123">
                    <t t-set='extra_class' t-value='"col-xs-12 col-sm-5 col-sm-push-7"' />
                    <t t-set='redirect'>/shop/payment</t>
                </t>
            </div>
            <t t-if="website_sale_order.total_cost1">
                <div class="mt-4">
                    <h3>Pay with</h3>
                </div>
                <div t-if="payment_methods_sudo or tokens_sudo"
                    id="payment_method"
                    class="mt-4">
                    <t t-call="payment.form"/>
                </div>
                <div t-else="" class="alert alert-warning">
                    <t t-set="hide_payment_button" t-value="True"/>
                    <strong>No suitable payment option could be found.</strong><br/>
                    <div t-if="not request.env.is_admin()">
                        If you believe that it is an error, please contact the website administrator.
                    </div>
                    <div class="mt-2" groups="base.group_system">
                        <a t-if="request.env.company.country_id.is_stripe_supported_country"
                            t-attf-href="/web#action=#{action_activate_stripe_id}"
                            role="button"
                            class="btn btn-primary"
                            t-out="'ACTIVATE STRIPE'"
                        />
                        <div t-else=""
                            class="d-inline"
                            title="Stripe Connect is not available in your country, please use another payment provider."
                        >
                            <button t-out="'ACTIVATE STRIPE'" class="btn btn-primary" disabled="true"/>
                        </div>
                        <a role="button"
                            class="btn-link alert-warning ps-2"
                            t-attf-href="/web#action=#{payment_action_id}"
                        >
                            <strong><i class="oi oi-arrow-right"></i> View alternatives</strong>
                        </a>
                    </div>
                </div>
            </t>
        </div>

        <table class="table table-striped table-condensed" id="cart_products"
            t-if="website_sale_order and website_sale_order.other_items_ids">
        </table>
        <div class="oe_structure clearfix mt-3" id="oe_structure_website_sale_payment_1"/>
        <div t-if="not providers" class="mt-2">
            <a role="button" class="btn-link"
                groups="base.group_system"
                t-attf-href="/web#action=#{payment_action_id}">
                <i class="fa fa-arrow-right"></i> Add payment providers
            </a>
        </div>
        <div class="js_payment mt-3" t-if="not website_sale_order.total_cost1" id="payment_method">
            <form target="_self" action="/shop/payment/validate" method="post" class="float-right">
                <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()" />
                <a role="button" class="btn btn-primary a-submit" href="#">
                    <span t-if="order.total_cost1 &gt; 0">Pay Now
                        <span class="fa fa-chevron-right"></span>
                    </span>
                    <span t-if="order.total_cost1 == 0">Confirm Order
                        <span class="fa fa-chevron-right"></span>
                    </span>
                </a>
            </form>
        </div>

    </t>
</template>


    <template id="confirmation1">
        <t t-call="website.layout">
            <t t-set="additional_title">Reservation - Confirmed</t>
            <div id="wrap">
                <div class="container oe_website_sale">
                    <ul class="wizard pull-right">
                        <li class="text-muted">
                            Review Order
                            <span class="chevron"></span>
                        </li>
                        <li class="text-muted">Shipping &amp; Billing
                            <span class="chevron"></span>
                        </li>
                        <li class="text-muted">
                            Payment
                            <span class="chevron"></span>
                        </li>
                        <li class="text-primary">
                            Confirmation
                            <span class="chevron"></span>
                        </li>
                    </ul>
                    <h1 class="mb32">Order
                        <em t-field="order.reservation_no"/>
                        Confirmed
                    </h1>
                    <div class="row">
                        <div class="col-md-8 oe_cart">
                            <h2>Thank you for your order.</h2>
                            <div class="oe_website_sale_tx_status" t-att-data-order-id="order.id">
                            </div>
                            <div class="clearfix"/>
                            <div class="oe_structure"/>
                        </div>
                        <div class="col-md-3 col-md-offset-1 text-muted" id="right_column">
                            <h4>Bill To:</h4>
                            <div t-field="order.partner_id" t-field-options='{
                                    "widget": "contact",
                                    "fields": ["address", "name", "phone"]
                                    }'/>
                            <div>
                                <t t-esc="order.partner_id.street"/>
                                ,
                                <t t-esc="order.partner_id.street2"/>
                            </div>
                            <div t-field="order.partner_id.street2"/>
                            <div>
                                <t t-esc="order.partner_id.zip"/>
                                ,
                                <t t-esc="order.partner_id.state_id.name"/>
                            </div>
                            <div t-field="order.partner_id.country_id"/>
                            <t>
                                <h4 class="mt32">Ship To:</h4>
                                <!-- <div t-field="order.partner_id" t-field-options='{ "widget": 
                                        "contact", "fields": ["address", "name", "phone"] }'/> -->
                                <address t-if="order.partner_id.id == order.partner_id.id">Ship to the same address
                                </address>
                            </t>
                        </div>
                    </div>
                </div>
                <div class="oe_structure"/>
            </div>
        </t>
    </template>
</data>
</odoo>