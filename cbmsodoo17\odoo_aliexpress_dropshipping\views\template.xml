<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) 2016-Present Webkul Software Pvt. Ltd. (<https://webkul.com/>) -->
<!-- See LICENSE file for full copyright and licensing details. -->
<!-- License URL : https://store.webkul.com/license.html/ -->

<odoo>
    <data>

        <!-- <template id="product_template_inherit_in_aliexpress" inherit_id="website_sale.product">
            <xpath expr="//section//div//a[@id='add_to_cart']" position="after">
                <t t-set="show_del_time" t-value="request.env['res.config.settings'].sudo().get_values()['ds_display_shipping_time']"/>
                <div t-if="show_del_time and product.ali_shipping_time">
                    <hr/>
                    <span style="color: #777777;">Estimated Delivery Time: </span> <span><t t-esc="product.ali_shipping_time"/></span>
                </div>
                <div id="aliexpress_product" t-att-data-aliexpress-product="'1' if product.is_drop_ship_product else '0'"/>
            </xpath>
        </template> -->

          <template id="cart_line_validate_lines_in_aliexpress" inherit_id="website_sale.cart_lines">
            <xpath expr="//div[@name='website_sale_cart_line_quantity']" position="inside">
                <div id="aliexpress_product" t-att-data-aliexpress-product="'1' if line.product_id.is_drop_ship_product else '0'"/>
            </xpath>
        </template>

        <template id="wk_add_attribute_images" inherit_id="website_sale.variants">
            <xpath expr="//ul/t/li" position="inside">
                <t t-if="ptal.attribute_id.display_type == 'ali_image'">
                    <ul id="ali_image_ul" t-att-data-attribute_id="ptal.attribute_id.id" t-attf-class="list-inline  #{'d-none' if single_and_custom else ''}">
                        <li t-foreach="ptal.product_template_value_ids._only_active()" t-as="ptav" class="list-inline-item">
                            <label t-attf-class="css_attribute_color #{'active' if ptav in combination else ''} #{'custom_value' if ptav.is_custom else ''}">
                                <input style="display: none;" type="radio" t-attf-class="js_variant_change  #{ptal.attribute_id.create_variant}" t-att-checked="ptav in combination" t-att-name="'ptal-%s' % ptal.id" t-att-value="ptav.id" t-att-title="ptav.name" t-att-data-value_id="ptav.id" t-att-data-value_name="ptav.name" t-att-data-attribute_name="ptav.attribute_id.name" t-att-data-is_custom="ptav.is_custom" t-att-data-is_single="single" t-att-data-is_single_and_custom="single_and_custom"/>
                                <img t-attf-src="/web/image/product.template.attribute.value/#{ptav.id}/ali_image" t-att-alt="ptav.name" t-att-title="ptav.name" class="aliexpress_image_type"/>
                            </label>
                        </li>
                    </ul>
                </t>
            </xpath>
        </template>

    </data>
</odoo>
