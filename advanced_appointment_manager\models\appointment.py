# -*- coding: utf-8 -*-
from odoo import api, fields, models, _
from odoo.exceptions import UserError
from datetime import timedelta
import logging

_logger = logging.getLogger(__name__)

class WebsiteAppointmentAppointment(models.Model):
    _inherit = 'calendar.event'

    approval_state = fields.Selection([
        ("pending", "Pending"),
        ("approved", "Approved"),
        ("rejected", "Rejected"),
        ("cancelled", "Cancelled"),
    ], string="Approval Status", default="pending", copy=False, tracking=True)

    checkin_state = fields.Selection([
        ("waiting", "Waiting for Client"),
        ("in_progress", "In Progress"),
        ("completed", "Completed"),
        ("no_show", "No Show"),
        ("forfeited", "Forfeited"),
    ], string="Check-in Status", default="waiting", copy=False, tracking=True)

    # Track original staff assignment for audit purposes
    original_user_id = fields.Many2one('res.users', string='Original Organizer', readonly=True, copy=False)
    staff_changed_by = fields.Many2one('res.users', string='Staff Changed By', readonly=True, copy=False)
    staff_change_date = fields.Datetime('Staff Change Date', readonly=True, copy=False)
    staff_change_reason = fields.Text('Staff Change Reason', copy=False)

    # Audit trail
    audit_log_ids = fields.One2many('appointment.audit.log', 'appointment_id', string='Audit Log', readonly=True)

    @api.model_create_multi
    def create(self, vals_list):
        """Override create to handle portal user creation for website appointments"""
        # Store original organizer for tracking
        for vals in vals_list:
            if 'user_id' in vals and vals.get('approval_state') == 'pending':
                vals['original_user_id'] = vals['user_id']

        events = super().create(vals_list)

        # Process each created event
        for event in events:
            # Check if this is a website appointment (has approval_state pending)
            if hasattr(event, 'approval_state') and event.approval_state == 'pending' and event.partner_id:
                _logger.info(f"Processing website appointment {event.id} for partner {event.partner_id.name}")

                # Grant portal access to the customer if they don't have it
                try:
                    self._grant_portal_access(event.partner_id)
                    _logger.info(f"Portal access granted/verified for partner {event.partner_id.name}")
                except Exception as e:
                    _logger.error(f"Failed to grant portal access for partner {event.partner_id.name}: {e}")

        return events

    def write(self, vals):
        """Override write to track staff changes and validate permissions"""
        # Store old values for notification purposes
        old_users = {}

        # Track staff changes for pending appointments
        if 'user_id' in vals:
            for record in self:
                if record.approval_state == 'pending':
                    # Check if user has permission to change staff
                    if not self.env.user.has_group('base.group_system'):
                        raise UserError(_("Only administrators can change the organizer of pending appointments."))

                    # Store old user for notifications
                    old_users[record.id] = record.user_id

                    # Log the staff change if it's actually changing
                    if record.user_id.id != vals['user_id']:
                        vals.update({
                            'staff_changed_by': self.env.user.id,
                            'staff_change_date': fields.Datetime.now(),
                        })
                        _logger.info(f"Staff changed for appointment {record.id}: {record.user_id.name} -> {self.env['res.users'].browse(vals['user_id']).name} by {self.env.user.name}")

                # Validate staff availability
                self._validate_staff_availability(vals['user_id'])

        # Validate attendee changes
        if 'partner_ids' in vals:
            partner_ids = []
            for command in vals['partner_ids']:
                if command[0] == 6:  # Replace command
                    partner_ids = command[2]
                elif command[0] == 4:  # Add command
                    partner_ids.append(command[1])
            if partner_ids:
                self._validate_attendee_changes(partner_ids)

        result = super().write(vals)

        # Send notifications and log changes for staff changes
        if 'user_id' in vals:
            for record in self:
                if record.id in old_users and record.approval_state == 'pending':
                    old_user = old_users[record.id]
                    if old_user != record.user_id:
                        # Log the change in audit trail
                        self.env['appointment.audit.log'].log_staff_change(
                            record.id,
                            old_user.id if old_user else False,
                            record.user_id.id if record.user_id else False,
                            vals.get('staff_change_reason')
                        )

                        # Send notifications
                        record._notify_staff_change(old_user, record.user_id, vals.get('staff_change_reason'))
                        record._sync_attendees_with_organizer()

        return result

    def _grant_portal_access(self, partner):
        """Grant portal access to a partner and send invitation email if needed"""
        if not partner or not partner.email:
            _logger.warning(f"Cannot grant portal access: partner {partner} has no email")
            return False

        email_login = partner.email.lower()

        # Check if user already exists (including inactive users)
        existing_user = self.env['res.users'].with_context(active_test=False).search([('login', '=', email_login)], limit=1)
        if existing_user:
            _logger.info(f"User with login '{email_login}' already exists. Checking access...")

            # Get important groups
            portal_group = self.env.ref('base.group_portal', raise_if_not_found=False)
            internal_user_group = self.env.ref('base.group_user', raise_if_not_found=False)

            if not portal_group:
                _logger.error("Portal group not found")
                return False

            # Check if user already has portal access
            if existing_user.active and portal_group in existing_user.groups_id:
                _logger.info(f"User {existing_user.login} already has portal access")
                return True

            # Check if user is an internal user (has more privileges than portal)
            if existing_user.active and internal_user_group and internal_user_group in existing_user.groups_id:
                _logger.info(f"User {existing_user.login} is an internal user - no need for portal access")
                return True

            # If user is inactive, reactivate and give portal access
            if not existing_user.active:
                existing_user.write({
                    'active': True,
                    'groups_id': [(6, 0, [portal_group.id])]  # Replace groups with only portal group
                })
                _logger.info(f"Reactivated user {existing_user.login} with portal access")
                return True

            # If user is active but has no portal access and is not internal user
            if portal_group not in existing_user.groups_id:
                # Replace user groups with portal group to avoid conflicts
                existing_user.write({
                    'groups_id': [(6, 0, [portal_group.id])]  # Replace all groups with portal group
                })
                _logger.info(f"Granted portal access to existing user {existing_user.login}")
                return True

            return True

        # Create new portal user only if one doesn't exist
        try:
            _logger.info(f"Creating new portal user for {partner.name} ({email_login})")

            # Use the portal wizard to create the user properly
            portal_wizard = self.env['portal.wizard'].create({
                'user_ids': [(0, 0, {
                    'partner_id': partner.id,
                    'email': partner.email,
                    'in_portal': True,
                })]
            })
            portal_wizard.action_apply()
            _logger.info(f"Created portal user and sent invitation email to {partner.email}")

        except Exception as e:
            _logger.error(f"Could not create portal user for {partner.name}: {e}")
            return False

        return True

    def _validate_staff_availability(self, user_id):
        """Validate if the selected staff member is available for the appointment time"""
        if not user_id:
            return True

        user = self.env['res.users'].browse(user_id)
        if not user.exists():
            raise UserError(_("Selected organizer does not exist."))

        # Check for conflicting appointments (optional - can be enabled based on requirements)
        # This is a basic check - you might want to make it more sophisticated
        conflicting_events = self.env['calendar.event'].search([
            ('user_id', '=', user_id),
            ('start', '<', self.stop),
            ('stop', '>', self.start),
            ('id', '!=', self.id),
            ('approval_state', 'in', ['approved', 'pending'])
        ])

        if conflicting_events:
            _logger.warning(f"Staff member {user.name} has {len(conflicting_events)} conflicting appointments")
            # Note: We're not raising an error here as it might be intentional
            # You can uncomment the next line if you want to enforce strict availability
            # raise UserError(_("Selected organizer has conflicting appointments during this time."))

        return True

    def _validate_attendee_changes(self, partner_ids):
        """Validate attendee changes for pending appointments"""
        if not partner_ids:
            return True

        # Ensure all partners exist and are valid
        partners = self.env['res.partner'].browse(partner_ids)
        invalid_partners = partners.filtered(lambda p: not p.exists())
        if invalid_partners:
            raise UserError(_("Some selected attendees do not exist."))

        return True

    def action_change_staff(self, new_user_id, reason=None):
        """Helper method to change staff with proper validation and logging"""
        self.ensure_one()

        if self.approval_state != 'pending':
            raise UserError(_("Staff can only be changed for pending appointments."))

        if not self.env.user.has_group('base.group_system'):
            raise UserError(_("Only administrators can change appointment staff."))

        old_user = self.user_id
        self._validate_staff_availability(new_user_id)

        self.write({
            'user_id': new_user_id,
            'staff_change_reason': reason or f"Staff changed from {old_user.name} to {self.env['res.users'].browse(new_user_id).name}"
        })

        _logger.info(f"Staff successfully changed for appointment {self.id}")
        return True

    def _sync_attendees_with_organizer(self):
        """Ensure the organizer is included in attendees if not already present"""
        self.ensure_one()

        if self.user_id and self.user_id.partner_id:
            organizer_partner = self.user_id.partner_id
            if organizer_partner not in self.partner_ids:
                self.partner_ids = [(4, organizer_partner.id)]
                _logger.info(f"Added organizer {organizer_partner.name} to attendees for appointment {self.id}")

    def _notify_staff_change(self, old_user, new_user, reason=None):
        """Send notifications when staff is changed"""
        self.ensure_one()

        # Prepare notification message
        subject = f"Staff Assignment Changed - {self.name}"
        body = f"""
        <p>The staff assignment for appointment "<strong>{self.name}</strong>" has been changed:</p>
        <ul>
            <li><strong>Previous Organizer:</strong> {old_user.name if old_user else 'None'}</li>
            <li><strong>New Organizer:</strong> {new_user.name if new_user else 'None'}</li>
            <li><strong>Changed By:</strong> {self.env.user.name}</li>
            <li><strong>Date:</strong> {fields.Datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</li>
            <li><strong>Appointment Date:</strong> {self.start.strftime('%Y-%m-%d %H:%M:%S') if self.start else 'Not set'}</li>
        </ul>
        """

        if reason:
            body += f"<p><strong>Reason:</strong> {reason}</p>"

        # Notify the new organizer
        if new_user and new_user.partner_id:
            self.message_post(
                body=body,
                subject=subject,
                partner_ids=[new_user.partner_id.id],
                subtype_xmlid='mail.mt_note'
            )

        # Notify the old organizer if different
        if old_user and old_user.partner_id and old_user != new_user:
            self.message_post(
                body=body,
                subject=subject,
                partner_ids=[old_user.partner_id.id],
                subtype_xmlid='mail.mt_note'
            )

        # Log the change in chatter
        self.message_post(
            body=f"Staff assignment changed from {old_user.name if old_user else 'None'} to {new_user.name if new_user else 'None'}",
            subtype_xmlid='mail.mt_note'
        )

    def get_available_staff(self):
        """Get list of available staff members for the appointment time"""
        self.ensure_one()

        # Get all users who can be organizers (you might want to filter by specific groups)
        all_users = self.env['res.users'].search([
            ('active', '=', True),
            ('share', '=', False)  # Exclude portal users
        ])

        if not self.start or not self.stop:
            return all_users

        # Find users without conflicting appointments
        available_users = self.env['res.users']
        for user in all_users:
            conflicting_events = self.env['calendar.event'].search([
                ('user_id', '=', user.id),
                ('start', '<', self.stop),
                ('stop', '>', self.start),
                ('id', '!=', self.id),
                ('approval_state', 'in', ['approved', 'pending'])
            ])

            if not conflicting_events:
                available_users |= user

        return available_users

    def _check_staff_edit_permission(self):
        """Check if current user has permission to edit staff assignments"""
        self.ensure_one()

        # Only allow staff editing for pending appointments
        if self.approval_state != 'pending':
            return False

        # System administrators can always edit
        if self.env.user.has_group('base.group_system'):
            return True

        # No one else can edit staff during pending approval
        return False

    @api.model
    def check_access_rights(self, operation, raise_exception=True):
        """Override to add custom access control for staff editing"""
        result = super().check_access_rights(operation, raise_exception)

        # Additional check for write operations on pending appointments
        if operation == 'write' and self:
            for record in self:
                if record.approval_state == 'pending' and not record._check_staff_edit_permission():
                    if raise_exception:
                        raise UserError(_("You don't have permission to modify pending appointments."))
                    return False

        return result

    def action_approve(self):
        """Approve appointment and send notification"""
        for appt in self:
            if appt.approval_state != 'pending':
                _logger.warning(f"Appointment {appt.id} is not in pending state, current state: {appt.approval_state}")
                continue

            _logger.info(f"Approving appointment {appt.id} for partner {appt.partner_id.name}")

            # Log the approval change
            self.env['appointment.audit.log'].log_approval_change(
                appt.id, 'pending', 'approved',
                f"Appointment approved by {self.env.user.name}"
            )

            # Update appointment status
            appt.write({'approval_state': 'approved', 'checkin_state': 'waiting'})

            # Grant portal access to customer
            if appt.partner_id:
                portal_granted = appt._grant_portal_access(appt.partner_id)
                if portal_granted:
                    _logger.info(f"Portal access granted for appointment {appt.id}")
                else:
                    _logger.warning(f"Failed to grant portal access for appointment {appt.id}")

            # Send approval notification email
            template = self.env.ref('advanced_appointment_manager.email_template_appointment_approved_aam', raise_if_not_found=False)
            if template:
                try:
                    template.send_mail(appt.id, force_send=True)
                    _logger.info(f"Sent approval email for appointment {appt.id}")
                except Exception as e:
                    _logger.error(f"Failed to send approval email for appointment {appt.id}: {e}")
            else:
                _logger.error("Approval email template not found")

        return True

    def action_reject(self):
        """Reject appointment and send notification"""
        for appt in self:
            if appt.approval_state != 'pending':
                _logger.warning(f"Appointment {appt.id} is not in pending state, current state: {appt.approval_state}")
                continue

            _logger.info(f"Rejecting appointment {appt.id} for partner {appt.partner_id.name}")

            # Log the rejection change
            self.env['appointment.audit.log'].log_approval_change(
                appt.id, 'pending', 'rejected',
                f"Appointment rejected by {self.env.user.name}"
            )

            # Update appointment status
            appt.write({'approval_state': 'rejected'})

            # Send rejection notification email
            template = self.env.ref('advanced_appointment_manager.email_template_appointment_rejected_aam', raise_if_not_found=False)
            if template:
                try:
                    template.send_mail(appt.id, force_send=True)
                    _logger.info(f"Sent rejection email for appointment {appt.id}")
                except Exception as e:
                    _logger.error(f"Failed to send rejection email for appointment {appt.id}: {e}")
            else:
                _logger.error("Rejection email template not found")

        return True

    def action_check_in(self):
        self.ensure_one()
        self.checkin_state = "in_progress"

    def action_mark_completed(self):
        self.ensure_one()
        self.checkin_state = "completed"

    def action_mark_no_show(self):
        self.ensure_one()
        self.checkin_state = "no_show"

    def action_mark_forfeited(self):
        self.ensure_one()
        if self.checkin_state == 'forfeited': return
        self.checkin_state = 'forfeited'
        template = self.env.ref('advanced_appointment_manager.email_template_appointment_forfeited_aam', raise_if_not_found=False)
        if template:
            # Changed to False to add email to queue for inspection.
            template.send_mail(self.id, force_send=False)

    def action_cancel(self):
        if not self.filtered(lambda appt: appt.approval_state in ['pending', 'approved']): return False
        return self.write({'approval_state': 'cancelled'})

    @api.model
    def _cron_update_appointment_status(self):
        company = self.env.company
        lateness_policy = company.lateness_policy
        if lateness_policy == 'none': return
        now = fields.Datetime.now()
        search_window_past = now - timedelta(days=2)
        search_window_future = now + timedelta(days=2)
        waiting_appointments = self.search([
            ('start', '>=', search_window_past), ('start', '<=', search_window_future),
            ('approval_state', '=', 'approved'), ('checkin_state', '=', 'waiting')
        ])
        if not waiting_appointments: return
        if lateness_policy == 'no_show':
            no_show_delay_minutes = company.no_show_delay_minutes
            for appt in waiting_appointments:
                if appt.start < now - timedelta(minutes=no_show_delay_minutes):
                    _logger.info(f"Marking appointment {appt.id} as No Show based on policy.")
                    appt.action_mark_no_show()
        elif lateness_policy == 'forfeit':
            forfeit_minutes_before = company.forfeit_minutes_before
            for appt in waiting_appointments:
                deadline = appt.start - timedelta(minutes=forfeit_minutes_before)
                if deadline <= now < appt.start:
                    _logger.info(f"Marking appointment {appt.id} as Forfeited based on policy.")
                    appt.action_mark_forfeited()

    def test_email_templates(self):
        """Test method to verify email templates are working"""
        self.ensure_one()
        _logger.info("Testing email templates for advanced appointment manager")

        # Test approval template
        approval_template = self.env.ref('advanced_appointment_manager.email_template_appointment_approved_aam', raise_if_not_found=False)
        if approval_template:
            _logger.info(f"✅ Approval email template found: {approval_template.name}")
        else:
            _logger.error("❌ Approval email template not found")

        # Test rejection template
        rejection_template = self.env.ref('advanced_appointment_manager.email_template_appointment_rejected_aam', raise_if_not_found=False)
        if rejection_template:
            _logger.info(f"✅ Rejection email template found: {rejection_template.name}")
        else:
            _logger.error("❌ Rejection email template not found")

        # Test forfeited template
        forfeited_template = self.env.ref('advanced_appointment_manager.email_template_appointment_forfeited_aam', raise_if_not_found=False)
        if forfeited_template:
            _logger.info(f"✅ Forfeited email template found: {forfeited_template.name}")
        else:
            _logger.error("❌ Forfeited email template not found")

        # Show user notification
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Email Templates Test',
                'message': 'Email templates test completed. Check server logs for details.',
                'type': 'success',
            }
        }

    def debug_appointment_flow(self):
        """Debug method to check appointment flow"""
        self.ensure_one()
        _logger.info("=== DEBUGGING APPOINTMENT FLOW ===")
        _logger.info(f"Current appointment ID: {self.id}")
        _logger.info(f"Approval state: {self.approval_state}")
        _logger.info(f"Check-in state: {self.checkin_state}")
        _logger.info(f"Partner: {self.partner_id.name if self.partner_id else 'None'}")
        _logger.info(f"Partner email: {self.partner_id.email if self.partner_id else 'None'}")

        # Check if partner has portal access
        portal_status = "No partner"
        if self.partner_id and self.partner_id.email:
            existing_user = self.env['res.users'].search([('login', '=', self.partner_id.email.lower())], limit=1)
            if existing_user:
                _logger.info(f"Partner has user account: {existing_user.login} (Active: {existing_user.active})")
                _logger.info(f"User groups: {[g.name for g in existing_user.groups_id]}")
                portal_status = f"User exists: {existing_user.login} (Active: {existing_user.active})"
            else:
                _logger.info("Partner does not have user account")
                portal_status = "No user account"

        _logger.info("=== END DEBUG ===")

        # Show user notification with debug info
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Debug Information',
                'message': f'Appointment {self.id}: {self.approval_state} | Portal: {portal_status}. Check server logs for full details.',
                'type': 'info',
            }
        }