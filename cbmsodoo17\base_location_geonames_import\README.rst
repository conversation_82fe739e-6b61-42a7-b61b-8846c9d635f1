=============================
Base Location Geonames Import
=============================

.. 
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! This file is generated by oca-gen-addon-readme !!
   !! changes will be overwritten.                   !!
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! source digest: sha256:2fd6b1dbd59c6e4f741bbdd1640c5533f64c48bc1aa1ebb64878348ab207eb9a
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

.. |badge1| image:: https://img.shields.io/badge/maturity-Mature-brightgreen.png
    :target: https://odoo-community.org/page/development-status
    :alt: Mature
.. |badge2| image:: https://img.shields.io/badge/licence-AGPL--3-blue.png
    :target: http://www.gnu.org/licenses/agpl-3.0-standalone.html
    :alt: License: AGPL-3
.. |badge3| image:: https://img.shields.io/badge/github-OCA%2Fpartner--contact-lightgray.png?logo=github
    :target: https://github.com/OCA/partner-contact/tree/17.0/base_location_geonames_import
    :alt: OCA/partner-contact
.. |badge4| image:: https://img.shields.io/badge/weblate-Translate%20me-F47D42.png
    :target: https://translation.odoo-community.org/projects/partner-contact-17-0/partner-contact-17-0-base_location_geonames_import
    :alt: Translate me on Weblate
.. |badge5| image:: https://img.shields.io/badge/runboat-Try%20me-875A7B.png
    :target: https://runboat.odoo-community.org/builds?repo=OCA/partner-contact&target_branch=17.0
    :alt: Try me on Runboat

|badge1| |badge2| |badge3| |badge4| |badge5|

This module adds a wizard to import cities and/or city zip entries from
`Geonames <http://www.geonames.org/>`__ database.

**Table of contents**

.. contents::
   :local:

Configuration
=============

To access the menu to import city zip entries from Geonames you must be
part of the group *Administration / Settings*.

If you want/need to modify the default Geonames URL
(http://download.geonames.org/export/zip/), you can set the
*geonames.url* system parameter.

Usage
=====

Go to the menu *Contacts > Configuration > Localization > Import from
Geonames*. In the wizard, select one or several countries and click on
the *Import* button.

For each selected country, the wizard will delete all not detected
entries, download the latest version of the list of cities from
geonames.org and create new city zip entries.

Bug Tracker
===========

Bugs are tracked on `GitHub Issues <https://github.com/OCA/partner-contact/issues>`_.
In case of trouble, please check there if your issue has already been reported.
If you spotted it first, help us to smash it by providing a detailed and welcomed
`feedback <https://github.com/OCA/partner-contact/issues/new?body=module:%20base_location_geonames_import%0Aversion:%2017.0%0A%0A**Steps%20to%20reproduce**%0A-%20...%0A%0A**Current%20behavior**%0A%0A**Expected%20behavior**>`_.

Do not contact contributors directly about support or help with technical issues.

Credits
=======

Authors
-------

* Akretion
* Agile Business Group
* Tecnativa
* AdaptiveCity

Contributors
------------

-  Alexis de Lattre <<EMAIL>>
-  Lorenzo Battistini <<EMAIL>>
-  Pedro M. Baeza <<EMAIL>>
-  Dave Lasley <<EMAIL>>
-  Jordi Ballester <<EMAIL>>
-  Franco Tampieri <<EMAIL>>
-  Aitor Bouzas <<EMAIL>>
-  Manuel Regidor <<EMAIL>>

Maintainers
-----------

This module is maintained by the OCA.

.. image:: https://odoo-community.org/logo.png
   :alt: Odoo Community Association
   :target: https://odoo-community.org

OCA, or the Odoo Community Association, is a nonprofit organization whose
mission is to support the collaborative development of Odoo features and
promote its widespread use.

This module is part of the `OCA/partner-contact <https://github.com/OCA/partner-contact/tree/17.0/base_location_geonames_import>`_ project on GitHub.

You are welcome to contribute. To learn how please visit https://odoo-community.org/page/Contribute.
