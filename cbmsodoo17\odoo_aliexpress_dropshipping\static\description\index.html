<!-- Copyright (c) 2016-Present Webkul Software Pvt. Ltd. (<https://webkul.com/>) -->
<!-- See LICENSE file for full copyright and licensing details. -->
<!-- License URL : https://store.webkul.com/license.html/ -->

<section class="oe_container">
    <section class="oe_container" id="wk_header">
        <div class="mt32 mb32">
            <h2 class="text-center" style="color:#000">
                Odoo Aliexpress Dropshipping
            </h2>
            <!-- <h5 class="mt8 mb8 text-center" style="font-weight:500; font-size:18px; color:#484848; font-family:'Montserrat', sans-serif">
                <i>MODULE-TAGLINE</i>
            </h5> -->
        </div>
        <div class="row mt16 mb16 py-2 m-1" style="font-weight:500; font-size:18px; color:#484848; font-family:'Montserrat', sans-serif">
            <div class="container col-md-6  border-right" style="font-weight:500;line-height: 24px;font-size:16px;color:#484848;font-family:'Montserrat', sans-serif; vertical-align: middle; display: grid;">
                <p class="mb4 mt4 mr8">
                    Now, integrate Aliexpress Dropshipping model into Odoo. With this module you can import and publish Aliexpress website products on your website. And when a customer places an order for that product you can send it to Aliexpress so they can deliver it directly to the customer. 
                </p>
            </div>
            <div class="container col-md-6 ">
                <h4 class="ml-md-3">Information</h4>
                <div class="mt-2 ml-md-3" style="display:flex">
                    <span class="ml-1 mr-2 my-auto"><img src="icon-user-guide.png" alt="user-guide"></span>
                    <div style="font-weight: 600;font-size: 14px;line-height: 10px;" class="mt-2">
                        User Guide
                    </div>
                </div>
                <div class="ml-md-3" style="display:flex;">
                    <span style="font-size:15px; margin-top:0.2rem">https://webkul.com/blog/odoo-aliexpress-dropshipping/</span>
                </div>
            </div>
        </div>
    </section>

    <section class="container">
        <div class="row my-4 mx-3">
            <div class="col-12 d-flex justify-content-center">
                <img src="Mask Group.png" class="img-fluid">
            </div>
        </div>
        <div class="row my-2 px-5 py-3 mx-3 shadow-sm p-3 mb-5 bg-white rounded" style="background-color:#FFFFFF; font-family:'Montserrat', sans-serif;">
            <div class="col-12 d-flex align-items-center mt32">
                <div class="d-flex align-items-center">
                    <img src="Group 85.png" class="mx-auto">
                </div>
                <div class="ml-3 d-flex align-items-center" style="color:#333333; font-size:26px; font-weight:600">
                    <h3 style="line-height:28px;font-size:calc(0.7rem + 1.0vw);">Now, Earn Twice the profits & No Inventory Management with Odoo &amp; Aliexpress Dropshipping</h3>
                </div>
            </div>
            <div class="col-12 mt-3" style="color:#11160D; text-align:justify; font-size:16px; font-weight:500">
                <div style="font-size: 16px;color: #5C5C5C;">
                    <p>Then, This is a module you Should Have on Your Odoo Website</p>
                </div>
                <div class="row mt32">
                    <div class="col-md-4 my-2">
                        <div class="p-3 shadow-sm p-3 mb-5 bg-white rounded" style="font-size: 16px;color: #555555;background: #FFFFFF;height: 80%;">
                            <div>
                                <img src="Group 69.png">
                            </div>
                            <div>
                                <p>Don&apos;t need to manage inventory</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 my-2">
                        <div class="p-3 shadow-sm p-3 mb-5 bg-white rounded" style="font-size: 16px;color: #555555;background: #FFFFFF;height: 80%;">
                            <div>
                                <img src="Group 71.png">
                            </div>
                            <div>
                                <p>No need to worry about delivery.</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 my-2">
                        <div class="p-3 shadow-sm p-3 mb-5 bg-white rounded" style="font-size: 16px;color: #555555;background: #FFFFFF;height: 80%;">
                            <div>
                                <img src="Rectangle 108.png">
                            </div>
                            <div>
                                <p>Just receive and transfer the customers order to Aliexpress and earn profit margin.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
        <div class="row my-4 px-5 py-3 mx-3 shadow-sm p-3 mb-5 bg-white rounded" style="background-color:#FFFFFF; font-family:'Montserrat', sans-serif;">
            <div class="col-12 d-flex align-items-center mt32">
                <div class="d-flex align-items-center">
                    <img src="Group 84.png" class="mx-auto">
                </div>
                <div class="ml-3 d-flex align-items-center" style="color:#333333; font-size:26px; font-weight:600">
                    <h3 style="line-height:28px;font-size:calc(0.7rem + 1.0vw);">Take Advantage of Aliexpress Dropshipping On Your Odoo Website</h3>
                </div>
            </div>
            <div class="col-12" style="color:#11160D; text-align:justify; font-size:16px; font-weight:500">
                <div style="font-size: 16px;color: #484848;font-weight: 600;
                font-size: 24px;line-height: 40px;">
                    What is Dropshipping?
                </div>
                <div class="mt32 d-flex justify-content-center">
                    <img src="flow.png" class="img-fluid">
                </div>
                <div class="my-4 p-4">
                    <div style="font-size: 16px;line-height:36px;color: #5C5C5C;">Dropshipping involves three participants/parties:</div>
                    <div style="font-size: 16px;line-height:36px;color: #5C5C5C;">
                        <ul>
                            <li>The customer who places the order - You odoo Website Customers</li>
                            <li>The seller accepts the order and brings it to the company/supplier - You as website admin.</li>
                            <li>The company who have the goods and deliver them- Here Aliexpress</li>
                        </ul>
                    </div>
                </div>
                <div class="my-3">
                    <div style="font-weight: 600;font-size: 24px;line-height: 60px;color: #484848;">Introducing Aliexpress Dropshipping in Odoo</div>
                    <div style="font-size: 16px;line-height:36px;color: #5C5C5C;">
                        <ul>
                            <li>You import Aliexpress Website product Data to your Odoo website so you can accept the customers orders for Aliexpress products</li>
                            <li>Then, send the orders to Aliexpress and Aliexpress delivers the product directly to the customers.</li>
                            <li>You do not need to manage, store or deliver the product to customers.</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="row my-4 px-5 py-3 mx-3 shadow-sm p-3 mb-5 bg-white rounded" style="background-color:#FFFFFF; border-radius:2px; font-family:'Montserrat', sans-serif;">
            <div class="col-12">
                <div style="color:#333333; font-size:26px; font-weight:600">
                    <h3 style="line-height:28px;font-size:calc(0.7rem + 1.0vw);">How Does Odoo Aliexpress Dropshipping work?</h3>
                </div>
            </div>
            <div class="col-12 my-3 d-flex justify-content-center">
                <img src="flowchart.png" class="img-fluid">        
            </div>
        </div>
        <div class="row my-4 px-5 py-3 mx-3 shadow-sm p-3 mb-5 bg-white rounded" style="background-color:#FFFFFF; border-radius:2px; font-family:'Montserrat', sans-serif;">
            <div class="col-12">
                <div style="color:#333333; font-size:26px; font-weight:600">
                    <h3 style="line-height:28px;font-size:calc(0.7rem + 1.0vw);">Why Use Odoo Aliexpress Dropshipping Module?</h3>
                </div>
            </div>
            <div class="col-12 my-3">
                <div class="row">
                    <div class="col-md-6">
                        <div class="p-4 m-2 shadow-sm p-3 mb-5 bg-white rounded" style="background-color:#FFFFFF; line-height: 28px;height: 90%;">
                            <ul>
                                <li>Minimal set and configuration required to use the module. Have the module up and running in minutes.
                                </li>
                                <li>Onelick product import from Aliexpress website To Odoo.
                                </li>
                                <li>Import, Publish and sell Aliexpress products on Odoo Website.
                                </li>
                                <li>Earn a profit margin by selling Aliexpress products on your Odoo website.
                                </li>
                                <li>No need to maintain inventory of products as products are drop shipped with Aliexpress.
                                </li>
                                <li>Save Delivery cost too as Aliexpress delivers the product directly to customers.
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-md-5">
                        <div class="p-4 m-2 shadow-sm p-3 mb-5 bg-white rounded" style="background-color:#FFFFFF; line-height: 28px;height: 90%;">
                            <ul>
                                <li>No need to keep product inventory
                                </li>
                                <li>One-step product Import
                                </li>
                                <li>Earn margins by selling Aliexpress products
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row mt16 mb32 p-3 d-flex justify-content-center mx-3" style="border: 1px solid #E8E8E8;font-family: Montserrat;">
                
            <div class="col-12 mt16 mb16">
                <h2 style="font-size:calc(1.0rem + 1vw);">KEY FEATURES</h2>
                <p style="color:#555555;font-size:14px;font-weight: 400;">Below is the detailed list of Feature for Odoo Aliexpress Dropshipping</p>
            </div>
            <div class="row px-3">
                <div class="col-md-6" style="padding: 0px;">
                    <div class="m-1 p-4 shadow-sm p-3 mb-5 bg-white rounded" style="background: #FFFFFF;padding:4% 4% 1% 4%;height: 90%;">
                        
                        <h4>Integrate Aliexpress Dropshipping Model With Odoo</h4>
                        <div style="color:#555555;font-size:15px;font-weight:400;">
                            <ul>
                                <li>Integrates the Aliexpress website with Odoo.</li>
                                <li>Import the products from Aliexpress to Odoo website.</li>
                                <li>Take the orders from customers, send them to Aliexpress and have them drop shipped.</li>
                                <li>No need to manage deliveries as they are fulfilled by Aliexpress.</li>
                                <li>Earn profit margins on each Aliexpress order on your Odoo website..</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-6" style="padding: 0px;">
                    <div class="m-1 p-4 shadow-sm p-3 mb-5 bg-white rounded" style=" background: #FFFFFF;padding:4% 4% 1% 4%;height: 90%;">
                        <h4>Get Free Chrome Extension To Import Aliexpress Product Data To Odoo</h4>
                    <div style="color:#555555;font-size:15px;font-weight:400;">
                        <ul>                        
                            <li>You get a Chrome extension with the module.</li>
                            <li>Use the extension to quickly import the product data from aliexpress website.</li>
                        </ul>
                    </div>
                    </div>
                </div>
            </div>
            <div class="row px-3">
                <div class="col-md-6" style="padding: 0px;">
                    <div class="m-1 p-4 shadow-sm p-3 mb-5 bg-white rounded" style=" background: #FFFFFF;padding:4% 4% 1% 4%;height: 90%;">
                        <h4>Import Desired Product Data as Feeds From Aliexpress Website to Odoo</h4>
                        <div style="color:#555555;font-size:15px;font-weight:400;">
                            <ul>                            
                                <li>You can choose what product data to import from respective Odoo settings.</li>
                                <li>You can enable/disable data import such as description, price, etc.</li>
                                <li>You can also set the price of the imported product as the same, higher or lower than on Aliexpress to add margins</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-6" style="padding: 0px;">
                    <div class="m-1 p-4 shadow-sm p-3 mb-5 bg-white rounded" style=" background: #FFFFFF;padding:4% 4% 1% 4%;height: 90%;">
                        <h4>Auto-Create Products From Imported Product Feeds</h4>
                        <div style="color:#555555;font-size:15px;font-weight:400;">
                            <ul>                            
                                <li>There are two options to auto-create products from product feed - instantly or using scheduler.</li>
                                <li>The scheduler comes configured with the module.</li>
                                <li>Alternatively, Instant auto-created can be enabled from settings.</li>
                                <li>If instant auto-created is disbaled, the scheduler creates the products at regular intervals.</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row px-3">
                <div class="col-md-6" style="padding: 0px;">
                    <div class="m-1 p-4 shadow-sm p-3 mb-5 bg-white rounded" style=" background: #FFFFFF;padding:4% 4% 1% 4%;height: 90%;">
                        <h4>Auto-Fetch Product updates from Aliexpress Website to Odoo</h4>
                        <div style="color:#555555;font-size:15px;font-weight:400;">
                            <ul>                            
                                <li>The module can automatically fetch updates of created products from Aliexpress.</li>
                                <li>The module comes with two default configured schedulers to update the product data.</li>
                                <li>One Cron updates the current price and quantity from Aliexpress to Odoo. (fast update)</li>
                                <li>Another scheduler updates all the product data such as description, pictures, etc. </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-6" style="padding: 0px;">
                    <div class="m-1 p-4 shadow-sm p-3 mb-5 bg-white rounded" style=" background: #FFFFFF;padding:4% 4% 1% 4%;height: 90%;">
                        <h4>Accept Aliexpress Dropship Order on Odoo website</h4>
                        <div style="color:#555555;font-size:15px;font-weight:400;">
                            <ul>                            
                                <li>Create products and Publish the imported products on Odoo Website.</li>
                                <li>The customer can place the order for aliexpress products on your website.</li>
                                <li>You can then send the order to Aliexpress with a click from Odoo.</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row px-3">
                <div class="col-md-6" style="padding: 0px;">
                    <div class="m-1 p-4 shadow-sm p-3 mb-5 bg-white rounded" style=" background: #FFFFFF;padding:4% 4% 1% 4%;height: 90%;">
                        <h4>Send accepted order to Aliexpress with a click</h4>
                        <div style="color:#555555;font-size:15px;font-weight:400;">
                            <ul>                            
                                <li>Click place order and you are redirected to Aliexpress.</li>
                                <li>The products of the selected order are automatically added to the cart.</li>
                                <li>Place the same order on the customer's behalf on Aliexpress website.</li>
                                <li>Aliexpress will deliver it directly to the customer.</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-6" style="padding: 0px;">
                    <div class="m-1 p-4 shadow-sm p-3 mb-5 bg-white rounded" style=" background: #FFFFFF;padding:4% 4% 1% 4%;height: 90%;">
                        <h4>Designed New Menu To show Aliexpress Orders separately in Odoo</h4>
                        <div style="color:#555555;font-size:15px;font-weight:400;">
                            <ul>                            
                                <li>Manage all the orders for Aliexpress products from a separate menu.</li>
                                <li>Moreover, manage imported products and data from this menu.</li>
                            </ul>
                            
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="mb32" style="background:#efefef78;padding: 20px 3%;font-size:17px;margin:20px 0;">
        <h2 style="margin: 0;">
            Note: <a href="https://chrome.google.com/webstore/detail/odoo-aliexpress-importer/ckeaieachgddaicacgljehgmneaabjbe" target="_blank">Click here</a> to download the chrome extension.
        </h2>
    </section>
    <section class="oe_container mp_tabs">
        <div class="mp-nav-center shadow-sm p-3 mb-5 bg-white rounded" style="background: #FFFFFF;text-align: center;">
            <ul class="mp-nav nav nav-tabs" style="display: inline-block;">
                <li class="nav-item active" style="background-color: #FFB93C;">
                    <a href="#tab1" class="nav-link active show" role="tab" aria-selected="true" data-bs-toggle="tab">
                        <h4 class="panel-title"> <b>Import Products</b> </h4>
                    </a>
                </li>
                <li class="nav-item" style="background-color: #FFB93C;">
                    <a href="#tab2" class="nav-link" role="tab" aria-selected="true" data-bs-toggle="tab">
                        <h4 class="panel-title"> <b>Dropship Orders</b> </h4>
                    </a>
                </li>
                <li class="nav-item" style="background-color: #FFB93C;">
                    <a href="#tab3" class="nav-link" role="tab" aria-selected="true" data-bs-toggle="tab">
                        <h4 class="panel-title"> <b>Chrome Ext Integration</b> </h4>
                    </a>
                </li>
                <li class="nav-item" style="background-color: #FFB93C;">
                    <a href="#tab4" class="nav-link" role="tab" aria-selected="true" data-bs-toggle="tab">
                        <h4 class="panel-title"> <b>Configuration</b> </h4>
                    </a>
                </li>
            </ul>
        </div>
        <div class="tab-content" id="my-tab-content">
            <div class="tab-pane container active" id="tab1">

                <div class="row">
                    <div class="col-md-12 col-lg-12 col-xs-12">
                        <section style="background:#efefef78;padding: 10px 6%;font-size:17px;margin:20px 0;">

                            <div class="row">
                                <div class="col-md-12 col-lg-12 col-xs-12">
                                    <h3 class="oe_slogan" style="color:#000000;font-weight: 400;font-size: 18px; margin-top: 10px; margin-bottom: 8px">
                                        <i class="fa fa-arrow-circle-o-right"></i> Import any Product from Aliexpress to Odoo with a click.
                                    </h3>
                                </div>
                                
                                <div class="col-md-12 col-lg-12 col-xs-12">
                                    <a href="Screenshot(9).png"><img style="width:100%;display:block;margin-left:auto;margin-right:auto;max-height:570px;margin-bottom: 4%;" class=" oe_screenshot" src="Screenshot(9).png"></a>
                                </div>
                                <div class="col-md-12 col-lg-12 col-xs-12">
                                    <a href="Screenshot(8).png"><img style="width:100%;display:block;margin-left:auto;margin-right:auto;max-height:570px;margin-bottom: 4%;" class=" oe_screenshot" src="Screenshot(8).png"></a>
                                </div>
                                <div class="col-md-12 col-lg-12 col-xs-12">
                                    <h3 class="oe_slogan" style="color:#000000;font-weight: 400;font-size: 18px; margin-top: 10px; margin-bottom: 8px">
                                        <i class="fa fa-arrow-circle-o-right"></i> Product Feeds are auto-generated in Odoo once the products are imported.
                                    </h3>
                                </div>
                                <div class="col-md-12 col-lg-12 col-xs-12">
                                    <a href="Screenshot(10).png"><img style="width:100%;display:block;margin-left:auto;margin-right:auto;max-height:570px;margin-bottom: 4%;" class=" oe_screenshot" src="Screenshot(10).png"></a>
                                </div>
                                <div class="col-md-12 col-lg-12 col-xs-12">
                                    <h3 class="oe_slogan" style="color:#000000;font-weight: 400;font-size: 18px; margin-top: 10px; margin-bottom: 8px">
                                        <i class="fa fa-arrow-circle-o-right"></i>  Create products in Odoo from Product feed. (Auto-create can also be enabled from settings)
                                    </h3>
                                </div>
                                <div class="col-md-12 col-lg-12 col-xs-12">
                                    <a href="Screenshot(11).png"><img style="width:100%;display:block;margin-left:auto;margin-right:auto;max-height:570px;margin-bottom: 4%;" class=" oe_screenshot" src="Screenshot(11).png"></a>
                                </div>

                                <div class="col-md-12 col-lg-12 col-xs-12">
                                    <h3 class="oe_slogan" style="color:#000000;font-weight: 400;font-size: 18px; margin-top: 10px; margin-bottom: 8px">
                                        <i class="fa fa-arrow-circle-o-right"></i>Publish the product on Odoo website Or enable Auto-publish from settings.
                                    </h3>
                                </div>
                                <div class="col-md-12 col-lg-12 col-xs-12">
                                    <a href="Screenshot(13).png"><img style="width:100%;display:block;margin-left:auto;margin-right:auto;max-height:570px;margin-bottom: 4%;" class=" oe_screenshot" src="Screenshot(13).png"></a>
                                </div>
                                <div class="col-md-12 col-lg-12 col-xs-12">
                                    <a href="Screenshot(14).png"><img style="width:100%;display:block;margin-left:auto;margin-right:auto;max-height:570px;margin-bottom: 4%;" class=" oe_screenshot" src="Screenshot(14).png"></a>
                                </div>
                                
                            </div>
                        </section>
                    </div>
                </div>
            </div>
            <div class="tab-pane container" id="tab2">

                <div class="row">
                    <div class="col-md-12 col-lg-12 col-xs-12">
                        <section style="background:#efefef78;padding: 10px 6%;font-size:17px;margin:20px 0;">

                            <div class="row">
                                <div class="col-md-12 col-lg-12 col-xs-12">
                                    <h3 class="oe_slogan" style="color:#000000;font-weight: 400;font-size: 18px; margin-top: 10px; margin-bottom: 8px">
                                        <i class="fa fa-arrow-circle-o-right"></i> The customers can place the orders for Aliexpress products on Odoo Website
                                    </h3>
                                </div>
                                <div class="col-md-12 col-lg-12 col-xs-12">
                                    <a href="Screenshot(15).png"><img style="width:100%;display:block;margin-left:auto;margin-right:auto;max-height:570px;margin-bottom: 4%;" class=" oe_screenshot" src="Screenshot(15).png"></a>
                                </div>
                                <div class="col-md-12 col-lg-12 col-xs-12">
                                    <h3 class="oe_slogan" style="color:#000000;font-weight: 400;font-size: 18px; margin-top: 10px; margin-bottom: 8px">
                                        <i class="fa fa-arrow-circle-o-right"></i> Send orders to Aliexpress with a click
                                    </h3>
                                    <li>Click on the Place Order button. You will be redirected to the aliexpress cart and the products are automatically added the Aliexpress website cart.</li>
                                    <li>Place the order on customer&apos;s behalf to have it drop shipped from Aliexoress to your customers directly.</li>
                                </div>
                                <div class="col-md-12 col-lg-12 col-xs-12">
                                    <a href="Screenshot(16).png"><img style="width:100%;display:block;margin-left:auto;margin-right:auto;max-height:570px;margin-bottom: 4%;" class=" oe_screenshot" src="Screenshot(16).png"></a>
                                </div>
                                
                            </div>
                        </section>
                    </div>
                </div>
            </div>
            <div class="tab-pane container" id="tab3">
                <div class="row">
                    <div class="col-md-12 col-lg-12 col-xs-12">
                        <section style="background:#efefef78;padding: 10px 6%;font-size:17px;margin:20px 0;">
                            <div class="row">
                                <div class="col-md-12 col-lg-12 col-xs-12">
                                    <h3 class="oe_slogan" style="color:#000000;font-weight: 400;font-size: 18px; margin-top: 10px; margin-bottom: 8px">
                                        <i class="fa fa-arrow-circle-o-right"></i> Facilitates single step integration of Chrome Extension with Aliexpress.
                                    </h3>
                                    <li>Use the extension to fetch the product data from Aliexpress website to Odoo.</li>
                                    <li>Token can be found in the users >> preferences in Odoo.</li>
                                    
                                </div>
                                <div class="col-md-12 col-lg-12 col-xs-12">
                                    <a href="Screenshot(5).png"><img style="width:100%;display:block;margin-left:auto;margin-right:auto;max-height:570px;margin-bottom: 4%;" class=" oe_screenshot" src="Screenshot(5).png"></a>
                                </div>
                                <div class="col-md-12 col-lg-12 col-xs-12">
                                    <a href="Screenshot(6).png"><img style="width:100%;display:block;margin-left:auto;margin-right:auto;max-height:570px;margin-bottom: 4%;" class=" oe_screenshot" src="Screenshot(6).png"></a>
                                </div>
                                <div class="col-md-12 col-lg-12 col-xs-12">
                                    <h3 class="oe_slogan" style="color:#000000;font-weight: 400;font-size: 18px; margin-top: 10px; margin-bottom: 8px">
                                        <i class="fa fa-arrow-circle-o-right"></i> Token can be found in the users >> preferences in Odoo.
                                    </h3>
                                </div>
                                <div class="col-md-12 col-lg-12 col-xs-12">
                                    <a href="Screenshot(17).png"><img style="width:100%;display:block;margin-left:auto;margin-right:auto;max-height:570px;margin-bottom: 4%;" class=" oe_screenshot" src="Screenshot(17).png"></a>
                                </div>
                                <div class="col-md-12 col-lg-12 col-xs-12">
                                    <h3 class="oe_slogan" style="color:#000000;font-weight: 400;font-size: 18px; margin-top: 10px; margin-bottom: 8px">
                                        <i class="fa fa-arrow-circle-o-right"></i> Configure quick product import settings in the Chrome extension
                                    </h3>
                                </div>
                                <div class="col-md-12 col-lg-12 col-xs-12">
                                    <a href="Screenshot(7).png"><img style="width:100%;display:block;margin-left:auto;margin-right:auto;max-height:570px;margin-bottom: 4%;" class=" oe_screenshot" src="Screenshot(7).png"></a>
                                </div>
                            </div>
                        </section>
                    </div>
                </div>
            </div>
            <div class="tab-pane container" id="tab4">
                <div class="row">
                    <div class="col-md-12 col-lg-12 col-xs-12">
                        <section style="background:#efefef78;padding: 10px 6%;font-size:17px;margin:20px 0;">
                            <div class="row">
                                <div class="col-md-12 col-lg-12 col-xs-12">
                                    <h3 class="oe_slogan" style="color:#000000;font-weight: 400;font-size: 18px; margin-top: 10px; margin-bottom: 8px">
                                        <i class="fa fa-arrow-circle-o-right"></i> Configure what product Data to fetch with products from Aliexpress to Odoo
                                    </h3>
                                    <li>You can choose to fetch features, descriptions, packaging details etc with the products in Odoo.</li>                                    
                                </div>
                                <div class="col-md-12 col-lg-12 col-xs-12">
                                    <a href="Screenshot.png"><img style="width:100%;display:block;margin-left:auto;margin-right:auto;max-height:570px;margin-bottom: 4%;" class=" oe_screenshot" src="Screenshot.png"></a>
                                </div>
                                <div class="col-md-12 col-lg-12 col-xs-12">
                                    <h3 class="oe_slogan" style="color:#000000;font-weight: 400;font-size: 18px; margin-top: 10px; margin-bottom: 8px">
                                        <i class="fa fa-arrow-circle-o-right"></i> Auto-Publish the created products on Odoo website
                                    </h3>
                                </div>
                                <div class="col-md-12 col-lg-12 col-xs-12">
                                    <a href="Screenshot(1).png"><img style="width:100%;display:block;margin-left:auto;margin-right:auto;max-height:570px;margin-bottom: 4%;" class=" oe_screenshot" src="Screenshot(1).png"></a>
                                </div>
                                <div class="col-md-12 col-lg-12 col-xs-12">
                                    <h3 class="oe_slogan" style="color:#000000;font-weight: 400;font-size: 18px; margin-top: 10px; margin-bottom: 8px">
                                        <i class="fa fa-arrow-circle-o-right"></i> Import same product price or add a margin accordingly
                                    </h3>
                                </div>
                                <div class="col-md-12 col-lg-12 col-xs-12">
                                    <a href="Screenshot(2).png"><img style="width:100%;display:block;margin-left:auto;margin-right:auto;max-height:570px;margin-bottom: 4%;" class=" oe_screenshot" src="Screenshot(2).png"></a>
                                </div>
                            </div>
                        </section>
                    </div>
                </div>
            </div>

        </div>
    </section>
</section>

<section class="pt32" id="webkul_support">
    <div class="row">
        <div class="col-12">
            <h2 class="text-center">Help and Support</h2>
            <p class="mb-2 text-center" style="font-size:16px; color:#333; font-weight:400">Get Immediate support for any of your query</p>
        </div>
        <div class="col-12">
            <p class="text-center px-5" style="font-size:14px; color:#555; font-weight:normal">You will get 90 days free support for any doubt, queries, and bug fixing (excluding data recovery) or any type of issue related to this module.</p>
        </div>
        <div class="mx-auto col-lg-9 mb-4 oe_screenshot">
            <div class="row align-items-center justify-content-center mx-0 p-3">
                <div class="col-sm-2 text-center pr-0">
                    <img src="mail.png" alt="mail" class="img img-fluid">
                </div>
                <div class="col-xl-7 col-sm-10">
                    <p class="my-2" style="color:#555; font-weight:bold">Write a mail to us:</p>
                    <b class="text-primary" style="font-size:18px"><EMAIL></b>
                    <p class="my-2" style="font-size:14px; color:#777; font-weight:normal">Any queries or want any extra features? Just drop a mail to our support.</p>
                </div>
                <div class="col-xl-3 offset-xl-0 float-xl-right col-sm-10 offset-sm-2 float-left mt16">
                    <a href="mailto:<EMAIL>" style="padding:10px 22px; background-color:#2335D7; font-size:14px; color:#fff"><i class="fa fa-pencil-square-o" style="color:white; margin-right:4px"></i>Write To US</a>
                </div>
            </div>
        </div>
        <div class="mx-auto col-lg-9 oe_screenshot">
            <div class="row align-items-center justify-content-center mx-0 p-3">
                <div class="col-sm-2 text-center pr-0">
                    <img src="support-icon.png" alt="support-icon" class="img img-fluid">
                </div>
                <div class="col-sm-10 ">
                    <p class="my-2" style="font-weight:bold; color:#555">Get in touch with our Expert:</p>
                    <b class="text-primary text-break" style="font-size:18px">https://webkul.uvdesk.com/en/customer/create-ticket/</b>
                    <p class="my-2" style="font-weight:normal; font-size:14px; color:#777">Have any technical queries, want extra features, or anything else? Our team is here to answer all your questions. Just Raise A Support Ticket.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<section class="oe_container">
    <div class="oe_span12">
        <!-- Piwik Image Tracker-->
        <img src="http://odooimg.webkul.com/analytics/piwik/piwik.php?idsite=3&rec=1&action_name=ODOO-ALIEXPRESS-DROPSHIPPING&url=https://apps.openerp.com/apps/modules/13.0/odoo_aliexpress_dropshipping&uid=odoo_aliexpress_dropshipping" style="border:0" alt="" />
        <!-- End Piwik -->
    </div>
</section>
