<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="view_wooc_product_image_form" model="ir.ui.view">
        <field name="name">woocommerce.product.variant.form</field>
        <field name="model">woocommerce.product.variant</field>
        <field name="arch" type="xml">
            <form string="WooCommerce Variants">
                <sheet>
                    <!-- <field name="wooc_variant_image" widget="image" class="oe_avatar" /> -->
                    <group>

                        <div class="oe_title">
                            <span class="d-block">
                                <h6 class="d-inline-flex">#<field name="wooc_id" readonly="1" /></h6>
                            </span>
                            <h2>
                                <field name="name" readonly="1" />
                            </h2>
                        </div>
                        <group>
                        </group>
                    </group>
                    <group>
                        <field name="product_template_id" string="Product Parent :" readonly="1" />
                    </group>

                    <group col="2">

                        <group>
                            <field name="wooc_sku" string="SKU:" />
                        </group>
                    </group>
                    <div name="options">
                        <span class="d-inline-block">
                            <field name="is_enabled" on_change="1" />
                            <label for="is_enabled" />
                        </span>
                        <span class="d-inline-block">
                            <field name="is_manage_stock" on_change="1" readonly="1" help="To enable, update product quantity."/>
                            <label for="is_manage_stock" />
                        </span>
                    </div>
                    <separator name="variant_data" string="Variant Details" />
                    <group>
                        <field name="wooc_regular_price" string="Regular Price :" />
                    </group>
                    <br />
                    <group>
                        <group>
                            <field name="wooc_stock_status" string="Status :" />
                        </group>
                        <group>
                            <field name="wooc_stock_quantity" string="Stock Quantity :" readonly="1" 
                            invisible = "is_manage_stock == False"/>
                        </group>
                    </group>
                    <br />
                    <group col="4">
                        <group>
                            <field name="wooc_v_weight" string="Weight :" />
                        </group>

                        <group col="3" colspan="3">
                            <group>
                                <field name="wooc_v_dimension_length" string="Length:" />
                            </group>
                            <group>
                                <field name="wooc_v_dimension_width" string="Width:" />
                            </group>
                            <group>
                                <field name="wooc_v_dimension_height" string="Height :" />
                            </group>
                        </group>
                    </group>
                    <group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="view_wooc_product_variant_kanban" model="ir.ui.view">
        <field name="name">woocommerce.product.variant.kanban</field>
        <field name="model">woocommerce.product.variant</field>
        <field name="arch" type="xml">
            <kanban string="WooCommerce Variants" create="0">
                <field name="name" />
                <field name="id" />
                <field name="wooc_id" />
                <field name="is_enabled" />
                <field name="wooc_variant_image" />

                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="oe_kanban_global_click"
                        t-att-style="! record.is_enabled.raw_value and 'background: #fdd;' or ''">
                            <div class="o_kanban_image">
                                <t t-if="record.wooc_variant_image.value">
                                    <img alt="Product Image"
                                        t-att-src="kanban_image('woocommerce.product.variant', 'wooc_variant_image',record.id.raw_value)" />
                                </t>
                            </div>
                            <div class="oe_kanban_details">
                                <strong class="o_kanban_record_title">
                                    <span class="d-block">
                                        <h6 class="d-inline-flex"> #<field name="wooc_id"
                                                readonly="1" />
                                        </h6>
                                    </span>
                                    <field name="name" />
                                </strong>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

</odoo>