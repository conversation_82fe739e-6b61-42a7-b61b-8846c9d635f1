<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">
    <record id="automatic_validation_ept" model="sale.workflow.process.ept">
        <field name="name">Automatic Validation</field>
        <field name="validate_order" eval="0"/>
        <field name="create_invoice" eval="0"/>
        <field name="invoice_date_is_order_date" eval="0"/>
        <field name="sale_journal_id" model="account.journal"
               eval="obj().search([('type', '=', 'sale'),('company_id', '=', obj().env.company.id)], limit=1).id"/>
    </record>
</odoo>
