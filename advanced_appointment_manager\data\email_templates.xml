<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="email_template_appointment_approved_aam" model="mail.template">
            <field name="name">Appointment Approved Notification</field>
            <field name="model_id" eval="ref('calendar.model_calendar_event')"/>
            <field name="subject">✅ Your appointment has been approved - ${object.name}</field>
            <field name="email_from">${(object.user_id.email_formatted or user.email_formatted or '<EMAIL>')|safe}</field>
            <field name="partner_to">${object.partner_id.id}</field>
            <field name="body_html" type="html">
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #f8f9fa; padding: 20px;">
                    <div style="background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                        <div style="text-align: center; margin-bottom: 30px;">
                            <div style="background-color: #28a745; color: white; padding: 15px; border-radius: 50px; display: inline-block; margin-bottom: 20px;">
                                <span style="font-size: 24px;">✅</span>
                            </div>
                            <h1 style="color: #28a745; margin: 0;">Appointment Approved!</h1>
                        </div>

                        <p style="font-size: 16px; color: #333;">Dear ${object.partner_id.name},</p>

                        <p style="font-size: 16px; color: #333;">
                            Great news! Your appointment request has been <strong style="color: #28a745;">APPROVED</strong>.
                        </p>

                        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0;">
                            <h3 style="color: #333; margin-top: 0;">Appointment Details:</h3>
                            <p style="margin: 5px 0;"><strong>Service:</strong> ${object.name}</p>
                            <p style="margin: 5px 0;"><strong>Date &amp; Time:</strong> <t t-out="format_datetime(object.start, tz=object.partner_id.tz or 'UTC', dt_format='EEEE, MMMM d, yyyy \'at\' h:mm a')"/></p>
                            <t t-if="object.location">
                                <p style="margin: 5px 0;"><strong>Location:</strong> ${object.location}</p>
                            </t>
                            <t t-if="object.description">
                                <p style="margin: 5px 0;"><strong>Notes:</strong> ${object.description}</p>
                            </t>
                        </div>

                        <div style="background-color: #e7f3ff; padding: 15px; border-radius: 6px; border-left: 4px solid #007bff; margin: 20px 0;">
                            <h4 style="color: #007bff; margin-top: 0;">Customer Portal Access</h4>
                            <p style="margin: 5px 0; color: #333;">You now have access to our customer portal where you can:</p>
                            <ul style="color: #333; margin: 10px 0;">
                                <li>View and manage your appointments</li>
                                <li>Update your contact information</li>
                                <li>Access appointment history</li>
                            </ul>
                            <p style="margin: 5px 0; color: #333;">
                                <a href="/my/appointments" style="color: #007bff; text-decoration: none;">
                                    <strong>Access Your Portal →</strong>
                                </a>
                            </p>
                        </div>

                        <p style="font-size: 16px; color: #333;">We look forward to seeing you at your appointment!</p>

                        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
                            <p style="color: #666; font-size: 14px;">
                                If you have any questions, please don't hesitate to contact us.
                            </p>
                        </div>
                    </div>
                </div>
            </field>
            <field name="auto_delete" eval="True"/>
        </record>

        <record id="email_template_appointment_rejected_aam" model="mail.template">
            <field name="name">Appointment Rejected Notification</field>
            <field name="model_id" eval="ref('calendar.model_calendar_event')"/>
            <field name="subject">❌ Appointment request update - ${object.name}</field>
            <field name="email_from">${(object.user_id.email_formatted or user.email_formatted or '<EMAIL>')|safe}</field>
            <field name="partner_to">${object.partner_id.id}</field>
            <field name="body_html" type="html">
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #f8f9fa; padding: 20px;">
                    <div style="background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                        <div style="text-align: center; margin-bottom: 30px;">
                            <div style="background-color: #dc3545; color: white; padding: 15px; border-radius: 50px; display: inline-block; margin-bottom: 20px;">
                                <span style="font-size: 24px;">❌</span>
                            </div>
                            <h1 style="color: #dc3545; margin: 0;">Appointment Request Update</h1>
                        </div>

                        <p style="font-size: 16px; color: #333;">Dear ${object.partner_id.name},</p>

                        <p style="font-size: 16px; color: #333;">
                            We regret to inform you that your appointment request could not be approved at this time.
                        </p>

                        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0;">
                            <h3 style="color: #333; margin-top: 0;">Requested Appointment:</h3>
                            <p style="margin: 5px 0;"><strong>Service:</strong> ${object.name}</p>
                            <p style="margin: 5px 0;"><strong>Requested Date &amp; Time:</strong> <t t-out="format_datetime(object.start, tz=object.partner_id.tz or 'UTC', dt_format='EEEE, MMMM d, yyyy \'at\' h:mm a')"/></p>
                            <p style="margin: 5px 0; color: #dc3545;"><strong>Status:</strong> Not Approved</p>
                        </div>

                        <div style="background-color: #fff3cd; padding: 15px; border-radius: 6px; border-left: 4px solid #ffc107; margin: 20px 0;">
                            <h4 style="color: #856404; margin-top: 0;">What you can do next:</h4>
                            <ul style="color: #333; margin: 10px 0;">
                                <li>Try booking a different time slot</li>
                                <li>Contact us directly to discuss alternative options</li>
                                <li>Check our availability for other dates</li>
                            </ul>
                        </div>

                        <div style="text-align: center; margin: 20px 0;">
                            <a href="/appointment" style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
                                Book Another Appointment
                            </a>
                        </div>

                        <p style="font-size: 16px; color: #333;">
                            We apologize for any inconvenience and appreciate your understanding.
                        </p>

                        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
                            <p style="color: #666; font-size: 14px;">
                                If you have any questions or need assistance, please don't hesitate to contact us.
                            </p>
                        </div>
                    </div>
                </div>
            </field>
            <field name="auto_delete" eval="True"/>
        </record>

        <record id="email_template_appointment_forfeited_aam" model="mail.template">
            <field name="name">Appointment Forfeited Notification</field>
            <field name="model_id" eval="ref('calendar.model_calendar_event')"/>
            <field name="subject">[Forfeited] Your appointment for ${object.name}</field>
            <field name="email_from">${(object.user_id.email_formatted or user.email_formatted or '<EMAIL>')|safe}</field>
            <field name="partner_to">${object.partner_id.id}</field>
            <field name="body_html" type="html">
                <div style="font-family: sans-serif; font-size: 14px;">
                    <p>Dear ${object.partner_id.name},</p>
                    <p>
                        This is to inform you that your appointment for <strong>${object.name}</strong> 
                        on <strong><t t-out="format_datetime(object.start, tz=object.partner_id.tz or 'UTC')"/></strong>
                        has been <strong style="color:orange;">FORFEITED</strong> due to a failure to check in by the required time.
                    </p>
                    <p>Please contact us if you wish to re-book.</p>
                </div>
            </field>
            <field name="auto_delete" eval="True"/>
        </record>
    </data>
</odoo>